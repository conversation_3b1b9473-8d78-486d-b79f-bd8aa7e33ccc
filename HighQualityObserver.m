function x_hat_new = HighQualityObserver(agent_id, x_agents, x_hat_agents, neighbors, params, dt, t)
% 高质量分布式观测器
% 专门设计用于保持控制性能的高精度状态估计
% 输入参数:
%   agent_id - 当前无人机ID
%   x_agents - 所有无人机真实状态矩阵 (12 x N) [仅用于生成测量]
%   x_hat_agents - 所有无人机状态估计矩阵 (12 x N)
%   neighbors - 邻接矩阵，表示通信拓扑 (N x N)
%   params - 观测器参数结构体
%   dt - 时间步长
%   t - 当前时间
% 输出:
%   x_hat_new - 更新后的状态估计 (12x1)

% 获取当前状态估计
x_hat_i = x_hat_agents(:, agent_id);

% 生成高质量测量 (模拟高精度传感器)
y_pos = x_agents(1:3, agent_id) + params.noise_pos * randn(3, 1);  % 位置测量
y_att = x_agents(7:9, agent_id) + params.noise_att * randn(3, 1);  % 姿态测量

%% 高质量局部观测器更新
% 位置估计校正 (高增益，快速收敛)
pos_error = y_pos - x_hat_i(1:3);
x_hat_i(1:3) = x_hat_i(1:3) + params.alpha_pos * pos_error;

% 速度估计 (基于位置变化和动态模型)
persistent prev_pos_hq prev_time_hq
if isempty(prev_pos_hq)
    prev_pos_hq = zeros(3, 10);  % 支持最多10个无人机
    prev_time_hq = zeros(1, 10);
end

if agent_id <= size(prev_pos_hq, 2)
    if prev_time_hq(agent_id) > 0
        % 基于位置差分的速度估计
        vel_diff = (y_pos - prev_pos_hq(:, agent_id)) / dt;
        
        % 结合当前速度估计和差分速度
        vel_error = vel_diff - x_hat_i(4:6);
        x_hat_i(4:6) = x_hat_i(4:6) + params.alpha_vel * vel_error;
    end
    prev_pos_hq(:, agent_id) = y_pos;
    prev_time_hq(agent_id) = prev_time_hq(agent_id) + dt;
end

% 姿态估计校正
att_error = y_att - x_hat_i(7:9);
x_hat_i(7:9) = x_hat_i(7:9) + params.alpha_att * att_error;

% 角速度估计 (基于姿态变化)
persistent prev_att_hq
if isempty(prev_att_hq)
    prev_att_hq = zeros(3, 10);
end

if agent_id <= size(prev_att_hq, 2)
    if prev_time_hq(agent_id) > dt
        % 基于姿态差分的角速度估计
        omega_diff = (y_att - prev_att_hq(:, agent_id)) / dt;
        
        % 结合当前角速度估计和差分角速度
        omega_error = omega_diff - x_hat_i(10:12);
        x_hat_i(10:12) = x_hat_i(10:12) + params.alpha_omega * omega_error;
    end
    prev_att_hq(:, agent_id) = y_att;
end

%% 高质量一致性更新 (仅在稳定后应用)
if t > 10.0  % 10秒后才开始一致性更新，确保局部估计已稳定
    N = size(x_hat_agents, 2);
    consensus_term = zeros(12, 1);
    neighbor_count = 0;
    
    % 计算与邻居的一致性误差 (仅针对位置和速度)
    for j = 1:N
        if neighbors(agent_id, j) == 1 && j ~= agent_id
            % 位置一致性 (权重较小)
            pos_consensus = x_hat_agents(1:3, j) - x_hat_i(1:3);
            consensus_term(1:3) = consensus_term(1:3) + pos_consensus;
            
            % 速度一致性 (权重较小)
            vel_consensus = x_hat_agents(4:6, j) - x_hat_i(4:6);
            consensus_term(4:6) = consensus_term(4:6) + vel_consensus;
            
            neighbor_count = neighbor_count + 1;
        end
    end
    
    % 应用一致性校正 (非常保守，避免破坏局部估计)
    if neighbor_count > 0
        consensus_term = consensus_term / neighbor_count;
        
        % 时间自适应一致性增益 (随时间减小)
        time_factor = max(0.1, 1.0 - (t - 10.0) / 30.0);  % 40秒后降到最小
        adaptive_beta = params.beta_consensus * time_factor;
        
        % 只对位置和速度应用一致性，姿态保持局部估计
        x_hat_i(1:6) = x_hat_i(1:6) + adaptive_beta * consensus_term(1:6);
    end
end

%% 高级数值稳定性保护
% 自适应限制 (基于测量质量)
measurement_quality = 1.0 / (1.0 + norm(pos_error) + norm(att_error));

% 位置限制 (自适应)
max_pos = 30 * measurement_quality + 20;
x_hat_i(1:3) = max(-max_pos, min(max_pos, x_hat_i(1:3)));

% 速度限制 (自适应)
max_vel = 10 * measurement_quality + 5;
x_hat_i(4:6) = max(-max_vel, min(max_vel, x_hat_i(4:6)));

% 姿态限制
max_att = pi/6;  % 30度限制
x_hat_i(7:9) = max(-max_att, min(max_att, x_hat_i(7:9)));

% 角速度限制
max_omega = 2;
x_hat_i(10:12) = max(-max_omega, min(max_omega, x_hat_i(10:12)));

%% 估计质量监控和自适应重置
persistent estimation_quality_history
if isempty(estimation_quality_history)
    estimation_quality_history = zeros(10, 10);  % 10个无人机，10个历史点
end

if agent_id <= size(estimation_quality_history, 1)
    % 计算当前估计质量
    current_error = norm(x_hat_i - x_agents(:, agent_id));
    
    % 更新质量历史
    estimation_quality_history(agent_id, :) = [estimation_quality_history(agent_id, 2:end), current_error];
    
    % 如果估计质量持续恶化，进行软重置
    if t > 15.0  % 15秒后才开始监控
        avg_error = mean(estimation_quality_history(agent_id, :));
        if avg_error > 3.0  % 如果平均误差超过3米
            % 软重置：向测量值靠拢
            reset_factor = 0.1;
            x_hat_i(1:3) = (1 - reset_factor) * x_hat_i(1:3) + reset_factor * y_pos;
            x_hat_i(7:9) = (1 - reset_factor) * x_hat_i(7:9) + reset_factor * y_att;
            
            % 重置质量历史
            estimation_quality_history(agent_id, :) = avg_error * 0.5;
        end
    end
end

%% 输出更新后的状态估计
x_hat_new = x_hat_i;

%% 调试信息 (可选)
persistent debug_counter_hq
if isempty(debug_counter_hq)
    debug_counter_hq = zeros(1, 10);
end

if agent_id <= length(debug_counter_hq)
    debug_counter_hq(agent_id) = debug_counter_hq(agent_id) + 1;
    
    % 每5秒输出一次调试信息
    if mod(debug_counter_hq(agent_id), 500) == 0
        pos_est_error = norm(x_hat_i(1:3) - x_agents(1:3, agent_id));
        vel_est_error = norm(x_hat_i(4:6) - x_agents(4:6, agent_id));
        
        % 计算邻居数量
        neighbor_count_debug = sum(neighbors(agent_id, :)) - (neighbors(agent_id, agent_id) > 0);
        
        if agent_id == 1
            uav_type = '领导者';
        else
            uav_type = sprintf('跟随者%d', agent_id-1);
        end
        
        % 判断当前使用的状态类型
        if t < 5.0
            state_type = '真实状态';
        elseif t < params.transition_time
            transition_ratio = (t - 5.0) / (params.transition_time - 5.0);
            state_type = sprintf('混合状态(%.0f%%估计)', transition_ratio * 100);
        else
            state_type = '估计状态';
        end
        
        fprintf('高质量观测器[%s] [%.1fs] - 位置估计误差: %.4f m, 速度估计误差: %.4f m/s, 邻居数: %d, 状态: %s\n', ...
                uav_type, debug_counter_hq(agent_id)*0.01, pos_est_error, vel_est_error, neighbor_count_debug, state_type);
    end
end

end
