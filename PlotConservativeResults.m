function PlotConservativeResults(t_span, x_history, x_hat_history, x_desired, x_virtual_history, ...
                                 estimation_error, u_real_history, u_estimated_history, ...
                                 pos_errors_real, vel_errors_real, pos_errors_estimated, vel_errors_estimated, ...
                                 formation_offset, neighbors, observer_quality)
% 保守稳定混合架构系统结果可视化
% 重点展示精确的控制性能和稳定的观测器效果

%% 设置中文字体
try
    set(0, 'DefaultAxesFontName', 'Microsoft YaHei');
    set(0, 'DefaultTextFontName', 'Microsoft YaHei');
catch
    % 如果设置失败，使用默认字体
end

N_total = size(x_history, 2);
colors = {'m', 'r', 'g', 'b', 'c'};
line_styles = {'-', '--', '-.', ':', '-'};

%% 图1: 精确性能分析
figure('Name', '保守稳定混合架构 - 精确性能分析', 'Position', [100, 100, 1400, 800]);

% 子图1: 真实状态控制的跟踪误差
subplot(2, 3, 1);
hold on; grid on;
for i = 1:N_total
    if i == 1
        plot(t_span, pos_errors_real(i, :), 'Color', colors{i}, 'LineWidth', 2.5, 'LineStyle', line_styles{i});
    else
        plot(t_span, pos_errors_real(i, :), 'Color', colors{i}, 'LineWidth', 1.8, 'LineStyle', line_styles{i});
    end
end
xlabel('时间 (s)', 'FontSize', 12);
ylabel('位置跟踪误差 (m)', 'FontSize', 12);
title('主控制系统 - 基于真实状态（精确性能）', 'FontSize', 14, 'FontWeight', 'bold');
legend_labels = cell(N_total, 1);
for i = 1:N_total
    if i == 1
        legend_labels{i} = '领导者（精确状态）';
    else
        legend_labels{i} = sprintf('跟随者%d', i-1);
    end
end
legend(legend_labels, 'Location', 'best', 'FontSize', 10);

% 添加性能要求线
yline(0.5, 'r--', '目标: 0.5m', 'LineWidth', 2, 'FontSize', 10);
ylim([0, max(1.5, max(pos_errors_real(:))*1.1)]);

% 子图2: 速度跟踪误差
subplot(2, 3, 2);
hold on; grid on;
for i = 1:N_total
    if i == 1
        plot(t_span, vel_errors_real(i, :), 'Color', colors{i}, 'LineWidth', 2.5, 'LineStyle', line_styles{i});
    else
        plot(t_span, vel_errors_real(i, :), 'Color', colors{i}, 'LineWidth', 1.8, 'LineStyle', line_styles{i});
    end
end
xlabel('时间 (s)', 'FontSize', 12);
ylabel('速度跟踪误差 (m/s)', 'FontSize', 12);
title('主控制系统 - 速度跟踪性能', 'FontSize', 14, 'FontWeight', 'bold');
legend(legend_labels, 'Location', 'best', 'FontSize', 10);

% 添加性能要求线
yline(1.0, 'r--', '目标: 1.0m/s', 'LineWidth', 2, 'FontSize', 10);
ylim([0, max(2.0, max(vel_errors_real(:))*1.1)]);

% 子图3: 15秒后性能统计
subplot(2, 3, 3);
steady_start_idx = find(t_span >= 15, 1);
if ~isempty(steady_start_idx)
    steady_pos_real = pos_errors_real(:, steady_start_idx:end);
    steady_vel_real = vel_errors_real(:, steady_start_idx:end);
    
    performance_stats = [
        mean(steady_pos_real(:));
        max(steady_pos_real(:));
        mean(steady_vel_real(:));
        max(steady_vel_real(:))
    ];
    
    bar_colors = [0.2, 0.8, 0.2; 0.8, 0.6, 0.2; 0.2, 0.6, 0.8; 0.8, 0.2, 0.2];
    bar_handle = bar(performance_stats);
    bar_handle.FaceColor = 'flat';
    bar_handle.CData = bar_colors;
    
    set(gca, 'XTickLabel', {'位置-平均', '位置-最大', '速度-平均', '速度-最大'});
    ylabel('跟踪误差', 'FontSize', 12);
    title('15秒后稳态性能统计', 'FontSize', 14, 'FontWeight', 'bold');
    grid on;
    
    % 添加数值标签
    for i = 1:length(performance_stats)
        text(i, performance_stats(i) + max(performance_stats)*0.02, ...
             sprintf('%.3f', performance_stats(i)), 'HorizontalAlignment', 'center', 'FontSize', 10);
    end
    
    % 添加目标线
    yline(0.5, 'r--', 'LineWidth', 1.5);
    yline(1.0, 'b--', 'LineWidth', 1.5);
end

% 子图4: 估计状态控制对比
subplot(2, 3, 4);
hold on; grid on;
for i = 1:N_total
    plot(t_span, pos_errors_real(i, :), 'Color', colors{i}, 'LineWidth', 1.5, 'LineStyle', '-');
    plot(t_span, pos_errors_estimated(i, :), 'Color', colors{i}, 'LineWidth', 1.5, 'LineStyle', '--');
end
xlabel('时间 (s)', 'FontSize', 12);
ylabel('位置跟踪误差 (m)', 'FontSize', 12);
title('控制性能对比（实线=真实，虚线=估计）', 'FontSize', 14, 'FontWeight', 'bold');
legend_combined = cell(N_total*2, 1);
for i = 1:N_total
    if i == 1
        legend_combined{2*i-1} = '领导者-真实';
        legend_combined{2*i} = '领导者-估计';
    else
        legend_combined{2*i-1} = sprintf('跟随者%d-真实', i-1);
        legend_combined{2*i} = sprintf('跟随者%d-估计', i-1);
    end
end
legend(legend_combined, 'Location', 'best', 'FontSize', 8);

% 子图5: 观测器质量监控
subplot(2, 3, 5);
hold on; grid on;
for i = 2:N_total  % 只显示跟随者的观测器质量
    plot(t_span, observer_quality(i, :), 'Color', colors{i}, 'LineWidth', 2, 'LineStyle', line_styles{i});
end
xlabel('时间 (s)', 'FontSize', 12);
ylabel('观测器质量指标', 'FontSize', 12);
title('保守分布式观测器质量监控', 'FontSize', 14, 'FontWeight', 'bold');
ylim([0, 1.1]);
quality_legend = cell(N_total-1, 1);
for i = 2:N_total
    quality_legend{i-1} = sprintf('跟随者%d', i-1);
end
legend(quality_legend, 'Location', 'best', 'FontSize', 10);

% 添加质量阈值线
yline(0.8, 'g--', '优秀质量', 'LineWidth', 1.5, 'FontSize', 10);
yline(0.5, 'y--', '可接受质量', 'LineWidth', 1.5, 'FontSize', 10);

% 子图6: 性能达成情况
subplot(2, 3, 6);
if ~isempty(steady_start_idx)
    % 检查性能达成情况
    pos_target_achieved = mean(steady_pos_real(:)) < 0.5;
    vel_target_achieved = mean(steady_vel_real(:)) < 1.0;
    
    achievement_data = [
        mean(steady_pos_real(:)) / 0.5 * 100;  % 位置目标达成百分比
        mean(steady_vel_real(:)) / 1.0 * 100   % 速度目标达成百分比
    ];
    
    bar_handle2 = bar(achievement_data);
    if pos_target_achieved && vel_target_achieved
        bar_handle2.FaceColor = [0.2, 0.8, 0.2];  % 绿色表示成功
    elseif pos_target_achieved || vel_target_achieved
        bar_handle2.FaceColor = [0.8, 0.8, 0.2];  % 黄色表示部分成功
    else
        bar_handle2.FaceColor = [0.8, 0.2, 0.2];  % 红色表示失败
    end
    
    set(gca, 'XTickLabel', {'位置目标', '速度目标'});
    ylabel('目标达成率 (%)', 'FontSize', 12);
    title('性能目标达成情况', 'FontSize', 14, 'FontWeight', 'bold');
    grid on;
    
    % 添加100%目标线
    yline(100, 'r--', '100%目标', 'LineWidth', 2, 'FontSize', 10);
    
    % 添加数值标签
    for i = 1:length(achievement_data)
        text(i, achievement_data(i) + max(achievement_data)*0.02, ...
             sprintf('%.1f%%', achievement_data(i)), 'HorizontalAlignment', 'center', 'FontSize', 10);
    end
    
    % 添加达成状态文本
    if pos_target_achieved && vel_target_achieved
        text(0.5, 0.9, '✅ 全部目标达成！', 'Units', 'normalized', 'FontSize', 12, ...
             'FontWeight', 'bold', 'Color', 'green', 'HorizontalAlignment', 'center');
    elseif pos_target_achieved
        text(0.5, 0.9, '⚠️ 位置目标达成', 'Units', 'normalized', 'FontSize', 12, ...
             'FontWeight', 'bold', 'Color', 'orange', 'HorizontalAlignment', 'center');
    elseif vel_target_achieved
        text(0.5, 0.9, '⚠️ 速度目标达成', 'Units', 'normalized', 'FontSize', 12, ...
             'FontWeight', 'bold', 'Color', 'orange', 'HorizontalAlignment', 'center');
    else
        text(0.5, 0.9, '❌ 目标未达成', 'Units', 'normalized', 'FontSize', 12, ...
             'FontWeight', 'bold', 'Color', 'red', 'HorizontalAlignment', 'center');
    end
end

sgtitle('保守稳定混合架构系统 - 精确性能分析', 'FontSize', 16, 'FontWeight', 'bold');

%% 图2: 观测器详细分析
figure('Name', '保守分布式观测器 - 详细分析', 'Position', [150, 150, 1400, 800]);

% 子图1: 位置估计误差
subplot(2, 3, 1);
hold on; grid on;
for i = 1:N_total
    pos_est_error = squeeze(sqrt(sum(estimation_error(1:3, i, :).^2, 1)));
    if i == 1
        plot(t_span, pos_est_error, 'Color', colors{i}, 'LineWidth', 2.5, 'LineStyle', line_styles{i});
    else
        plot(t_span, pos_est_error, 'Color', colors{i}, 'LineWidth', 1.8, 'LineStyle', line_styles{i});
    end
end
xlabel('时间 (s)', 'FontSize', 12);
ylabel('位置估计误差 (m)', 'FontSize', 12);
title('保守分布式观测器 - 位置估计误差', 'FontSize', 14, 'FontWeight', 'bold');
legend(legend_labels, 'Location', 'best', 'FontSize', 10);
ylim([0, max(0.5, max(sqrt(sum(estimation_error(1:3, :, :).^2, 1)), [], 'all')*1.1)]);

% 子图2: 速度估计误差
subplot(2, 3, 2);
hold on; grid on;
for i = 1:N_total
    vel_est_error = squeeze(sqrt(sum(estimation_error(4:6, i, :).^2, 1)));
    if i == 1
        plot(t_span, vel_est_error, 'Color', colors{i}, 'LineWidth', 2.5, 'LineStyle', line_styles{i});
    else
        plot(t_span, vel_est_error, 'Color', colors{i}, 'LineWidth', 1.8, 'LineStyle', line_styles{i});
    end
end
xlabel('时间 (s)', 'FontSize', 12);
ylabel('速度估计误差 (m/s)', 'FontSize', 12);
title('保守分布式观测器 - 速度估计误差', 'FontSize', 14, 'FontWeight', 'bold');
legend(legend_labels, 'Location', 'best', 'FontSize', 10);
ylim([0, max(0.3, max(sqrt(sum(estimation_error(4:6, :, :).^2, 1)), [], 'all')*1.1)]);

% 子图3: 通信拓扑
subplot(2, 3, 3);
% 绘制通信拓扑图
theta = linspace(0, 2*pi, N_total+1);
theta = theta(1:end-1);
radius = 1;
pos_x = radius * cos(theta);
pos_y = radius * sin(theta);

hold on;
% 绘制连接线
for i = 1:N_total
    for j = 1:N_total
        if neighbors(i, j) == 1
            plot([pos_x(i), pos_x(j)], [pos_y(i), pos_y(j)], 'k-', 'LineWidth', 1.5);
        end
    end
end

% 绘制节点
for i = 1:N_total
    if i == 1
        scatter(pos_x(i), pos_y(i), 200, colors{i}, 'filled', 'MarkerEdgeColor', 'k', 'LineWidth', 2);
        text(pos_x(i), pos_y(i), '领导者', 'HorizontalAlignment', 'center', 'FontSize', 10, 'FontWeight', 'bold');
    else
        scatter(pos_x(i), pos_y(i), 150, colors{i}, 'filled', 'MarkerEdgeColor', 'k', 'LineWidth', 1.5);
        text(pos_x(i), pos_y(i), sprintf('跟随者%d', i-1), 'HorizontalAlignment', 'center', 'FontSize', 9);
    end
end

axis equal; grid on;
title('保守分布式通信拓扑', 'FontSize', 14, 'FontWeight', 'bold');
xlabel('相对位置 X', 'FontSize', 12);
ylabel('相对位置 Y', 'FontSize', 12);

% 子图4: 观测器性能统计
subplot(2, 3, 4);
observer_stats = zeros(N_total, 3);  % 最大误差、平均误差、平均质量
for i = 1:N_total
    est_error_norm = squeeze(sqrt(sum(estimation_error(:, i, :).^2, 1)));
    observer_stats(i, 1) = max(est_error_norm);
    observer_stats(i, 2) = mean(est_error_norm);
    observer_stats(i, 3) = mean(observer_quality(i, :));
end

bar_handle3 = bar(observer_stats(:, 1:2), 'grouped');
bar_handle3(1).FaceColor = [0.8, 0.2, 0.2];
bar_handle3(2).FaceColor = [0.2, 0.8, 0.2];

ylabel('状态估计误差 (m)', 'FontSize', 12);
title('保守分布式观测器性能统计', 'FontSize', 14, 'FontWeight', 'bold');
legend({'最大估计误差', '平均估计误差'}, 'Location', 'best', 'FontSize', 10);
grid on;

uav_labels = cell(N_total, 1);
for i = 1:N_total
    if i == 1
        uav_labels{i} = '领导者';
    else
        uav_labels{i} = sprintf('跟随者%d', i-1);
    end
end
set(gca, 'XTickLabel', uav_labels);

% 添加数值标签
for i = 1:N_total
    for j = 1:2
        text(i + (j-1.5)*0.15, observer_stats(i,j) + max(observer_stats(:,1:2), [], 'all')*0.02, ...
             sprintf('%.3f', observer_stats(i,j)), 'HorizontalAlignment', 'center', 'FontSize', 9);
    end
end

% 子图5: 15-25秒窗口放大分析
subplot(2, 3, 5);
zoom_start = find(t_span >= 15, 1);
zoom_end = find(t_span <= 25, 1, 'last');
if ~isempty(zoom_start) && ~isempty(zoom_end)
    t_zoom = t_span(zoom_start:zoom_end);
    hold on; grid on;
    for i = 1:N_total
        plot(t_zoom, pos_errors_real(i, zoom_start:zoom_end), 'Color', colors{i}, 'LineWidth', 2, 'LineStyle', '-');
        plot(t_zoom, pos_errors_estimated(i, zoom_start:zoom_end), 'Color', colors{i}, 'LineWidth', 2, 'LineStyle', '--');
    end
    xlabel('时间 (s)', 'FontSize', 12);
    ylabel('位置跟踪误差 (m)', 'FontSize', 12);
    title('15-25秒稳态窗口分析', 'FontSize', 14, 'FontWeight', 'bold');
    
    % 添加目标线
    yline(0.5, 'r--', '目标: 0.5m', 'LineWidth', 1.5, 'FontSize', 10);
    
    legend_zoom = cell(N_total*2, 1);
    for i = 1:N_total
        if i == 1
            legend_zoom{2*i-1} = '领导者-真实';
            legend_zoom{2*i} = '领导者-估计';
        else
            legend_zoom{2*i-1} = sprintf('跟随者%d-真实', i-1);
            legend_zoom{2*i} = sprintf('跟随者%d-估计', i-1);
        end
    end
    legend(legend_zoom, 'Location', 'best', 'FontSize', 8);
end

% 子图6: 系统稳定性评估
subplot(2, 3, 6);
% 计算系统稳定性指标
stability_metrics = zeros(4, 1);
if ~isempty(steady_start_idx)
    steady_pos_real = pos_errors_real(:, steady_start_idx:end);
    steady_vel_real = vel_errors_real(:, steady_start_idx:end);
    steady_pos_est = pos_errors_estimated(:, steady_start_idx:end);
    steady_vel_est = vel_errors_estimated(:, steady_start_idx:end);
    
    stability_metrics(1) = std(steady_pos_real(:));  % 位置稳定性
    stability_metrics(2) = std(steady_vel_real(:));  % 速度稳定性
    stability_metrics(3) = std(steady_pos_est(:));   % 估计位置稳定性
    stability_metrics(4) = std(steady_vel_est(:));   % 估计速度稳定性
end

bar_handle4 = bar(stability_metrics);
bar_handle4.FaceColor = [0.2, 0.6, 0.8];

set(gca, 'XTickLabel', {'真实-位置', '真实-速度', '估计-位置', '估计-速度'});
ylabel('标准差 (稳定性指标)', 'FontSize', 12);
title('系统稳定性评估', 'FontSize', 14, 'FontWeight', 'bold');
grid on;

% 添加数值标签
for i = 1:length(stability_metrics)
    text(i, stability_metrics(i) + max(stability_metrics)*0.02, ...
         sprintf('%.3f', stability_metrics(i)), 'HorizontalAlignment', 'center', 'FontSize', 10);
end

sgtitle('保守分布式观测器 - 详细性能分析', 'FontSize', 16, 'FontWeight', 'bold');

%% 图3: 3D轨迹展示
figure('Name', '保守稳定混合架构 - 3D轨迹展示', 'Position', [200, 200, 1200, 600]);

% 子图1: 真实系统3D轨迹
subplot(1, 2, 1);
hold on; grid on;
for i = 1:N_total
    x_traj = squeeze(x_history(1, i, :));
    y_traj = squeeze(x_history(2, i, :));
    z_traj = squeeze(x_history(3, i, :));
    plot3(x_traj, y_traj, z_traj, 'Color', colors{i}, 'LineWidth', 2, 'LineStyle', line_styles{i});
end
xlabel('X (m)', 'FontSize', 12); ylabel('Y (m)', 'FontSize', 12); zlabel('Z (m)', 'FontSize', 12);
title('真实系统 - 3D飞行轨迹', 'FontSize', 14, 'FontWeight', 'bold');
legend(legend_labels, 'Location', 'best', 'FontSize', 10);
view(45, 30);

% 子图2: 编队形成展示
subplot(1, 2, 2);
time_snapshots = [5, 15, 25, 35, 50];
snapshot_indices = arrayfun(@(t) find(t_span >= t, 1), time_snapshots);

hold on; grid on;
for snap_idx = 1:length(time_snapshots)
    k_snap = snapshot_indices(snap_idx);
    if k_snap <= size(x_history, 3)
        % 真实系统编队
        for i = 1:N_total
            x_pos = x_history(1, i, k_snap);
            y_pos = x_history(2, i, k_snap);
            if i == 1
                scatter(x_pos, y_pos, 120, colors{i}, 'filled', 'o', 'MarkerEdgeColor', 'k', 'LineWidth', 1.5);
            else
                scatter(x_pos, y_pos, 80, colors{i}, 'filled', 's', 'MarkerEdgeColor', 'k', 'LineWidth', 1);
            end
        end
        
        % 连接编队
        if snap_idx == length(time_snapshots)  % 只在最后一个时刻显示连接
            leader_pos = [x_history(1, 1, k_snap), x_history(2, 1, k_snap)];
            for i = 2:N_total
                follower_pos = [x_history(1, i, k_snap), x_history(2, i, k_snap)];
                plot([leader_pos(1), follower_pos(1)], [leader_pos(2), follower_pos(2)], 'k--', 'LineWidth', 1);
            end
        end
    end
end

xlabel('X (m)', 'FontSize', 12);
ylabel('Y (m)', 'FontSize', 12);
title('编队形成过程展示', 'FontSize', 14, 'FontWeight', 'bold');
axis equal; grid on;

% 添加时间标签
for snap_idx = 1:length(time_snapshots)
    k_snap = snapshot_indices(snap_idx);
    if k_snap <= size(x_history, 3)
        x_center = mean(x_history(1, :, k_snap));
        y_center = mean(x_history(2, :, k_snap));
        text(x_center, y_center + 0.3, sprintf('t=%ds', time_snapshots(snap_idx)), ...
             'HorizontalAlignment', 'center', 'FontSize', 9, 'FontWeight', 'bold');
    end
end

sgtitle('保守稳定混合架构系统 - 3D轨迹与编队展示', 'FontSize', 16, 'FontWeight', 'bold');

fprintf('🎯 保守稳定混合架构系统可视化完成！\n');
fprintf('✅ 生成了3个主要图表组\n');
fprintf('✅ 精确性能分析与目标达成评估\n');
fprintf('✅ 保守分布式观测器详细分析\n');
fprintf('✅ 稳定3D轨迹与编队形成展示\n');
fprintf('✅ 系统稳定性评估\n');
fprintf('✅ 完整中文可视化显示\n');

end
