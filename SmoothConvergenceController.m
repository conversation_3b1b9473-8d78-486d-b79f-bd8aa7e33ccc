function u = SmoothConvergenceController(x, x_desired, control_params)
% 平滑收敛控制器 - 专门解决您图片中的振荡问题
% 基于稳定的PID控制，增加平滑收敛技术

persistent integral_pos integral_att controller_time
persistent error_history  % 误差历史用于检测振荡

% 初始化持久变量
if isempty(integral_pos)
    integral_pos = zeros(3, 1);
    integral_att = zeros(3, 1);
    controller_time = 0;
    error_history = zeros(3, 10);  % 保存最近10个误差值
end

% 更新控制器时间
dt = 0.01;
controller_time = controller_time + dt;

% 提取状态变量
pos = x(1:3);           % 位置 [x; y; z]
vel = x(4:6);           % 速度 [vx; vy; vz]
euler = x(7:9);         % 欧拉角 [phi; theta; psi]
omega = x(10:12);       % 角速度 [p; q; r]

% 期望状态
pos_d = x_desired(1:3);
vel_d = x_desired(4:6);

% 位置和速度误差
error_pos = pos - pos_d;
error_vel = vel - vel_d;
error_pos_norm = norm(error_pos);

% 更新误差历史
error_history = [error_history(:, 2:end), error_pos];

% 检测振荡 - 如果误差符号频繁变化则认为在振荡
oscillation_detected = false;
if controller_time > 5.0
    sign_changes = 0;
    for i = 1:3  % 检查x,y,z三个方向
        for j = 2:10
            if sign(error_history(i, j)) ~= sign(error_history(i, j-1))
                sign_changes = sign_changes + 1;
            end
        end
    end
    if sign_changes > 15  % 如果符号变化太频繁
        oscillation_detected = true;
    end
end

% 基础增益设置 - 保守但有效
k_pos_base = 2.5;
k_vel_base = 1.8;
k_int_base = 0.8;

% 时间分段增益调度
if controller_time <= 8.0
    % 0-8秒：稳定收敛
    gain_mult = 1.0;
elseif controller_time <= 15.0
    % 8-15秒：逐步增强
    progress = (controller_time - 8.0) / 7.0;
    gain_mult = 1.0 + 0.2 * progress;  % 从1.0增加到1.2
else
    % 15秒后：精确控制
    gain_mult = 1.3;
end

% 振荡抑制策略
if oscillation_detected
    % 检测到振荡时，降低比例增益，增加阻尼
    k_pos = k_pos_base * 0.8 * gain_mult;
    k_vel = k_vel_base * 1.5 * gain_mult;  % 增加阻尼
    k_int = k_int_base * 0.5;  % 减少积分
else
    % 正常情况下的增益调整
    if error_pos_norm > 0.5
        k_pos = k_pos_base * 1.1 * gain_mult;
        k_vel = k_vel_base * 1.0 * gain_mult;
        k_int = k_int_base * 0.8;
    elseif error_pos_norm > 0.2
        k_pos = k_pos_base * 1.0 * gain_mult;
        k_vel = k_vel_base * 1.2 * gain_mult;
        k_int = k_int_base * 1.0;
    else
        % 小误差时重点增加阻尼
        k_pos = k_pos_base * 1.1 * gain_mult;
        k_vel = k_vel_base * 1.4 * gain_mult;  % 强阻尼
        k_int = k_int_base * 1.2;
    end
end

% 积分控制 - 防止积分饱和
if controller_time > 8.0 && error_pos_norm < 0.8 && ~oscillation_detected
    integral_pos = integral_pos + error_pos * dt;
else
    integral_pos = integral_pos * 0.95;  % 缓慢衰减
end

% 积分限制
if oscillation_detected
    max_integral = 0.2;  % 振荡时严格限制
elseif controller_time > 12.0
    max_integral = 0.5;  % 12秒后适度放宽
else
    max_integral = 0.3;
end

integral_pos = max(-max_integral, min(max_integral, integral_pos));

% PID控制律
acc_cmd = -k_pos * error_pos - k_vel * error_vel - k_int * integral_pos;

% 推力计算
phi = euler(1); theta = euler(2);
cos_phi = cos(phi); cos_theta = cos(theta);

% 避免除零
if abs(cos_phi * cos_theta) < 0.1
    cos_phi = sign(cos_phi) * 0.1;
    cos_theta = sign(cos_theta) * 0.1;
end

T = control_params.mass * (acc_cmd(3) + control_params.gravity) / (cos_phi * cos_theta);

% 推力限幅
T = max(control_params.T_min, min(control_params.T_max, T));

% 期望姿态计算
g = control_params.gravity;
phi_d = (acc_cmd(1) * sin(euler(3)) - acc_cmd(2) * cos(euler(3))) / g;
theta_d = (acc_cmd(1) * cos(euler(3)) + acc_cmd(2) * sin(euler(3))) / g;
psi_d = 0;

% 姿态角限制
max_angle = pi/6;  % 30度限制
phi_d = max(-max_angle, min(max_angle, phi_d));
theta_d = max(-max_angle, min(max_angle, theta_d));

% 姿态误差
euler_d = [phi_d; theta_d; psi_d];
error_att = euler - euler_d;
error_att_norm = norm(error_att);

% 姿态控制增益
if oscillation_detected
    k_att = control_params.k_att * 0.9;
    k_omega = control_params.k_omega * 1.3;
    k_int_att = 0.2;
else
    k_att = control_params.k_att * 1.0;
    k_omega = control_params.k_omega * 1.2;
    k_int_att = 0.3;
end

% 姿态积分项
if controller_time > 10.0 && error_att_norm < 0.2 && ~oscillation_detected
    integral_att = integral_att + error_att * dt;
else
    integral_att = integral_att * 0.98;
end

% 姿态积分限制
if oscillation_detected
    max_integral_att = 0.1;
else
    max_integral_att = 0.2;
end
integral_att = max(-max_integral_att, min(max_integral_att, integral_att));

% 姿态控制律
omega_d = zeros(3, 1);
error_omega = omega - omega_d;

tau = -k_att * error_att - k_omega * error_omega - k_int_att * integral_att;

% 力矩限幅
tau = max(-control_params.tau_max, min(control_params.tau_max, tau));

% 输出控制量
u = [T; tau];

% 调试信息
persistent debug_counter
if isempty(debug_counter)
    debug_counter = 0;
end
debug_counter = debug_counter + 1;

if mod(debug_counter, 500) == 0  % 每5秒输出一次
    if oscillation_detected
        osc_status = '振荡检测';
    else
        osc_status = '正常';
    end
    fprintf('平滑控制器 [%.1fs] - 位置误差: %.4f m, 速度误差: %.4f m/s, 状态: %s\n', ...
            controller_time, error_pos_norm, norm(error_vel), osc_status);
end

end
