function LeaderFollowerFormation()
% 领导者-跟随者无人机编队控制仿真
% 1个领导者 + 4个跟随者无人机
% 跟随者根据领导者轨迹进行编队飞行

clear; clc; close all;

fprintf('=== 领导者-跟随者无人机编队控制仿真 ===\n');
fprintf('领导者: 1架 (UAV_leader)\n');
fprintf('跟随者: 4架 (UAV_1, UAV_2, UAV_3, UAV_4)\n');
fprintf('开始仿真...\n\n');

%% 仿真参数设置
dt = 0.01;              % 仿真步长 (s)
T_sim = 50;             % 仿真时间 (s)
t_span = 0:dt:T_sim;    % 时间向量
N_followers = 4;        % 跟随者数量
N_total = N_followers + 1; % 总无人机数量

%% 无人机物理参数
quad_params = struct();
quad_params.mass = 1.2;         % 质量 (kg)
quad_params.gravity = 9.81;     % 重力加速度 (m/s^2)
quad_params.Ixx = 0.0347;       % x轴转动惯量 (kg*m^2)
quad_params.Iyy = 0.0347;       % y轴转动惯量 (kg*m^2)
quad_params.Izz = 0.0617;       % z轴转动惯量 (kg*m^2)

%% 控制参数
control_params = struct();
control_params.k_pos = 3.0;         % 位置控制增益
control_params.k_vel = 2.0;         % 速度控制增益
control_params.k_att = 1.5;         % 姿态控制增益
control_params.k_omega = 0.8;       % 角速度控制增益
control_params.mass = quad_params.mass;
control_params.gravity = quad_params.gravity;
control_params.T_max = 25;          % 最大推力 (N)
control_params.T_min = 0;           % 最小推力 (N)
control_params.tau_max = 3;         % 最大力矩 (N*m)

%% 编队参数
formation_offset = [
    -2, -2, 0;    % UAV_1: 左后方
     2, -2, 0;    % UAV_2: 右后方
    -2,  2, 0;    % UAV_3: 左前方
     2,  2, 0     % UAV_4: 右前方
]';  % 相对于领导者的位置偏移 (3x4)

%% 初始状态设置
% 状态向量: [位置(3), 速度(3), 欧拉角(3), 角速度(3)]
x_agents = zeros(12, N_total);

% 领导者初始位置
x_agents(1:3, 1) = [5; 5; 9];  % 领导者起始位置

% 跟随者初始位置 (相对于领导者)
for i = 1:N_followers
    x_agents(1:3, i+1) = x_agents(1:3, 1) + formation_offset(:, i);
end

%% 数据存储
x_history = zeros(12, N_total, length(t_span));
x_desired_history = zeros(12, N_total, length(t_span));
u_history = zeros(4, N_total, length(t_span));
tracking_error_pos = zeros(3, N_followers, length(t_span));
tracking_error_vel = zeros(3, N_followers, length(t_span));

%% 主仿真循环
fprintf('仿真进行中...\n');
for k = 1:length(t_span)
    t = t_span(k);
    
    %% 领导者轨迹生成
    [leader_pos, leader_vel, leader_acc] = GenerateLeaderTrajectory(t);
    
    % 领导者期望状态
    x_desired_leader = zeros(12, 1);
    x_desired_leader(1:3) = leader_pos;
    x_desired_leader(4:6) = leader_vel;
    
    %% 跟随者期望轨迹计算
    x_desired_followers = zeros(12, N_followers);
    for i = 1:N_followers
        % 跟随者期望位置 = 领导者位置 + 编队偏移
        x_desired_followers(1:3, i) = leader_pos + formation_offset(:, i);
        x_desired_followers(4:6, i) = leader_vel;  % 期望速度与领导者相同
    end
    
    %% 领导者控制 (轨迹跟踪)
    u_leader = LeaderController(x_agents(:, 1), x_desired_leader, control_params);
    
    %% 跟随者控制 (编队跟踪)
    u_followers = zeros(4, N_followers);
    for i = 1:N_followers
        u_followers(:, i) = FollowerController(x_agents(:, i+1), ...
                                               x_desired_followers(:, i), ...
                                               control_params);
    end
    
    %% 动力学更新
    % 领导者动力学
    x_dot_leader = QuadrotorDynamics(t, x_agents(:, 1), u_leader, quad_params);
    x_agents(:, 1) = x_agents(:, 1) + dt * x_dot_leader;
    
    % 跟随者动力学
    for i = 1:N_followers
        x_dot_follower = QuadrotorDynamics(t, x_agents(:, i+1), u_followers(:, i), quad_params);
        x_agents(:, i+1) = x_agents(:, i+1) + dt * x_dot_follower;
    end
    
    %% 跟踪误差计算
    for i = 1:N_followers
        tracking_error_pos(:, i, k) = x_agents(1:3, i+1) - x_desired_followers(1:3, i);
        tracking_error_vel(:, i, k) = x_agents(4:6, i+1) - x_desired_followers(4:6, i);
    end
    
    %% 数据存储
    x_history(:, :, k) = x_agents;
    x_desired_history(:, 1, k) = x_desired_leader;
    x_desired_history(:, 2:end, k) = x_desired_followers;
    u_history(:, 1, k) = u_leader;
    u_history(:, 2:end, k) = u_followers;
    
    % 显示进度
    if mod(k, 500) == 0
        fprintf('仿真进度: %.1f%%\n', k/length(t_span)*100);
    end
end

fprintf('仿真完成！\n');

%% 结果可视化
PlotLeaderFollowerResults(t_span, x_history, x_desired_history, ...
                          tracking_error_pos, tracking_error_vel, u_history);

end
