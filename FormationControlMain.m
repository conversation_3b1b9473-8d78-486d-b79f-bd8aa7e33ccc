function FormationControlMain()
% 分布式观测器四旋翼编队控制主程序
% 该程序实现了基于分布式观测器的多四旋翼无人机编队控制仿真

clear; clc; close all;

%% 仿真参数设置
dt = 0.01;              % 仿真步长 (s)
T_sim = 20;             % 仿真时间 (s)
t_span = 0:dt:T_sim;    % 时间向量
N_agents = 4;           % 无人机数量

%% 无人机物理参数
quad_params = struct();
quad_params.mass = 1.2;         % 质量 (kg)
quad_params.gravity = 9.81;     % 重力加速度 (m/s^2)
quad_params.Ixx = 0.0347;       % x轴转动惯量 (kg*m^2)
quad_params.Iyy = 0.0347;       % y轴转动惯量 (kg*m^2)
quad_params.Izz = 0.0617;       % z轴转动惯量 (kg*m^2)

%% 控制参数
control_params = struct();
control_params.k_pos = 2.0;         % 位置控制增益
control_params.k_vel = 1.5;         % 速度控制增益
control_params.k_att = 1.0;         % 姿态控制增益
control_params.k_omega = 0.5;       % 角速度控制增益
control_params.k_consensus = 0.8;   % 一致性增益
control_params.mass = quad_params.mass;
control_params.gravity = quad_params.gravity;
control_params.T_max = 20;          % 最大推力 (N)
control_params.T_min = 0;           % 最小推力 (N)
control_params.tau_max = 2;         % 最大力矩 (N*m)

%% 观测器参数
observer_params = struct();
% 线性化系统矩阵 (简化模型，实际中需要根据工作点线性化)
observer_params.A = zeros(12, 12);
observer_params.A(1:3, 4:6) = eye(3);  % 位置-速度关系
observer_params.A(7:9, 10:12) = eye(3); % 欧拉角-角速度关系

observer_params.B = zeros(12, 4);       % 输入矩阵 (简化)
observer_params.C = [eye(3), zeros(3, 9);    % 位置测量
                     zeros(3, 6), eye(3), zeros(3, 3)]; % 姿态测量

% 观测器增益矩阵 (通过极点配置或LQR设计)
observer_params.observer_gain = 2 * eye(12, 6);  % 简化设计
observer_params.k_consensus_obs = 0.5;  % 观测器一致性增益

%% 通信拓扑 (邻接矩阵)
% 定义无人机之间的通信连接关系
% 1表示有通信连接，0表示无连接
neighbors = [0, 1, 1, 0;    % 无人机1与2,3连接
             1, 0, 1, 1;    % 无人机2与1,3,4连接
             1, 1, 0, 1;    % 无人机3与1,2,4连接
             0, 1, 1, 0];   % 无人机4与2,3连接

%% 初始状态设置
% 真实状态初始化
x_agents = zeros(12, N_agents);
% 位置初始化 (四个角落)
x_agents(1:3, 1) = [0; 0; 0];      % 无人机1
x_agents(1:3, 2) = [2; 0; 0];      % 无人机2
x_agents(1:3, 3) = [2; 2; 0];      % 无人机3
x_agents(1:3, 4) = [0; 2; 0];      % 无人机4

% 状态估计初始化 (添加初始估计误差)
x_hat_agents = x_agents + 0.1 * randn(12, N_agents);

%% 期望编队形状 (正方形编队，边长2m，高度5m)
formation_center = [5; 5; 5];  % 编队中心
formation_size = 2;            % 编队尺寸

x_desired = zeros(12, N_agents);
% 期望位置 (正方形编队)
x_desired(1:3, 1) = formation_center + [-formation_size/2; -formation_size/2; 0];
x_desired(1:3, 2) = formation_center + [formation_size/2; -formation_size/2; 0];
x_desired(1:3, 3) = formation_center + [formation_size/2; formation_size/2; 0];
x_desired(1:3, 4) = formation_center + [-formation_size/2; formation_size/2; 0];

%% 数据存储
x_history = zeros(12, N_agents, length(t_span));
x_hat_history = zeros(12, N_agents, length(t_span));
u_history = zeros(4, N_agents, length(t_span));
estimation_error = zeros(12, N_agents, length(t_span));

%% 主仿真循环
fprintf('开始分布式观测器编队控制仿真...\n');
for k = 1:length(t_span)
    t = t_span(k);
    
    % 生成测量数据 (位置和姿态，添加测量噪声)
    y_agents = zeros(6, N_agents);
    for i = 1:N_agents
        % 位置测量 (GPS等)
        y_agents(1:3, i) = x_agents(1:3, i) + 0.01 * randn(3, 1);
        % 姿态测量 (IMU等)
        y_agents(4:6, i) = x_agents(7:9, i) + 0.005 * randn(3, 1);
    end
    
    % 分布式观测器更新
    x_hat_dot_agents = zeros(12, N_agents);
    for i = 1:N_agents
        [x_hat_dot_agents(:, i), ~] = DistributedObserver(i, x_agents, y_agents, ...
                                                          x_hat_agents, neighbors, observer_params);
    end
    
    % 更新状态估计 (欧拉积分)
    x_hat_agents = x_hat_agents + dt * x_hat_dot_agents;
    
    % 分布式控制器
    u_agents = zeros(4, N_agents);
    for i = 1:N_agents
        % 使用状态估计进行控制
        u_agents(:, i) = DistributedController(i, x_hat_agents, x_desired, neighbors, control_params);
    end
    
    % 无人机动力学更新
    x_dot_agents = zeros(12, N_agents);
    for i = 1:N_agents
        x_dot_agents(:, i) = QuadrotorDynamics(t, x_agents(:, i), u_agents(:, i), quad_params);
    end
    
    % 更新真实状态 (欧拉积分)
    x_agents = x_agents + dt * x_dot_agents;
    
    % 存储数据
    x_history(:, :, k) = x_agents;
    x_hat_history(:, :, k) = x_hat_agents;
    u_history(:, :, k) = u_agents;
    estimation_error(:, :, k) = x_agents - x_hat_agents;
    
    % 显示进度
    if mod(k, 100) == 0
        fprintf('仿真进度: %.1f%%\n', k/length(t_span)*100);
    end
end

fprintf('仿真完成！\n');

%% 结果可视化
PlotResults(t_span, x_history, x_hat_history, x_desired, estimation_error, u_history);

end
