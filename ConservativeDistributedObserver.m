function [x_hat_new, quality] = ConservativeDistributedObserver(agent_id, x_agents, x_hat_agents, neighbors, params, dt)
% 保守稳定的分布式观测器
% 重点：确保稳定性和精度，避免发散
% 设计原则：宁可保守，不要激进

% 获取当前状态估计
x_hat_i = x_hat_agents(:, agent_id);

% 生成低噪声测量
y_pos = x_agents(1:3, agent_id) + params.noise_pos * randn(3, 1);
y_att = x_agents(7:9, agent_id) + params.noise_att * randn(3, 1);

%% 保守局部观测器更新
% 温和的位置估计校正
pos_error = y_pos - x_hat_i(1:3);
x_hat_i(1:3) = x_hat_i(1:3) + params.alpha_pos * pos_error;

% 保守的速度估计
persistent prev_pos_conservative prev_time_conservative
if isempty(prev_pos_conservative)
    prev_pos_conservative = zeros(3, 10);
    prev_time_conservative = zeros(1, 10);
end

if agent_id <= size(prev_pos_conservative, 2)
    if prev_time_conservative(agent_id) > 0
        % 简单差分速度估计
        vel_diff = (y_pos - prev_pos_conservative(:, agent_id)) / dt;
        
        % 限制速度变化率（避免突变）
        max_vel_change = 0.5;  % 最大速度变化 0.5 m/s
        vel_diff = max(-max_vel_change, min(max_vel_change, vel_diff));
        
        % 温和的速度估计校正
        vel_error = vel_diff - x_hat_i(4:6);
        x_hat_i(4:6) = x_hat_i(4:6) + params.alpha_vel * vel_error;
    end
    prev_pos_conservative(:, agent_id) = y_pos;
    prev_time_conservative(agent_id) = prev_time_conservative(agent_id) + dt;
end

% 温和的姿态估计校正
att_error = y_att - x_hat_i(7:9);
x_hat_i(7:9) = x_hat_i(7:9) + params.alpha_att * att_error;

% 保守的角速度估计
persistent prev_att_conservative
if isempty(prev_att_conservative)
    prev_att_conservative = zeros(3, 10);
end

if agent_id <= size(prev_att_conservative, 2)
    if prev_time_conservative(agent_id) > dt
        % 角速度差分估计
        omega_diff = (y_att - prev_att_conservative(:, agent_id)) / dt;
        
        % 限制角速度变化率
        max_omega_change = 0.2;  % 最大角速度变化
        omega_diff = max(-max_omega_change, min(max_omega_change, omega_diff));
        
        % 温和的角速度估计校正
        omega_error = omega_diff - x_hat_i(10:12);
        x_hat_i(10:12) = x_hat_i(10:12) + params.alpha_omega * omega_error;
    end
    prev_att_conservative(:, agent_id) = y_att;
end

%% 保守分布式一致性更新
N = size(x_hat_agents, 2);
consensus_term = zeros(12, 1);
neighbor_count = 0;

% 计算简单一致性误差
for j = 1:N
    if neighbors(agent_id, j) == 1 && j ~= agent_id
        % 位置一致性
        pos_consensus = (x_hat_agents(1:3, j) - x_hat_i(1:3));
        consensus_term(1:3) = consensus_term(1:3) + pos_consensus;
        
        % 速度一致性
        vel_consensus = (x_hat_agents(4:6, j) - x_hat_i(4:6));
        consensus_term(4:6) = consensus_term(4:6) + vel_consensus;
        
        % 姿态一致性（较小权重）
        att_consensus = 0.2 * (x_hat_agents(7:9, j) - x_hat_i(7:9));
        consensus_term(7:9) = consensus_term(7:9) + att_consensus;
        
        % 角速度一致性（较小权重）
        omega_consensus = 0.2 * (x_hat_agents(10:12, j) - x_hat_i(10:12));
        consensus_term(10:12) = consensus_term(10:12) + omega_consensus;
        
        neighbor_count = neighbor_count + 1;
    end
end

% 应用保守分布式一致性校正
if neighbor_count > 0
    consensus_term = consensus_term / neighbor_count;
    
    % 保守的一致性更新
    x_hat_i = x_hat_i + params.beta_consensus * consensus_term;
end

%% 严格的数值稳定性保护
% 物理约束（严格限制）
max_pos = 10;
x_hat_i(1:3) = max(-max_pos, min(max_pos, x_hat_i(1:3)));

max_vel = 3;
x_hat_i(4:6) = max(-max_vel, min(max_vel, x_hat_i(4:6)));

max_att = pi/12;  % 15度限制（非常严格）
x_hat_i(7:9) = max(-max_att, min(max_att, x_hat_i(7:9)));

max_omega = 1.0;
x_hat_i(10:12) = max(-max_omega, min(max_omega, x_hat_i(10:12)));

% 估计质量监控和保守重置
estimation_error_norm = norm(x_hat_i - x_agents(:, agent_id));
if estimation_error_norm > params.max_estimation_error
    % 保守重置机制
    reset_factor = 0.1;
    x_hat_i(1:3) = (1 - reset_factor) * x_hat_i(1:3) + reset_factor * y_pos;
    x_hat_i(7:9) = (1 - reset_factor) * x_hat_i(7:9) + reset_factor * y_att;
    
    % 速度和角速度保守重置
    x_hat_i(4:6) = 0.8 * x_hat_i(4:6);
    x_hat_i(10:12) = 0.8 * x_hat_i(10:12);
end

%% 观测器质量评估
% 计算保守质量指标
pos_quality = exp(-norm(x_hat_i(1:3) - x_agents(1:3, agent_id)) / 0.2);
vel_quality = exp(-norm(x_hat_i(4:6) - x_agents(4:6, agent_id)) / 0.3);
att_quality = exp(-norm(x_hat_i(7:9) - x_agents(7:9, agent_id)) / 0.1);

% 综合质量指标
quality = 0.6 * pos_quality + 0.3 * vel_quality + 0.1 * att_quality;
quality = max(0, min(1, quality));

% 如果质量过低，应用额外的稳定化措施
if quality < params.quality_threshold
    stabilization_factor = 0.2;
    x_hat_i(1:3) = (1 - stabilization_factor) * x_hat_i(1:3) + stabilization_factor * y_pos;
    x_hat_i(4:6) = 0.7 * x_hat_i(4:6);  % 大幅减速稳定
end

%% 输出更新后的状态估计
x_hat_new = x_hat_i;

%% 保守的调试信息
persistent debug_counter_conservative
if isempty(debug_counter_conservative)
    debug_counter_conservative = zeros(1, 10);
end

if agent_id <= length(debug_counter_conservative)
    debug_counter_conservative(agent_id) = debug_counter_conservative(agent_id) + 1;
    
    % 每5秒输出一次调试信息
    if mod(debug_counter_conservative(agent_id), 500) == 0
        pos_est_error = norm(x_hat_i(1:3) - x_agents(1:3, agent_id));
        vel_est_error = norm(x_hat_i(4:6) - x_agents(4:6, agent_id));
        
        uav_type = sprintf('跟随者%d', agent_id-1);
        
        fprintf('保守观测器[%s] [%.1fs] - 位置估计误差: %.4f m, 速度估计误差: %.4f m/s, 邻居数: %d, 质量: %.3f\n', ...
                uav_type, debug_counter_conservative(agent_id)*0.01, pos_est_error, vel_est_error, neighbor_count, quality);
    end
end

end
