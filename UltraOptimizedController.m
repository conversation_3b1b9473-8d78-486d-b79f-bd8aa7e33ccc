function u = UltraOptimizedController(x, x_desired, params)
% 超级优化控制器 - 进一步减少40-45秒误差峰值
% 使用预测控制和轨迹预补偿技术

% 提取状态变量
pos = x(1:3);           % 位置 [x, y, z]
vel = x(4:6);           % 速度 [vx, vy, vz]
att = x(7:9);           % 姿态角 [phi, theta, psi]
omega = x(10:12);       % 角速度 [p, q, r]

% 提取期望状态
pos_d = x_desired(1:3);
vel_d = x_desired(4:6);
att_d = x_desired(7:9);
omega_d = x_desired(10:12);

% 超级优化控制参数
k_pos = params.k_pos * 2.0;     % 位置增益大幅提高
k_vel = params.k_vel * 1.8;     % 速度增益大幅提高
k_att = params.k_att * 1.5;     % 姿态增益提高
k_omega = params.k_omega * 1.3; % 角速度增益提高

% 高级积分控制
persistent pos_error_integral vel_error_integral att_error_integral
persistent prev_pos_error prev_vel_error
if isempty(pos_error_integral)
    pos_error_integral = zeros(3, 1);
    vel_error_integral = zeros(3, 1);
    att_error_integral = zeros(3, 1);
    prev_pos_error = zeros(3, 1);
    prev_vel_error = zeros(3, 1);
end

% 位置和速度误差
pos_error = pos_d - pos;
vel_error = vel_d - vel;

% 微分项计算
dt = 0.01;
pos_error_derivative = (pos_error - prev_pos_error) / dt;
vel_error_derivative = (vel_error - prev_vel_error) / dt;

% 更新历史误差
prev_pos_error = pos_error;
prev_vel_error = vel_error;

% 积分项更新（智能抗饱和）
ki_pos = 0.3;  % 增加积分增益
ki_vel = 0.15; % 增加积分增益
kd_pos = 0.1;  % 微分增益
kd_vel = 0.05; % 微分增益

% 智能积分抗饱和 - 根据误差大小调整
error_magnitude = norm(pos_error) + norm(vel_error);
if error_magnitude > 2.0
    max_integral = 3.0; % 大误差时允许更大积分
elseif error_magnitude > 1.0
    max_integral = 2.0;
else
    max_integral = 1.0;
end

pos_error_integral = pos_error_integral + pos_error * dt;
pos_error_integral = max(-max_integral, min(max_integral, pos_error_integral));

vel_error_integral = vel_error_integral + vel_error * dt;
vel_error_integral = max(-max_integral, min(max_integral, vel_error_integral));

% 高级PID位置控制器
acc_desired = k_pos * pos_error + k_vel * vel_error + ...
              ki_pos * pos_error_integral + ki_vel * vel_error_integral + ...
              kd_pos * pos_error_derivative + kd_vel * vel_error_derivative;

% 轨迹预测和前馈补偿
persistent trajectory_predictor
if isempty(trajectory_predictor)
    trajectory_predictor = zeros(3, 5); % 存储最近5个期望位置
end

% 更新轨迹预测器
trajectory_predictor(:, 1:4) = trajectory_predictor(:, 2:5);
trajectory_predictor(:, 5) = pos_d;

% 计算轨迹变化率（用于前馈）
if sum(sum(trajectory_predictor)) ~= 0
    trajectory_change = trajectory_predictor(:, 5) - trajectory_predictor(:, 1);
    feedforward_acc = trajectory_change / (4 * dt); % 预测加速度
    acc_desired = acc_desired + 0.3 * feedforward_acc; % 30%前馈
end

% 期望推力 (考虑重力补偿和动态补偿)
mass = params.mass;
gravity = params.gravity;
T_desired = mass * (acc_desired(3) + gravity);

% 动态推力补偿 - 根据垂直速度调整
vertical_vel_compensation = 0.2 * vel(3); % 垂直速度补偿
T_desired = T_desired + mass * vertical_vel_compensation;

% 改进的期望姿态角计算
phi_desired = asin(max(-0.5, min(0.5, (acc_desired(1) * sin(att(3)) - acc_desired(2) * cos(att(3))) / gravity)));
theta_desired = atan2(acc_desired(1) * cos(att(3)) + acc_desired(2) * sin(att(3)), ...
                      acc_desired(3) + gravity);
psi_desired = att_d(3);

att_desired = [phi_desired; theta_desired; psi_desired];

% 姿态误差和积分控制
att_error = att_desired - att;
omega_error = omega_d - omega;

% 姿态积分控制
att_error_integral = att_error_integral + att_error * dt;
att_error_integral = max(-1.0, min(1.0, att_error_integral));

% 高级姿态控制器
tau_desired = k_att * att_error + k_omega * omega_error + 0.1 * att_error_integral;

% 智能控制输入限幅
T_max = params.T_max;
T_min = params.T_min;
tau_max = params.tau_max;

% 动态限幅 - 根据误差大小调整限制
if error_magnitude > 1.5
    T_max = T_max * 1.1; % 大误差时允许更大推力
    tau_max = tau_max * 1.1;
end

T = max(T_min, min(T_max, T_desired));
tau = max(-tau_max, min(tau_max, tau_desired));

% 高级自适应控制
persistent ultra_adaptation_gain adaptation_history
if isempty(ultra_adaptation_gain)
    ultra_adaptation_gain = 1.0;
    adaptation_history = ones(10, 1); % 存储最近10个增益值
end

% 更新适应历史
adaptation_history(1:9) = adaptation_history(2:10);
adaptation_history(10) = ultra_adaptation_gain;

% 智能自适应增益调整
if error_magnitude > 1.5
    ultra_adaptation_gain = min(2.0, ultra_adaptation_gain * 1.02);
elseif error_magnitude > 0.8
    ultra_adaptation_gain = min(1.5, ultra_adaptation_gain * 1.005);
elseif error_magnitude < 0.2
    ultra_adaptation_gain = max(0.7, ultra_adaptation_gain * 0.998);
end

% 平滑增益变化
smooth_gain = mean(adaptation_history);
ultra_adaptation_gain = 0.7 * ultra_adaptation_gain + 0.3 * smooth_gain;

% 输出控制信号
u = [T * ultra_adaptation_gain; tau * ultra_adaptation_gain];

% 最终安全限制
u(1) = max(T_min, min(T_max, u(1)));
u(2:4) = max(-tau_max, min(tau_max, u(2:4)));

end
