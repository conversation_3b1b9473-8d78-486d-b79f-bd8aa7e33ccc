function x_hat_new = DistributedObserverHybrid(agent_id, x_agents, x_hat_agents, neighbors, params, dt)
% 混合架构的分布式观测器
% 专门为混合系统设计的完整分布式状态估计功能
% 重点展示分布式观测器的工作原理和性能特征
% 输入参数:
%   agent_id - 当前无人机ID
%   x_agents - 所有无人机真实状态矩阵 (12 x N) [仅用于生成测量]
%   x_hat_agents - 所有无人机状态估计矩阵 (12 x N)
%   neighbors - 邻接矩阵，表示通信拓扑 (N x N)
%   params - 观测器参数结构体
%   dt - 时间步长
% 输出:
%   x_hat_new - 更新后的状态估计 (12x1)

% 获取当前状态估计
x_hat_i = x_hat_agents(:, agent_id);

% 生成带噪声的测量 (模拟真实传感器)
y_pos = x_agents(1:3, agent_id) + params.noise_pos * randn(3, 1);  % 位置测量
y_att = x_agents(7:9, agent_id) + params.noise_att * randn(3, 1);  % 姿态测量

%% 局部观测器更新 (基于本地测量)
% 位置估计校正
pos_error = y_pos - x_hat_i(1:3);
x_hat_i(1:3) = x_hat_i(1:3) + params.alpha_pos * pos_error;

% 速度估计 (基于位置变化和动态模型)
persistent prev_pos_hybrid prev_time_hybrid
if isempty(prev_pos_hybrid)
    prev_pos_hybrid = zeros(3, 10);  % 支持最多10个无人机
    prev_time_hybrid = zeros(1, 10);
end

if agent_id <= size(prev_pos_hybrid, 2)
    if prev_time_hybrid(agent_id) > 0
        % 基于位置差分的速度估计
        vel_diff = (y_pos - prev_pos_hybrid(:, agent_id)) / dt;
        
        % 结合当前速度估计和差分速度
        vel_error = vel_diff - x_hat_i(4:6);
        x_hat_i(4:6) = x_hat_i(4:6) + params.alpha_vel * vel_error;
    end
    prev_pos_hybrid(:, agent_id) = y_pos;
    prev_time_hybrid(agent_id) = prev_time_hybrid(agent_id) + dt;
end

% 姿态估计校正
att_error = y_att - x_hat_i(7:9);
x_hat_i(7:9) = x_hat_i(7:9) + params.alpha_att * att_error;

% 角速度估计 (基于姿态变化)
persistent prev_att_hybrid
if isempty(prev_att_hybrid)
    prev_att_hybrid = zeros(3, 10);
end

if agent_id <= size(prev_att_hybrid, 2)
    if prev_time_hybrid(agent_id) > dt
        % 基于姿态差分的角速度估计
        omega_diff = (y_att - prev_att_hybrid(:, agent_id)) / dt;
        
        % 结合当前角速度估计和差分角速度
        omega_error = omega_diff - x_hat_i(10:12);
        x_hat_i(10:12) = x_hat_i(10:12) + params.alpha_omega * omega_error;
    end
    prev_att_hybrid(:, agent_id) = y_att;
end

%% 分布式一致性更新 (核心分布式算法)
N = size(x_hat_agents, 2);
consensus_term = zeros(12, 1);
neighbor_count = 0;

% 计算与邻居的一致性误差
for j = 1:N
    if neighbors(agent_id, j) == 1 && j ~= agent_id
        % 位置一致性
        pos_consensus = x_hat_agents(1:3, j) - x_hat_i(1:3);
        consensus_term(1:3) = consensus_term(1:3) + pos_consensus;
        
        % 速度一致性
        vel_consensus = x_hat_agents(4:6, j) - x_hat_i(4:6);
        consensus_term(4:6) = consensus_term(4:6) + vel_consensus;
        
        % 姿态一致性 (较小权重)
        att_consensus = x_hat_agents(7:9, j) - x_hat_i(7:9);
        consensus_term(7:9) = consensus_term(7:9) + 0.5 * att_consensus;
        
        % 角速度一致性 (较小权重)
        omega_consensus = x_hat_agents(10:12, j) - x_hat_i(10:12);
        consensus_term(10:12) = consensus_term(10:12) + 0.5 * omega_consensus;
        
        neighbor_count = neighbor_count + 1;
    end
end

% 应用分布式一致性校正
if neighbor_count > 0
    consensus_term = consensus_term / neighbor_count;
    
    % 分布式一致性更新 (展示完整分布式算法)
    x_hat_i = x_hat_i + params.beta_consensus * consensus_term;
end

%% 分布式观测器的高级功能

% 1. 自适应增益调整 (基于邻居数量)
if neighbor_count > 0
    % 邻居越多，一致性权重越大
    adaptive_factor = min(1.5, 1.0 + 0.1 * neighbor_count);
    consensus_adjustment = (adaptive_factor - 1.0) * params.beta_consensus * consensus_term;
    x_hat_i = x_hat_i + consensus_adjustment;
end

% 2. 分布式故障检测 (检测异常邻居)
persistent neighbor_error_history
if isempty(neighbor_error_history)
    neighbor_error_history = zeros(10, 10, 5);  % 10个无人机，10个邻居，5个历史点
end

if agent_id <= size(neighbor_error_history, 1)
    neighbor_idx = 1;
    for j = 1:N
        if neighbors(agent_id, j) == 1 && j ~= agent_id && neighbor_idx <= size(neighbor_error_history, 2)
            % 计算与邻居j的状态差异
            neighbor_diff = norm(x_hat_agents(1:6, j) - x_hat_i(1:6));
            
            % 更新邻居误差历史
            history_slice = squeeze(neighbor_error_history(agent_id, neighbor_idx, :));
            neighbor_error_history(agent_id, neighbor_idx, :) = ...
                [history_slice(2:end); neighbor_diff];
            
            % 检测异常邻居 (连续高误差)
            avg_error = mean(neighbor_error_history(agent_id, neighbor_idx, :));
            if avg_error > 5.0  % 异常阈值
                % 降低对该邻居的信任度
                fault_factor = 0.5;
                neighbor_contribution = (x_hat_agents(1:6, j) - x_hat_i(1:6)) / neighbor_count;
                x_hat_i(1:6) = x_hat_i(1:6) - (1 - fault_factor) * params.beta_consensus * neighbor_contribution;
            end
            
            neighbor_idx = neighbor_idx + 1;
        end
    end
end

% 3. 分布式状态预测 (基于动态模型)
% 简化的四旋翼动态模型预测
if prev_time_hybrid(agent_id) > dt
    % 位置预测
    predicted_pos = x_hat_i(1:3) + dt * x_hat_i(4:6);
    pos_prediction_error = predicted_pos - y_pos;
    
    % 如果预测误差较小，增强对动态模型的信任
    if norm(pos_prediction_error) < 0.5
        model_trust_factor = 0.1;
        x_hat_i(1:3) = (1 - model_trust_factor) * x_hat_i(1:3) + model_trust_factor * predicted_pos;
    end
end

%% 数值稳定性保护和约束
% 物理约束
max_pos = 30;
x_hat_i(1:3) = max(-max_pos, min(max_pos, x_hat_i(1:3)));

max_vel = 12;
x_hat_i(4:6) = max(-max_vel, min(max_vel, x_hat_i(4:6)));

max_att = pi/4;  % 45度限制
x_hat_i(7:9) = max(-max_att, min(max_att, x_hat_i(7:9)));

max_omega = 2.5;
x_hat_i(10:12) = max(-max_omega, min(max_omega, x_hat_i(10:12)));

% 估计质量监控
estimation_error_norm = norm(x_hat_i - x_agents(:, agent_id));
if estimation_error_norm > params.max_estimation_error
    % 软重置机制
    reset_factor = 0.05;
    x_hat_i(1:3) = (1 - reset_factor) * x_hat_i(1:3) + reset_factor * y_pos;
    x_hat_i(7:9) = (1 - reset_factor) * x_hat_i(7:9) + reset_factor * y_att;
end

%% 输出更新后的状态估计
x_hat_new = x_hat_i;

%% 分布式观测器性能监控和调试信息
persistent debug_counter_hybrid communication_stats
if isempty(debug_counter_hybrid)
    debug_counter_hybrid = zeros(1, 10);
    communication_stats = zeros(10, 3);  % [总通信次数, 成功一致性更新, 故障检测次数]
end

if agent_id <= length(debug_counter_hybrid)
    debug_counter_hybrid(agent_id) = debug_counter_hybrid(agent_id) + 1;
    
    % 更新通信统计
    if agent_id <= size(communication_stats, 1)
        communication_stats(agent_id, 1) = communication_stats(agent_id, 1) + neighbor_count;
        if neighbor_count > 0
            communication_stats(agent_id, 2) = communication_stats(agent_id, 2) + 1;
        end
    end
    
    % 每5秒输出一次详细调试信息
    if mod(debug_counter_hybrid(agent_id), 500) == 0
        pos_est_error = norm(x_hat_i(1:3) - x_agents(1:3, agent_id));
        vel_est_error = norm(x_hat_i(4:6) - x_agents(4:6, agent_id));
        
        if agent_id == 1
            uav_type = '领导者';
        else
            uav_type = sprintf('跟随者%d', agent_id-1);
        end
        
        % 计算通信效率
        total_comm = communication_stats(agent_id, 1);
        successful_updates = communication_stats(agent_id, 2);
        comm_efficiency = 0;
        if total_comm > 0
            comm_efficiency = successful_updates / total_comm * 100;
        end
        
        fprintf('分布式观测器[%s] [%.1fs] - 位置估计误差: %.4f m, 速度估计误差: %.4f m/s, 邻居数: %d, 通信效率: %.1f%%\n', ...
                uav_type, debug_counter_hybrid(agent_id)*0.01, pos_est_error, vel_est_error, neighbor_count, comm_efficiency);
    end
end

end
