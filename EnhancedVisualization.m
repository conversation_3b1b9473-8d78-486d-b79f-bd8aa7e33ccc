function EnhancedVisualization(t_span, x_history, x_desired_history, tracking_error_pos, tracking_error_vel, u_history)
% 增强版可视化函数 - 确保所有曲线都可见
% 解决只显示无人机4曲线的问题

fprintf('开始生成增强版可视化图表...\n');

%% 计算跟踪误差范数
N_followers = size(tracking_error_pos, 2);
pos_error_norm = zeros(N_followers, length(t_span));
vel_error_norm = zeros(N_followers, length(t_span));

for i = 1:N_followers
    for k = 1:length(t_span)
        pos_error_norm(i, k) = norm(tracking_error_pos(:, i, k));
        vel_error_norm(i, k) = norm(tracking_error_vel(:, i, k));
    end
end

% 调试信息：检查数据
fprintf('数据检查:\n');
for i = 1:N_followers
    fprintf('无人机%d - 位置误差范围: [%.3f, %.3f], 速度误差范围: [%.3f, %.3f]\n', ...
            i, min(pos_error_norm(i, :)), max(pos_error_norm(i, :)), ...
            min(vel_error_norm(i, :)), max(vel_error_norm(i, :)));
end

%% 定义颜色和线型 - 确保区分度高
colors = [
    0.8, 0.2, 0.2;    % 红色 - 无人机1
    0.2, 0.8, 0.2;    % 绿色 - 无人机2  
    0.2, 0.2, 0.8;    % 蓝色 - 无人机3
    0.8, 0.2, 0.8;    % 紫色 - 无人机4
];

line_styles = {'-', '--', '-.', ':'};
line_widths = [2.5, 2.5, 2.5, 2.5]; % 统一加粗线宽

%% 图1: 位置跟踪误差
figure('Name', '位置跟踪误差分析', 'Position', [100, 100, 1200, 800]);

% 主图
subplot(2, 2, [1, 2]);
hold on; grid on;

% 绘制所有曲线，确保可见性
h_pos = [];
for i = 1:N_followers
    h = plot(t_span, pos_error_norm(i, :), ...
             'Color', colors(i, :), ...
             'LineStyle', line_styles{i}, ...
             'LineWidth', line_widths(i), ...
             'DisplayName', sprintf('无人机_%d', i));
    h_pos = [h_pos, h];
    
    % 强制显示每条曲线
    set(h, 'Visible', 'on');
end

xlabel('时间(s)', 'FontSize', 12); 
ylabel('位置跟踪误差 m_{pi}(t) (m)', 'FontSize', 12);
title('位置跟踪误差 m_{pi}(t)', 'FontSize', 14, 'FontWeight', 'bold');
legend(h_pos, 'Location', 'northeast', 'FontSize', 10);
xlim([0, 50]); 
ylim([0, max(pos_error_norm(:)) * 1.1]);

% 放大图 - 15-25秒
subplot(2, 2, 3);
hold on; grid on;
t_zoom = t_span(t_span >= 15 & t_span <= 25);
idx_zoom = find(t_span >= 15 & t_span <= 25);

for i = 1:N_followers
    plot(t_zoom, pos_error_norm(i, idx_zoom), ...
         'Color', colors(i, :), ...
         'LineStyle', line_styles{i}, ...
         'LineWidth', line_widths(i));
end

xlim([15, 25]); 
ylim([0, max(pos_error_norm(:, idx_zoom), [], 'all') * 1.1]);
xlabel('时间(s)', 'FontSize', 10); 
ylabel('位置误差 (m)', 'FontSize', 10);
title('放大图: 15-25秒', 'FontSize', 12);
grid on;

% 关键时段分析 - 40-45秒
subplot(2, 2, 4);
hold on; grid on;
t_critical = t_span(t_span >= 40 & t_span <= 45);
idx_critical = find(t_span >= 40 & t_span <= 45);

for i = 1:N_followers
    plot(t_critical, pos_error_norm(i, idx_critical), ...
         'Color', colors(i, :), ...
         'LineStyle', line_styles{i}, ...
         'LineWidth', line_widths(i));
end

xlim([40, 45]); 
ylim([0, max(pos_error_norm(:, idx_critical), [], 'all') * 1.1]);
xlabel('时间(s)', 'FontSize', 10); 
ylabel('位置误差 (m)', 'FontSize', 10);
title('关键时段: 40-45秒', 'FontSize', 12);
grid on;

%% 图2: 速度跟踪误差
figure('Name', '速度跟踪误差分析', 'Position', [150, 150, 1200, 800]);

% 主图
subplot(2, 2, [1, 2]);
hold on; grid on;

h_vel = [];
for i = 1:N_followers
    h = plot(t_span, vel_error_norm(i, :), ...
             'Color', colors(i, :), ...
             'LineStyle', line_styles{i}, ...
             'LineWidth', line_widths(i), ...
             'DisplayName', sprintf('无人机_%d', i));
    h_vel = [h_vel, h];
    
    % 强制显示每条曲线
    set(h, 'Visible', 'on');
end

xlabel('时间(s)', 'FontSize', 12); 
ylabel('速度跟踪误差 m_{vi}(t) (m/s)', 'FontSize', 12);
title('速度跟踪误差 m_{vi}(t)', 'FontSize', 14, 'FontWeight', 'bold');
legend(h_vel, 'Location', 'northeast', 'FontSize', 10);
xlim([0, 50]); 
ylim([0, max(vel_error_norm(:)) * 1.1]);

% 放大图 - 15-25秒
subplot(2, 2, 3);
hold on; grid on;

for i = 1:N_followers
    plot(t_zoom, vel_error_norm(i, idx_zoom), ...
         'Color', colors(i, :), ...
         'LineStyle', line_styles{i}, ...
         'LineWidth', line_widths(i));
end

xlim([15, 25]); 
ylim([0, max(vel_error_norm(:, idx_zoom), [], 'all') * 1.1]);
xlabel('时间(s)', 'FontSize', 10); 
ylabel('速度误差 (m/s)', 'FontSize', 10);
title('放大图: 15-25秒', 'FontSize', 12);
grid on;

% 关键时段分析 - 40-45秒
subplot(2, 2, 4);
hold on; grid on;

for i = 1:N_followers
    plot(t_critical, vel_error_norm(i, idx_critical), ...
         'Color', colors(i, :), ...
         'LineStyle', line_styles{i}, ...
         'LineWidth', line_widths(i));
end

xlim([40, 45]); 
ylim([0, max(vel_error_norm(:, idx_critical), [], 'all') * 1.1]);
xlabel('时间(s)', 'FontSize', 10); 
ylabel('速度误差 (m/s)', 'FontSize', 10);
title('关键时段: 40-45秒', 'FontSize', 12);
grid on;

%% 图3: 3D飞行轨迹
figure('Name', '3D飞行轨迹', 'Position', [200, 200, 1000, 800]);

% 绘制轨迹
hold on; grid on;

% 领导者轨迹
leader_traj = squeeze(x_history(1:3, 1, :));
plot3(leader_traj(1, :), leader_traj(2, :), leader_traj(3, :), ...
      'k-', 'LineWidth', 3, 'DisplayName', '领导者轨迹');

% 跟随者轨迹
for i = 1:N_followers
    follower_traj = squeeze(x_history(1:3, i+1, :));
    plot3(follower_traj(1, :), follower_traj(2, :), follower_traj(3, :), ...
          'Color', colors(i, :), 'LineStyle', line_styles{i}, ...
          'LineWidth', 2, 'DisplayName', sprintf('无人机_%d轨迹', i));
end

% 期望轨迹（虚线）
for i = 1:N_followers
    desired_traj = squeeze(x_desired_history(1:3, i+1, :));
    plot3(desired_traj(1, :), desired_traj(2, :), desired_traj(3, :), ...
          'Color', colors(i, :), 'LineStyle', ':', 'LineWidth', 1, ...
          'DisplayName', sprintf('无人机_%d期望轨迹', i));
end

xlabel('X (m)', 'FontSize', 12);
ylabel('Y (m)', 'FontSize', 12);
zlabel('Z (m)', 'FontSize', 12);
title('无人机编队3D飞行轨迹', 'FontSize', 14, 'FontWeight', 'bold');
legend('Location', 'best', 'FontSize', 10);
view(45, 30);
axis equal;

%% 性能统计表
fprintf('\n=== 增强版性能统计 ===\n');
for i = 1:N_followers
    max_pos = max(pos_error_norm(i, :));
    max_vel = max(vel_error_norm(i, :));
    
    % 40-45秒误差
    max_pos_40_45 = max(pos_error_norm(i, idx_critical));
    max_vel_40_45 = max(vel_error_norm(i, idx_critical));
    
    fprintf('无人机%d: 最大位置误差=%.3fm, 最大速度误差=%.3fm/s\n', i, max_pos, max_vel);
    fprintf('        40-45s: 位置误差=%.3fm, 速度误差=%.3fm/s\n', max_pos_40_45, max_vel_40_45);
end

fprintf('\n✅ 增强版可视化完成！\n');
fprintf('📊 生成了3个详细分析图表\n');
fprintf('🔍 包含15-25秒和40-45秒放大分析\n');
fprintf('🎯 所有无人机曲线应该都清晰可见\n');

end
