function [u] = DistributedController(agent_id, x_agents, x_desired, neighbors, params)
% 分布式控制器 - 基于一致性算法的编队控制
% 输入参数:
%   agent_id - 当前无人机ID
%   x_agents - 所有无人机状态矩阵 (12 x N)
%   x_desired - 期望编队状态矩阵 (12 x N)
%   neighbors - 邻接矩阵，表示通信拓扑 (N x N)
%   params - 控制参数结构体
% 输出:
%   u - 控制输入 [总推力, 滚转力矩, 俯仰力矩, 偏航力矩]' (4x1)

% 获取当前无人机状态
x_i = x_agents(:, agent_id);
x_d_i = x_desired(:, agent_id);

% 提取状态变量
pos_i = x_i(1:3);          % 当前位置
vel_i = x_i(4:6);          % 当前速度
euler_i = x_i(7:9);        % 当前欧拉角
omega_i = x_i(10:12);      % 当前角速度

% 期望状态
pos_d_i = x_d_i(1:3);      % 期望位置
vel_d_i = x_d_i(4:6);      % 期望速度
euler_d_i = x_d_i(7:9);    % 期望欧拉角
omega_d_i = x_d_i(10:12);  % 期望角速度

% 控制参数
k_pos = params.k_pos;      % 位置控制增益
k_vel = params.k_vel;      % 速度控制增益
k_att = params.k_att;      % 姿态控制增益
k_omega = params.k_omega;  % 角速度控制增益
k_consensus = params.k_consensus;  % 一致性增益

% 无人机物理参数
m = params.mass;
g = params.gravity;

% 一致性项计算
consensus_pos = zeros(3, 1);
consensus_vel = zeros(3, 1);
consensus_att = zeros(3, 1);

N = size(x_agents, 2);  % 无人机数量

% 计算与邻居的一致性误差
for j = 1:N
    if neighbors(agent_id, j) == 1 && j ~= agent_id
        % 位置一致性
        relative_pos = (pos_i - x_d_i(1:3)) - (x_agents(1:3, j) - x_desired(1:3, j));
        consensus_pos = consensus_pos + relative_pos;
        
        % 速度一致性
        relative_vel = (vel_i - x_d_i(4:6)) - (x_agents(4:6, j) - x_desired(4:6, j));
        consensus_vel = consensus_vel + relative_vel;
        
        % 姿态一致性
        relative_att = (euler_i - x_d_i(7:9)) - (x_agents(7:9, j) - x_desired(7:9, j));
        consensus_att = consensus_att + relative_att;
    end
end

% 位置控制器
pos_error = pos_i - pos_d_i;
vel_error = vel_i - vel_d_i;

% 期望加速度 (包含一致性项)
acc_desired = -k_pos * pos_error - k_vel * vel_error - k_consensus * consensus_pos;

% 期望推力 (考虑重力补偿)
phi = euler_i(1);
theta = euler_i(2);
psi = euler_i(3);

% 期望的总推力
T_desired = m * (acc_desired(3) + g) / (cos(phi) * cos(theta));

% 期望的滚转角和俯仰角
phi_desired = asin((acc_desired(1) * sin(psi) - acc_desired(2) * cos(psi)) / g);
theta_desired = atan2(acc_desired(1) * cos(psi) + acc_desired(2) * sin(psi), acc_desired(3) + g);

% 姿态控制器
att_error = euler_i - [phi_desired; theta_desired; euler_d_i(3)];
omega_error = omega_i - omega_d_i;

% 期望力矩 (包含一致性项)
tau_desired = -k_att * att_error - k_omega * omega_error - k_consensus * consensus_att;

% 限制控制输入
T_max = params.T_max;
T_min = params.T_min;
tau_max = params.tau_max;

T = max(T_min, min(T_max, T_desired));
tau_phi = max(-tau_max, min(tau_max, tau_desired(1)));
tau_theta = max(-tau_max, min(tau_max, tau_desired(2)));
tau_psi = max(-tau_max, min(tau_max, tau_desired(3)));

% 输出控制信号
u = [T; tau_phi; tau_theta; tau_psi];

end
