% 系统测试脚本 - 验证分布式观测器编队控制系统
clear; clc; close all;

fprintf('开始系统测试...\n');

%% 测试1: 四旋翼动力学模型
fprintf('测试1: 四旋翼动力学模型...\n');
try
    % 设置测试参数
    quad_params.mass = 1.2;
    quad_params.gravity = 9.81;
    quad_params.Ixx = 0.0347;
    quad_params.Iyy = 0.0347;
    quad_params.Izz = 0.0617;
    
    % 测试状态和控制输入
    x_test = [0; 0; 0; 0; 0; 0; 0; 0; 0; 0; 0; 0];  % 初始状态
    u_test = [quad_params.mass * quad_params.gravity; 0; 0; 0];  % 悬停控制
    
    % 调用动力学函数
    x_dot = QuadrotorDynamics(0, x_test, u_test, quad_params);
    
    if any(isnan(x_dot)) || any(isinf(x_dot))
        error('动力学模型输出包含NaN或Inf');
    end
    
    fprintf('  ✓ 四旋翼动力学模型测试通过\n');
catch ME
    fprintf('  ✗ 四旋翼动力学模型测试失败: %s\n', ME.message);
end

%% 测试2: 分布式观测器
fprintf('测试2: 分布式观测器...\n');
try
    % 观测器参数
    observer_params.A = zeros(12, 12);
    observer_params.A(1:3, 4:6) = eye(3);
    observer_params.A(7:9, 10:12) = eye(3);
    observer_params.B = zeros(12, 4);
    observer_params.C = [eye(3), zeros(3, 9); zeros(3, 6), eye(3), zeros(3, 3)];
    observer_params.observer_gain = 2 * eye(12, 6);
    observer_params.k_consensus_obs = 0.5;
    
    % 测试数据
    N_agents = 2;
    x_agents = randn(12, N_agents);
    y_agents = randn(6, N_agents);
    x_hat_agents = randn(12, N_agents);
    neighbors = [0, 1; 1, 0];
    
    % 调用观测器函数
    [x_hat_dot, x_hat] = DistributedObserver(1, x_agents, y_agents, x_hat_agents, neighbors, observer_params);
    
    if any(isnan(x_hat_dot)) || any(isinf(x_hat_dot))
        error('观测器输出包含NaN或Inf');
    end
    
    fprintf('  ✓ 分布式观测器测试通过\n');
catch ME
    fprintf('  ✗ 分布式观测器测试失败: %s\n', ME.message);
end

%% 测试3: 分布式控制器
fprintf('测试3: 分布式控制器...\n');
try
    % 控制器参数
    control_params.k_pos = 2.0;
    control_params.k_vel = 1.5;
    control_params.k_att = 1.0;
    control_params.k_omega = 0.5;
    control_params.k_consensus = 0.8;
    control_params.mass = 1.2;
    control_params.gravity = 9.81;
    control_params.T_max = 20;
    control_params.T_min = 0;
    control_params.tau_max = 2;
    
    % 测试数据
    N_agents = 2;
    x_agents = zeros(12, N_agents);
    x_desired = zeros(12, N_agents);
    x_desired(1:3, 1) = [0; 0; 5];  % 期望位置
    x_desired(1:3, 2) = [2; 0; 5];
    neighbors = [0, 1; 1, 0];
    
    % 调用控制器函数
    u = DistributedController(1, x_agents, x_desired, neighbors, control_params);
    
    if any(isnan(u)) || any(isinf(u))
        error('控制器输出包含NaN或Inf');
    end
    
    fprintf('  ✓ 分布式控制器测试通过\n');
catch ME
    fprintf('  ✗ 分布式控制器测试失败: %s\n', ME.message);
end

%% 测试4: 简化仿真
fprintf('测试4: 简化仿真 (5秒)...\n');
try
    % 简化的仿真参数
    dt = 0.01;
    T_sim = 5;  % 短时间仿真
    t_span = 0:dt:T_sim;
    N_agents = 2;  % 减少无人机数量
    
    % 初始化
    x_agents = zeros(12, N_agents);
    x_hat_agents = x_agents + 0.01 * randn(12, N_agents);
    x_desired = zeros(12, N_agents);
    x_desired(1:3, 1) = [0; 0; 5];
    x_desired(1:3, 2) = [2; 0; 5];
    
    neighbors = [0, 1; 1, 0];
    
    % 运行几个仿真步骤
    for k = 1:min(100, length(t_span))  % 只运行100步
        t = t_span(k);
        
        % 测量数据
        y_agents = zeros(6, N_agents);
        for i = 1:N_agents
            y_agents(1:3, i) = x_agents(1:3, i) + 0.01 * randn(3, 1);
            y_agents(4:6, i) = x_agents(7:9, i) + 0.005 * randn(3, 1);
        end
        
        % 观测器更新
        x_hat_dot_agents = zeros(12, N_agents);
        for i = 1:N_agents
            [x_hat_dot_agents(:, i), ~] = DistributedObserver(i, x_agents, y_agents, ...
                                                              x_hat_agents, neighbors, observer_params);
        end
        x_hat_agents = x_hat_agents + dt * x_hat_dot_agents;
        
        % 控制器
        u_agents = zeros(4, N_agents);
        for i = 1:N_agents
            u_agents(:, i) = DistributedController(i, x_hat_agents, x_desired, neighbors, control_params);
        end
        
        % 动力学更新
        x_dot_agents = zeros(12, N_agents);
        for i = 1:N_agents
            x_dot_agents(:, i) = QuadrotorDynamics(t, x_agents(:, i), u_agents(:, i), quad_params);
        end
        x_agents = x_agents + dt * x_dot_agents;
        
        % 检查数值稳定性
        if any(any(isnan(x_agents))) || any(any(isinf(x_agents)))
            error('仿真中出现NaN或Inf值');
        end
    end
    
    fprintf('  ✓ 简化仿真测试通过\n');
catch ME
    fprintf('  ✗ 简化仿真测试失败: %s\n', ME.message);
end

%% 测试总结
fprintf('\n=== 测试总结 ===\n');
fprintf('系统基本功能测试完成\n');
fprintf('如果所有测试都通过，可以运行完整仿真: FormationControlMain\n');
fprintf('如果有测试失败，请检查相应的函数实现\n');
