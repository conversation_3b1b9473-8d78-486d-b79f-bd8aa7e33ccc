function PlotImprovedObserverResults(t_span, x_history, x_hat_history, x_desired, ...
                                     estimation_error, u_history, pos_errors, vel_errors, formation_offset, observer_params)
% 改进领导者-跟随者 + 高质量观测器结果可视化函数
% 专门展示渐进式状态估计集成的效果

N_agents = size(x_history, 2);

% 设置中文字体
try
    set(0, 'DefaultAxesFontName', 'SimHei');
    set(0, 'DefaultTextFontName', 'SimHei');
catch
    warning('无法设置中文字体，使用默认字体');
end

% 高对比度颜色设置
colors = [
    0.8, 0.0, 0.8;  % 洋红 - 领导者
    1.0, 0.0, 0.0;  % 红色 - 跟随者1
    0.0, 0.8, 0.0;  % 绿色 - 跟随者2  
    0.0, 0.0, 1.0;  % 蓝色 - 跟随者3
    1.0, 0.5, 0.0   % 橙色 - 跟随者4
];

line_styles = {'-', '--', '-.', ':', '-'};
line_width = 2.5;

%% 图1: 位置跟踪误差图 (重点展示渐进式集成效果)
figure('Name', '改进领导者-跟随者 + 高质量观测器 - 位置跟踪误差', 'Position', [100, 100, 1200, 800]);

% 主图 - 完整时间范围
subplot(2, 2, [1, 2]);
hold on; grid on;

% 添加过渡阶段标记
transition_time = observer_params.transition_time;
fill([5, transition_time, transition_time, 5], [0, 0, max(pos_errors(:))*1.1, max(pos_errors(:))*1.1], ...
     [0.9, 0.9, 0.9], 'FaceAlpha', 0.3, 'EdgeColor', 'none', 'DisplayName', '状态估计过渡期');

for i = 1:N_agents
    if i == 1
        label_name = '领导者';
    else
        label_name = sprintf('跟随者%d', i-1);
    end
    plot(t_span, pos_errors(i, :), 'Color', colors(i, :), 'LineStyle', line_styles{i}, ...
         'LineWidth', line_width, 'DisplayName', label_name);
end

% 添加关键时间点标记
xline(5, '--k', '开始过渡', 'LineWidth', 1.5, 'FontSize', 10);
xline(transition_time, '--k', '完全估计', 'LineWidth', 1.5, 'FontSize', 10);

xlabel('时间 (s)', 'FontSize', 12);
ylabel('位置跟踪误差 (m)', 'FontSize', 12);
title('位置跟踪误差 - 改进领导者跟随者系统 + 高质量观测器', 'FontSize', 14, 'FontWeight', 'bold');
legend('Location', 'northeast', 'FontSize', 10);
xlim([0, max(t_span)]);
ylim([0, max(pos_errors(:)) * 1.1]);

% 添加性能指标文本
max_pos_error = max(pos_errors(:));
steady_start_idx = find(t_span >= transition_time, 1);
if ~isempty(steady_start_idx)
    steady_max_pos = max(pos_errors(:, steady_start_idx:end));
    text(0.02, 0.95, sprintf('最大误差: %.2f m\n完全估计后最大误差: %.2f m', max_pos_error, steady_max_pos), ...
         'Units', 'normalized', 'FontSize', 10, 'BackgroundColor', 'white', ...
         'VerticalAlignment', 'top');
end

% 放大图 - 25-35秒关键时段
subplot(2, 2, 3);
hold on; grid on;
zoom_start = 25; zoom_end = 35;
zoom_idx_start = find(t_span >= zoom_start, 1);
zoom_idx_end = find(t_span >= zoom_end, 1);

if ~isempty(zoom_idx_start) && ~isempty(zoom_idx_end)
    for i = 1:N_agents
        if i == 1
            label_name = '领导者';
        else
            label_name = sprintf('跟随者%d', i-1);
        end
        plot(t_span(zoom_idx_start:zoom_idx_end), pos_errors(i, zoom_idx_start:zoom_idx_end), ...
             'Color', colors(i, :), 'LineStyle', line_styles{i}, 'LineWidth', line_width, ...
             'DisplayName', label_name);
    end
    xlabel('时间 (s)', 'FontSize', 12);
    ylabel('位置跟踪误差 (m)', 'FontSize', 12);
    title(sprintf('位置跟踪误差放大图 (%d-%d秒)', zoom_start, zoom_end), 'FontSize', 12, 'FontWeight', 'bold');
    legend('Location', 'northeast', 'FontSize', 9);
    xlim([zoom_start, zoom_end]);
end

% 性能对比分析
subplot(2, 2, 4);
hold on; grid on;

% 计算不同阶段的平均性能
phase1_idx = find(t_span >= 0 & t_span < 5);
phase2_idx = find(t_span >= 5 & t_span < transition_time);
phase3_idx = find(t_span >= transition_time);

if ~isempty(phase1_idx) && ~isempty(phase2_idx) && ~isempty(phase3_idx)
    phase1_avg = mean(pos_errors(:, phase1_idx), 'all');
    phase2_avg = mean(pos_errors(:, phase2_idx), 'all');
    phase3_avg = mean(pos_errors(:, phase3_idx), 'all');
    
    phases = {'真实状态\n(0-5s)', '过渡期\n(5-15s)', '估计状态\n(15-50s)'};
    avg_errors = [phase1_avg, phase2_avg, phase3_avg];
    
    bar(avg_errors, 'FaceColor', [0.3, 0.6, 0.9], 'EdgeColor', 'k', 'LineWidth', 1.5);
    set(gca, 'XTickLabel', phases);
    ylabel('平均位置误差 (m)', 'FontSize', 12);
    title('不同阶段性能对比', 'FontSize', 12, 'FontWeight', 'bold');
    
    % 添加数值标签
    for i = 1:length(avg_errors)
        text(i, avg_errors(i) + max(avg_errors)*0.05, sprintf('%.3f m', avg_errors(i)), ...
             'HorizontalAlignment', 'center', 'FontSize', 10, 'FontWeight', 'bold');
    end
end

%% 图2: 速度跟踪误差图
figure('Name', '改进领导者-跟随者 + 高质量观测器 - 速度跟踪误差', 'Position', [150, 150, 1200, 800]);

% 主图 - 完整时间范围
subplot(2, 2, [1, 2]);
hold on; grid on;

% 添加过渡阶段标记
fill([5, transition_time, transition_time, 5], [0, 0, max(vel_errors(:))*1.1, max(vel_errors(:))*1.1], ...
     [0.9, 0.9, 0.9], 'FaceAlpha', 0.3, 'EdgeColor', 'none', 'DisplayName', '状态估计过渡期');

for i = 1:N_agents
    if i == 1
        label_name = '领导者';
    else
        label_name = sprintf('跟随者%d', i-1);
    end
    plot(t_span, vel_errors(i, :), 'Color', colors(i, :), 'LineStyle', line_styles{i}, ...
         'LineWidth', line_width, 'DisplayName', label_name);
end

% 添加关键时间点标记
xline(5, '--k', '开始过渡', 'LineWidth', 1.5, 'FontSize', 10);
xline(transition_time, '--k', '完全估计', 'LineWidth', 1.5, 'FontSize', 10);

xlabel('时间 (s)', 'FontSize', 12);
ylabel('速度跟踪误差 (m/s)', 'FontSize', 12);
title('速度跟踪误差 - 改进领导者跟随者系统 + 高质量观测器', 'FontSize', 14, 'FontWeight', 'bold');
legend('Location', 'northeast', 'FontSize', 10);
xlim([0, max(t_span)]);
ylim([0, max(vel_errors(:)) * 1.1]);

% 添加性能指标文本
max_vel_error = max(vel_errors(:));
if ~isempty(steady_start_idx)
    steady_max_vel = max(vel_errors(:, steady_start_idx:end));
    text(0.02, 0.95, sprintf('最大误差: %.2f m/s\n完全估计后最大误差: %.2f m/s', max_vel_error, steady_max_vel), ...
         'Units', 'normalized', 'FontSize', 10, 'BackgroundColor', 'white', ...
         'VerticalAlignment', 'top');
end

% 放大图 - 25-35秒关键时段
subplot(2, 2, 3);
hold on; grid on;
if ~isempty(zoom_idx_start) && ~isempty(zoom_idx_end)
    for i = 1:N_agents
        if i == 1
            label_name = '领导者';
        else
            label_name = sprintf('跟随者%d', i-1);
        end
        plot(t_span(zoom_idx_start:zoom_idx_end), vel_errors(i, zoom_idx_start:zoom_idx_end), ...
             'Color', colors(i, :), 'LineStyle', line_styles{i}, 'LineWidth', line_width, ...
             'DisplayName', label_name);
    end
    xlabel('时间 (s)', 'FontSize', 12);
    ylabel('速度跟踪误差 (m/s)', 'FontSize', 12);
    title(sprintf('速度跟踪误差放大图 (%d-%d秒)', zoom_start, zoom_end), 'FontSize', 12, 'FontWeight', 'bold');
    legend('Location', 'northeast', 'FontSize', 9);
    xlim([zoom_start, zoom_end]);
end

% 速度性能对比分析
subplot(2, 2, 4);
hold on; grid on;

if ~isempty(phase1_idx) && ~isempty(phase2_idx) && ~isempty(phase3_idx)
    phase1_vel_avg = mean(vel_errors(:, phase1_idx), 'all');
    phase2_vel_avg = mean(vel_errors(:, phase2_idx), 'all');
    phase3_vel_avg = mean(vel_errors(:, phase3_idx), 'all');
    
    phases = {'真实状态\n(0-5s)', '过渡期\n(5-15s)', '估计状态\n(15-50s)'};
    avg_vel_errors = [phase1_vel_avg, phase2_vel_avg, phase3_vel_avg];
    
    bar(avg_vel_errors, 'FaceColor', [0.9, 0.4, 0.2], 'EdgeColor', 'k', 'LineWidth', 1.5);
    set(gca, 'XTickLabel', phases);
    ylabel('平均速度误差 (m/s)', 'FontSize', 12);
    title('不同阶段速度性能对比', 'FontSize', 12, 'FontWeight', 'bold');
    
    % 添加数值标签
    for i = 1:length(avg_vel_errors)
        text(i, avg_vel_errors(i) + max(avg_vel_errors)*0.05, sprintf('%.3f m/s', avg_vel_errors(i)), ...
             'HorizontalAlignment', 'center', 'FontSize', 10, 'FontWeight', 'bold');
    end
end

%% 图3: 高质量观测器状态估计误差
figure('Name', '高质量观测器 - 状态估计误差分析', 'Position', [200, 200, 1200, 600]);

% 位置估计误差
subplot(1, 2, 1);
hold on; grid on;

% 添加过渡阶段标记
max_pos_est_error = 0;
for i = 1:N_agents
    pos_est_error = squeeze(sqrt(sum(estimation_error(1:3, i, :).^2, 1)));
    max_pos_est_error = max(max_pos_est_error, max(pos_est_error));
end

fill([5, transition_time, transition_time, 5], [0, 0, max_pos_est_error*1.1, max_pos_est_error*1.1], ...
     [0.9, 0.9, 0.9], 'FaceAlpha', 0.3, 'EdgeColor', 'none', 'DisplayName', '状态估计过渡期');

for i = 1:N_agents
    pos_est_error = squeeze(sqrt(sum(estimation_error(1:3, i, :).^2, 1)));
    if i == 1
        label_name = '领导者 (精确状态)';
        line_style_obs = ':';
    else
        label_name = sprintf('跟随者%d', i-1);
        line_style_obs = line_styles{i};
    end
    plot(t_span, pos_est_error, 'Color', colors(i, :), 'LineStyle', line_style_obs, ...
         'LineWidth', line_width, 'DisplayName', label_name);
end

xline(5, '--k', '开始过渡', 'LineWidth', 1.5, 'FontSize', 10);
xline(transition_time, '--k', '完全估计', 'LineWidth', 1.5, 'FontSize', 10);

xlabel('时间 (s)', 'FontSize', 12);
ylabel('位置估计误差 (m)', 'FontSize', 12);
title('高质量观测器 - 位置估计误差', 'FontSize', 14, 'FontWeight', 'bold');
legend('Location', 'northeast', 'FontSize', 10);
xlim([0, max(t_span)]);

% 速度估计误差
subplot(1, 2, 2);
hold on; grid on;

% 添加过渡阶段标记
max_vel_est_error = 0;
for i = 1:N_agents
    vel_est_error = squeeze(sqrt(sum(estimation_error(4:6, i, :).^2, 1)));
    max_vel_est_error = max(max_vel_est_error, max(vel_est_error));
end

fill([5, transition_time, transition_time, 5], [0, 0, max_vel_est_error*1.1, max_vel_est_error*1.1], ...
     [0.9, 0.9, 0.9], 'FaceAlpha', 0.3, 'EdgeColor', 'none', 'DisplayName', '状态估计过渡期');

for i = 1:N_agents
    vel_est_error = squeeze(sqrt(sum(estimation_error(4:6, i, :).^2, 1)));
    if i == 1
        label_name = '领导者 (精确状态)';
        line_style_obs = ':';
    else
        label_name = sprintf('跟随者%d', i-1);
        line_style_obs = line_styles{i};
    end
    plot(t_span, vel_est_error, 'Color', colors(i, :), 'LineStyle', line_style_obs, ...
         'LineWidth', line_width, 'DisplayName', label_name);
end

xline(5, '--k', '开始过渡', 'LineWidth', 1.5, 'FontSize', 10);
xline(transition_time, '--k', '完全估计', 'LineWidth', 1.5, 'FontSize', 10);

xlabel('时间 (s)', 'FontSize', 12);
ylabel('速度估计误差 (m/s)', 'FontSize', 12);
title('高质量观测器 - 速度估计误差', 'FontSize', 14, 'FontWeight', 'bold');
legend('Location', 'northeast', 'FontSize', 10);
xlim([0, max(t_span)]);

%% 图4: 3D轨迹图 + 编队形成过程
figure('Name', '改进领导者-跟随者 + 高质量观测器 - 3D轨迹与编队', 'Position', [250, 250, 1200, 800]);

% 3D轨迹图
subplot(2, 2, [1, 3]);
hold on; grid on;

% 绘制真实轨迹
for i = 1:N_agents
    if i == 1
        label_name = '领导者轨迹';
        line_width_3d = 3;
    else
        label_name = sprintf('跟随者%d轨迹', i-1);
        line_width_3d = 2;
    end
    plot3(squeeze(x_history(1, i, :)), squeeze(x_history(2, i, :)), squeeze(x_history(3, i, :)), ...
          'Color', colors(i, :), 'LineStyle', '-', 'LineWidth', line_width_3d, ...
          'DisplayName', label_name);
end

% 绘制期望轨迹 (虚线)
for i = 1:N_agents
    if i == 1
        label_name = '领导者期望';
    else
        label_name = sprintf('跟随者%d期望', i-1);
    end
    plot3(squeeze(x_desired(1, i, :)), squeeze(x_desired(2, i, :)), squeeze(x_desired(3, i, :)), ...
          '--', 'Color', colors(i, :) * 0.6, 'LineWidth', 1.5, 'DisplayName', label_name);
end

% 绘制初始位置
for i = 1:N_agents
    plot3(x_history(1, i, 1), x_history(2, i, 1), x_history(3, i, 1), 's', ...
          'Color', colors(i, :), 'MarkerSize', 10, 'MarkerFaceColor', 'white', ...
          'MarkerEdgeColor', colors(i, :), 'LineWidth', 2);
end

xlabel('X (m)', 'FontSize', 12);
ylabel('Y (m)', 'FontSize', 12);
zlabel('Z (m)', 'FontSize', 12);
title('改进领导者-跟随者编队控制 - 3D飞行轨迹', 'FontSize', 14, 'FontWeight', 'bold');
legend('Location', 'best', 'FontSize', 9);
view(45, 30);
axis equal;

% 编队形成过程 - 关键时刻快照
subplot(2, 2, 2);
hold on; grid on;
time_snapshots = [0, 10, 20, 30, 40];
snapshot_colors = [0.8, 0.8, 0.8; 0.6, 0.6, 0.6; 0.4, 0.4, 0.4; 0.2, 0.2, 0.2; 0.0, 0.0, 0.0];

for t_idx = 1:length(time_snapshots)
    t_snap = time_snapshots(t_idx);
    k_snap = find(t_span >= t_snap, 1);
    if ~isempty(k_snap)
        % 绘制无人机位置
        for i = 1:N_agents
            if i == 1
                marker_style = 'o';  % 领导者用圆形
                marker_size = 12;
            else
                marker_style = 's';  % 跟随者用方形
                marker_size = 10;
            end
            plot(x_history(1, i, k_snap), x_history(2, i, k_snap), marker_style, ...
                 'Color', snapshot_colors(t_idx, :), 'MarkerSize', marker_size, ...
                 'MarkerFaceColor', snapshot_colors(t_idx, :));
        end
        
        % 连接编队成员 (领导者到跟随者)
        if t_idx == length(time_snapshots)  % 最终编队用实线
            leader_pos = [x_history(1, 1, k_snap), x_history(2, 1, k_snap)];
            for i = 2:N_agents
                follower_pos = [x_history(1, i, k_snap), x_history(2, i, k_snap)];
                plot([leader_pos(1), follower_pos(1)], [leader_pos(2), follower_pos(2)], ...
                     '-', 'Color', snapshot_colors(t_idx, :), 'LineWidth', 1.5);
            end
        end
        
        % 时间标签
        if t_idx == 1
            text(x_history(1, 1, k_snap), x_history(2, 1, k_snap) + 0.5, ...
                 sprintf('%.0fs', t_snap), 'FontSize', 8, 'HorizontalAlignment', 'center');
        end
    end
end

xlabel('X (m)', 'FontSize', 12);
ylabel('Y (m)', 'FontSize', 12);
title('编队形成过程 (俯视图)', 'FontSize', 12, 'FontWeight', 'bold');
axis equal;

% 控制输入分析
subplot(2, 2, 4);
hold on; grid on;

% 添加过渡阶段标记
max_thrust = 0;
for i = 1:N_agents
    thrust_history = squeeze(u_history(1, i, :));
    max_thrust = max(max_thrust, max(thrust_history));
end

fill([5, transition_time, transition_time, 5], [0, 0, max_thrust*1.1, max_thrust*1.1], ...
     [0.9, 0.9, 0.9], 'FaceAlpha', 0.3, 'EdgeColor', 'none', 'DisplayName', '状态估计过渡期');

for i = 1:N_agents
    thrust_history = squeeze(u_history(1, i, :));
    if i == 1
        label_name = '领导者';
    else
        label_name = sprintf('跟随者%d', i-1);
    end
    plot(t_span, thrust_history, 'Color', colors(i, :), 'LineStyle', line_styles{i}, ...
         'LineWidth', 1.5, 'DisplayName', label_name);
end

xline(5, '--k', '开始过渡', 'LineWidth', 1.5, 'FontSize', 10);
xline(transition_time, '--k', '完全估计', 'LineWidth', 1.5, 'FontSize', 10);

xlabel('时间 (s)', 'FontSize', 12);
ylabel('推力 (N)', 'FontSize', 12);
title('控制输入 - 推力 (渐进式状态估计)', 'FontSize', 12, 'FontWeight', 'bold');
legend('Location', 'northeast', 'FontSize', 9);
xlim([0, max(t_span)]);

fprintf('📊 改进领导者-跟随者 + 高质量观测器可视化完成！\n');
fprintf('✅ 生成了4个主要图表\n');
fprintf('✅ 位置跟踪误差图 (包含渐进式集成分析)\n');
fprintf('✅ 速度跟踪误差图 (包含渐进式集成分析)\n');
fprintf('✅ 高质量观测器估计误差分析\n');
fprintf('✅ 3D飞行轨迹与编队形成过程\n');
fprintf('✅ 渐进式状态估计过渡可视化\n');

end
