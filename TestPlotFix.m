function TestPlotFix()
% 测试修复后的图表显示问题

clear; clc; close all;

fprintf('=== 测试图表显示修复 ===\n');

% 生成测试数据
t_span = 0:0.01:50;
N_followers = 4;

% 模拟跟踪误差数据
pos_error_norm = zeros(4, length(t_span));
vel_error_norm = zeros(4, length(t_span));

for i = 1:4
    % 位置误差：指数衰减 + 小幅振荡
    pos_error_norm(i, :) = (5-i) * exp(-0.1*t_span) .* (1 + 0.2*sin(0.5*t_span)) + 0.1;
    
    % 速度误差：更快衰减
    vel_error_norm(i, :) = (4-i+1) * exp(-0.15*t_span) .* (1 + 0.15*cos(0.8*t_span)) + 0.05;
end

% 颜色和线型设置
colors = [0, 0, 0;          % 领导者：黑色
          1, 0, 0;          % 跟随者1：红色
          0, 0.8, 0;        % 跟随者2：绿色
          0, 0.8, 0.8;      % 跟随者3：青色
          1, 0, 1];         % 跟随者4：品红色

line_styles = {'-', '-', '-', '-', '-'};
line_widths = [2.5, 2, 2, 2, 2];

%% 测试位置跟踪误差图
fprintf('生成位置跟踪误差图...\n');
figure('Name', '位置跟踪误差', 'Position', [100, 100, 800, 500]);

% 创建主图并保存句柄
main_ax = gca;
hold on; grid on;

% 绘制主图曲线
for i = 1:4
    plot(t_span, pos_error_norm(i, :), 'Color', colors(i+1, :), ...
         'LineStyle', line_styles{i+1}, 'LineWidth', line_widths(i+1), ...
         'DisplayName', sprintf('无人机_%d的位置跟踪误差', i));
end

% 设置主图属性
xlabel('时间(s)'); ylabel('位置跟踪误差响应 m_{pi}(t)');
title('位置跟踪误差 m_{pi}(t)');
legend('Location', 'northeast');
xlim([0, 50]); ylim([-1, 5]);

% 添加放大图 (内嵌子图)
zoom_ax = axes('Position', [0.15, 0.6, 0.25, 0.25]);
hold on; grid on;
t_zoom = t_span(t_span >= 5 & t_span <= 15);
for i = 1:4
    idx_zoom = find(t_span >= 5 & t_span <= 15);
    plot(t_zoom, pos_error_norm(i, idx_zoom), 'Color', colors(i+1, :), ...
         'LineStyle', line_styles{i+1}, 'LineWidth', line_widths(i+1));
end
xlim([5, 15]); ylim([0, 1.5]);
xlabel('时间(s)', 'FontSize', 8); 
ylabel('位置跟踪误差响应 m_{pi}(t)', 'FontSize', 8);
set(gca, 'FontSize', 8);

%% 测试速度跟踪误差图
fprintf('生成速度跟踪误差图...\n');
figure('Name', '速度跟踪误差', 'Position', [200, 200, 800, 500]);

% 创建主图并保存句柄
main_ax2 = gca;
hold on; grid on;

% 绘制主图曲线
for i = 1:4
    plot(t_span, vel_error_norm(i, :), 'Color', colors(i+1, :), ...
         'LineStyle', line_styles{i+1}, 'LineWidth', line_widths(i+1), ...
         'DisplayName', sprintf('无人机_%d的速度跟踪误差', i));
end

% 设置主图属性
xlabel('时间(s)'); ylabel('速度跟踪误差响应 m_{vi}(t)');
title('速度跟踪误差 m_{vi}(t)');
legend('Location', 'northeast');
xlim([0, 50]); ylim([-1, 4]);

% 添加放大图 (内嵌子图)
zoom_ax2 = axes('Position', [0.15, 0.6, 0.25, 0.25]);
hold on; grid on;
for i = 1:4
    idx_zoom = find(t_span >= 2 & t_span <= 12);
    plot(t_span(idx_zoom), vel_error_norm(i, idx_zoom), 'Color', colors(i+1, :), ...
         'LineStyle', line_styles{i+1}, 'LineWidth', line_widths(i+1));
end
xlim([2, 12]); ylim([0, 1]);
xlabel('时间(s)', 'FontSize', 8); 
ylabel('速度跟踪误差响应 m_{vi}(t)', 'FontSize', 8);
set(gca, 'FontSize', 8);

%% 测试简单图表（确保基本功能正常）
fprintf('生成简单测试图...\n');
figure('Name', '简单测试图', 'Position', [300, 300, 600, 400]);
hold on; grid on;

for i = 1:4
    plot(t_span, pos_error_norm(i, :), 'Color', colors(i+1, :), ...
         'LineWidth', 2, 'DisplayName', sprintf('无人机_%d', i));
end

xlabel('时间 (s)');
ylabel('位置跟踪误差 (m)');
title('简单测试图 - 验证图例显示');
legend('show', 'Location', 'best');
xlim([0, 50]);
ylim([0, 5]);

fprintf('\n✅ 图表生成完成！\n');
fprintf('请检查：\n');
fprintf('1. 图例是否正确显示\n');
fprintf('2. 曲线是否可见\n');
fprintf('3. 内嵌放大图是否正常\n');
fprintf('4. 中文标签是否正确显示\n');

end
