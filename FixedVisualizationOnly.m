function FixedVisualizationOnly(t_span, tracking_error_pos, tracking_error_vel, x_history, x_desired_history)
% 修复版可视化函数 - 专门解决曲线显示问题
% 确保所有4架无人机的曲线都清晰可见

fprintf('开始生成修复版可视化图表...\n');

%% 计算跟踪误差范数
N_followers = size(tracking_error_pos, 2);
pos_error_norm = zeros(N_followers, length(t_span));
vel_error_norm = zeros(N_followers, length(t_span));

for i = 1:N_followers
    for k = 1:length(t_span)
        pos_error_norm(i, k) = norm(tracking_error_pos(:, i, k));
        vel_error_norm(i, k) = norm(tracking_error_vel(:, i, k));
    end
end

% 数据验证
fprintf('数据验证:\n');
for i = 1:N_followers
    fprintf('无人机%d - 位置误差范围: [%.3f, %.3f], 速度误差范围: [%.3f, %.3f]\n', ...
            i, min(pos_error_norm(i, :)), max(pos_error_norm(i, :)), ...
            min(vel_error_norm(i, :)), max(vel_error_norm(i, :)));
end

%% 定义清晰的颜色和线型
colors = [
    1.0, 0.0, 0.0;    % 鲜红色 - 无人机1
    0.0, 0.8, 0.0;    % 鲜绿色 - 无人机2  
    0.0, 0.0, 1.0;    % 鲜蓝色 - 无人机3
    1.0, 0.0, 1.0;    % 鲜紫色 - 无人机4
];

line_styles = {'-', '--', '-.', ':'};
line_widths = [3, 3, 3, 3]; % 加粗线宽确保可见

%% 图1: 位置跟踪误差
figure('Name', '位置跟踪误差 m_pi(t)', 'Position', [100, 100, 1200, 800]);

% 主图
hold on; grid on;

% 绘制所有曲线，强制可见性
h_pos = [];
for i = 1:N_followers
    h = plot(t_span, pos_error_norm(i, :), ...
             'Color', colors(i, :), ...
             'LineStyle', line_styles{i}, ...
             'LineWidth', line_widths(i), ...
             'DisplayName', sprintf('无人机_%d的位置跟踪误差', i));
    h_pos = [h_pos, h];
    
    % 强制设置可见性
    set(h, 'Visible', 'on');
    
    % 验证数据非空
    if any(isnan(pos_error_norm(i, :))) || any(isinf(pos_error_norm(i, :)))
        fprintf('警告: 无人机%d位置误差数据包含NaN或Inf\n', i);
    end
end

xlabel('时间(s)', 'FontSize', 12); 
ylabel('位置跟踪误差响应 m_{pi}(t) (m)', 'FontSize', 12);
title('位置跟踪误差 m_{pi}(t)', 'FontSize', 14, 'FontWeight', 'bold');
legend(h_pos, 'Location', 'northeast', 'FontSize', 10);
xlim([0, 50]); 
ylim([0, max(pos_error_norm(:)) * 1.1]);

% 添加放大图 (内嵌子图) - 25-35秒
axes('Position', [0.15, 0.6, 0.25, 0.25]);
hold on; grid on;
t_zoom = t_span(t_span >= 25 & t_span <= 35);
idx_zoom = find(t_span >= 25 & t_span <= 35);

for i = 1:N_followers
    plot(t_zoom, pos_error_norm(i, idx_zoom), ...
         'Color', colors(i, :), ...
         'LineStyle', line_styles{i}, ...
         'LineWidth', line_widths(i));
end

xlim([25, 35]);
ylim([0, max(pos_error_norm(:, idx_zoom), [], 'all') * 1.1]);
xlabel('时间(s)', 'FontSize', 10);
ylabel('位置误差 (m)', 'FontSize', 10);
title('放大图: 25-35秒', 'FontSize', 12);

%% 图2: 速度跟踪误差
figure('Name', '速度跟踪误差 m_vi(t)', 'Position', [150, 150, 1200, 800]);

% 主图
hold on; grid on;

h_vel = [];
for i = 1:N_followers
    h = plot(t_span, vel_error_norm(i, :), ...
             'Color', colors(i, :), ...
             'LineStyle', line_styles{i}, ...
             'LineWidth', line_widths(i), ...
             'DisplayName', sprintf('无人机_%d的速度跟踪误差', i));
    h_vel = [h_vel, h];
    
    % 强制设置可见性
    set(h, 'Visible', 'on');
    
    % 验证数据非空
    if any(isnan(vel_error_norm(i, :))) || any(isinf(vel_error_norm(i, :)))
        fprintf('警告: 无人机%d速度误差数据包含NaN或Inf\n', i);
    end
end

xlabel('时间(s)', 'FontSize', 12); 
ylabel('速度跟踪误差响应 m_{vi}(t) (m/s)', 'FontSize', 12);
title('速度跟踪误差 m_{vi}(t)', 'FontSize', 14, 'FontWeight', 'bold');
legend(h_vel, 'Location', 'northeast', 'FontSize', 10);
xlim([0, 50]); 
ylim([0, max(vel_error_norm(:)) * 1.1]);

% 添加放大图 (内嵌子图) - 25-35秒
axes('Position', [0.15, 0.6, 0.25, 0.25]);
hold on; grid on;

for i = 1:N_followers
    plot(t_zoom, vel_error_norm(i, idx_zoom), ...
         'Color', colors(i, :), ...
         'LineStyle', line_styles{i}, ...
         'LineWidth', line_widths(i));
end

xlim([25, 35]);
ylim([0, max(vel_error_norm(:, idx_zoom), [], 'all') * 1.1]);
xlabel('时间(s)', 'FontSize', 10);
ylabel('速度误差 (m/s)', 'FontSize', 10);
title('放大图: 25-35秒', 'FontSize', 12);

%% 图3: 编队队形显示
figure('Name', '编队队形显示', 'Position', [200, 200, 1200, 600]);

% 显示不同时刻的编队队形
time_snapshots = [5, 15, 25, 35, 45]; % 选择几个关键时刻
colors_formation = [
    1.0, 0.0, 0.0;    % 红色 - 无人机1
    0.0, 0.8, 0.0;    % 绿色 - 无人机2
    0.0, 0.0, 1.0;    % 蓝色 - 无人机3
    1.0, 0.0, 1.0;    % 紫色 - 无人机4
];

for t_idx = 1:length(time_snapshots)
    subplot(2, 3, t_idx);
    hold on; grid on;

    t_snap = time_snapshots(t_idx);
    k_snap = find(t_span >= t_snap, 1); % 找到对应的时间索引

    if ~isempty(k_snap)
        % 绘制领导者位置
        leader_pos = x_history(1:3, 1, k_snap);
        plot3(leader_pos(1), leader_pos(2), leader_pos(3), ...
              'ko', 'MarkerSize', 12, 'MarkerFaceColor', 'k', ...
              'DisplayName', '领导者');

        % 绘制跟随者位置
        for i = 1:N_followers
            follower_pos = x_history(1:3, i+1, k_snap);
            plot3(follower_pos(1), follower_pos(2), follower_pos(3), ...
                  'o', 'Color', colors_formation(i, :), 'MarkerSize', 10, ...
                  'MarkerFaceColor', colors_formation(i, :), ...
                  'DisplayName', sprintf('无人机_%d', i));

            % 绘制连接线显示编队结构
            plot3([leader_pos(1), follower_pos(1)], ...
                  [leader_pos(2), follower_pos(2)], ...
                  [leader_pos(3), follower_pos(3)], ...
                  '--', 'Color', colors_formation(i, :), 'LineWidth', 1);
        end

        % 设置图形属性
        xlabel('X (m)', 'FontSize', 10);
        ylabel('Y (m)', 'FontSize', 10);
        zlabel('Z (m)', 'FontSize', 10);
        title(sprintf('t = %.0fs', t_snap), 'FontSize', 12);
        view(45, 30);
        axis equal;

        % 设置合适的坐标轴范围
        all_pos = squeeze(x_history(1:3, :, k_snap));
        x_range = [min(all_pos(1, :))-2, max(all_pos(1, :))+2];
        y_range = [min(all_pos(2, :))-2, max(all_pos(2, :))+2];
        z_range = [min(all_pos(3, :))-2, max(all_pos(3, :))+2];
        xlim(x_range); ylim(y_range); zlim(z_range);
    end
end

% 添加总图例
subplot(2, 3, 6);
hold on;
plot3(0, 0, 0, 'ko', 'MarkerSize', 12, 'MarkerFaceColor', 'k', 'DisplayName', '领导者');
for i = 1:N_followers
    plot3(0, 0, 0, 'o', 'Color', colors_formation(i, :), 'MarkerSize', 10, ...
          'MarkerFaceColor', colors_formation(i, :), 'DisplayName', sprintf('无人机_%d', i));
end
legend('Location', 'best', 'FontSize', 12);
axis off;
title('图例', 'FontSize', 14);

%% 图4: 3D飞行轨迹 (增强编队队形显示)
figure('Name', '3D飞行轨迹与编队队形', 'Position', [250, 250, 1200, 900]);

% 绘制轨迹
hold on; grid on;

% 领导者轨迹
leader_traj = squeeze(x_history(1:3, 1, :));
h_leader = plot3(leader_traj(1, :), leader_traj(2, :), leader_traj(3, :), ...
                 'k-', 'LineWidth', 4, 'DisplayName', '领导者轨迹');

% 跟随者实际轨迹
h_followers = [];
for i = 1:N_followers
    follower_traj = squeeze(x_history(1:3, i+1, :));
    h = plot3(follower_traj(1, :), follower_traj(2, :), follower_traj(3, :), ...
              'Color', colors(i, :), 'LineStyle', '-', ...
              'LineWidth', 3, 'DisplayName', sprintf('无人机_%d轨迹', i));
    h_followers = [h_followers, h];
end

% 添加编队队形连接线 (动态显示)
% 选择几个关键时刻显示编队形状
formation_times = [10, 20, 30, 40]; % 关键时刻
formation_colors = [0.7, 0.7, 0.7]; % 灰色连接线

for t_idx = 1:length(formation_times)
    t_form = formation_times(t_idx);
    k_form = find(t_span >= t_form, 1);

    if ~isempty(k_form)
        % 获取该时刻所有无人机位置
        leader_pos = x_history(1:3, 1, k_form);

        % 绘制编队连接线
        for i = 1:N_followers
            follower_pos = x_history(1:3, i+1, k_form);

            % 领导者到跟随者的连接线
            plot3([leader_pos(1), follower_pos(1)], ...
                  [leader_pos(2), follower_pos(2)], ...
                  [leader_pos(3), follower_pos(3)], ...
                  '--', 'Color', formation_colors, 'LineWidth', 1.5, ...
                  'HandleVisibility', 'off');
        end

        % 跟随者之间的连接线 (形成四边形)
        follower_positions = zeros(3, N_followers);
        for i = 1:N_followers
            follower_positions(:, i) = x_history(1:3, i+1, k_form);
        end

        % 连接相邻跟随者
        connections = [1,2; 2,4; 4,3; 3,1]; % 形成矩形连接
        for c = 1:size(connections, 1)
            i1 = connections(c, 1);
            i2 = connections(c, 2);
            plot3([follower_positions(1, i1), follower_positions(1, i2)], ...
                  [follower_positions(2, i1), follower_positions(2, i2)], ...
                  [follower_positions(3, i1), follower_positions(3, i2)], ...
                  ':', 'Color', formation_colors, 'LineWidth', 1, ...
                  'HandleVisibility', 'off');
        end

        % 在该时刻标记无人机位置
        plot3(leader_pos(1), leader_pos(2), leader_pos(3), ...
              'ko', 'MarkerSize', 8, 'MarkerFaceColor', 'k', ...
              'HandleVisibility', 'off');

        for i = 1:N_followers
            follower_pos = x_history(1:3, i+1, k_form);
            plot3(follower_pos(1), follower_pos(2), follower_pos(3), ...
                  'o', 'Color', colors(i, :), 'MarkerSize', 6, ...
                  'MarkerFaceColor', colors(i, :), 'HandleVisibility', 'off');
        end
    end
end

% 标记起始和结束位置
% 起始位置
start_leader = leader_traj(:, 1);
plot3(start_leader(1), start_leader(2), start_leader(3), ...
      'ks', 'MarkerSize', 12, 'MarkerFaceColor', 'g', ...
      'DisplayName', '起始位置');

% 结束位置
end_leader = leader_traj(:, end);
plot3(end_leader(1), end_leader(2), end_leader(3), ...
      'kd', 'MarkerSize', 12, 'MarkerFaceColor', 'r', ...
      'DisplayName', '结束位置');

% 设置图形属性
xlabel('X (m)', 'FontSize', 12);
ylabel('Y (m)', 'FontSize', 12);
zlabel('Z (m)', 'FontSize', 12);
title('无人机编队3D飞行轨迹与队形显示', 'FontSize', 14, 'FontWeight', 'bold');

% 创建图例
legend_handles = [h_leader, h_followers];
legend_labels = {'领导者轨迹'};
for i = 1:N_followers
    legend_labels{end+1} = sprintf('无人机_%d轨迹', i);
end
legend_labels{end+1} = '起始位置';
legend_labels{end+1} = '结束位置';

% 添加起始和结束位置到图例句柄
legend_handles = [legend_handles, findobj(gca, 'Marker', 's'), findobj(gca, 'Marker', 'd')];
legend(legend_handles, legend_labels, 'Location', 'best', 'FontSize', 10);

view(45, 30);
axis equal;
grid on;

%% 验证图形对象
fprintf('\n图形验证:\n');
fprintf('位置误差图: %d条曲线\n', length(h_pos));
fprintf('速度误差图: %d条曲线\n', length(h_vel));

% 检查每条曲线的可见性
for i = 1:length(h_pos)
    visibility = get(h_pos(i), 'Visible');
    fprintf('位置误差曲线%d可见性: %s\n', i, visibility);
end

for i = 1:length(h_vel)
    visibility = get(h_vel(i), 'Visible');
    fprintf('速度误差曲线%d可见性: %s\n', i, visibility);
end

%% 性能统计
fprintf('\n=== 修复版性能统计 ===\n');
for i = 1:N_followers
    max_pos = max(pos_error_norm(i, :));
    max_vel = max(vel_error_norm(i, :));
    
    % 40-45秒误差
    idx_40_45 = find(t_span >= 40 & t_span <= 45);
    if ~isempty(idx_40_45)
        max_pos_40_45 = max(pos_error_norm(i, idx_40_45));
        max_vel_40_45 = max(vel_error_norm(i, idx_40_45));
        
        fprintf('无人机%d: 最大位置误差=%.3fm, 最大速度误差=%.3fm/s\n', i, max_pos, max_vel);
        fprintf('        40-45s: 位置误差=%.3fm, 速度误差=%.3fm/s\n', max_pos_40_45, max_vel_40_45);
    else
        fprintf('无人机%d: 最大位置误差=%.3fm, 最大速度误差=%.3fm/s\n', i, max_pos, max_vel);
    end
end

fprintf('\n✅ 修复版可视化完成！\n');
fprintf('📊 生成了4个主要图表\n');
fprintf('📈 位置跟踪误差图 (放大图25-35秒)\n');
fprintf('📈 速度跟踪误差图 (放大图25-35秒)\n');
fprintf('🛩️ 编队队形显示 (5个关键时刻)\n');
fprintf('🌐 3D飞行轨迹图\n');
fprintf('🎯 所有无人机曲线强制可见\n');
fprintf('✨ 使用高对比度颜色和加粗线条\n');

end
