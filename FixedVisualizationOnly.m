function FixedVisualizationOnly(t_span, tracking_error_pos, tracking_error_vel, x_history, x_desired_history)
% 修复版可视化函数 - 专门解决曲线显示问题
% 确保所有4架无人机的曲线都清晰可见

fprintf('开始生成修复版可视化图表...\n');

%% 计算跟踪误差范数
N_followers = size(tracking_error_pos, 2);
pos_error_norm = zeros(N_followers, length(t_span));
vel_error_norm = zeros(N_followers, length(t_span));

for i = 1:N_followers
    for k = 1:length(t_span)
        pos_error_norm(i, k) = norm(tracking_error_pos(:, i, k));
        vel_error_norm(i, k) = norm(tracking_error_vel(:, i, k));
    end
end

% 数据验证
fprintf('数据验证:\n');
for i = 1:N_followers
    fprintf('无人机%d - 位置误差范围: [%.3f, %.3f], 速度误差范围: [%.3f, %.3f]\n', ...
            i, min(pos_error_norm(i, :)), max(pos_error_norm(i, :)), ...
            min(vel_error_norm(i, :)), max(vel_error_norm(i, :)));
end

%% 定义清晰的颜色和线型
colors = [
    1.0, 0.0, 0.0;    % 鲜红色 - 无人机1
    0.0, 0.8, 0.0;    % 鲜绿色 - 无人机2  
    0.0, 0.0, 1.0;    % 鲜蓝色 - 无人机3
    1.0, 0.0, 1.0;    % 鲜紫色 - 无人机4
];

line_styles = {'-', '--', '-.', ':'};
line_widths = [3, 3, 3, 3]; % 加粗线宽确保可见

%% 图1: 位置跟踪误差
figure('Name', '位置跟踪误差 m_pi(t)', 'Position', [100, 100, 1200, 800]);

% 主图
hold on; grid on;

% 绘制所有曲线，强制可见性
h_pos = [];
for i = 1:N_followers
    h = plot(t_span, pos_error_norm(i, :), ...
             'Color', colors(i, :), ...
             'LineStyle', line_styles{i}, ...
             'LineWidth', line_widths(i), ...
             'DisplayName', sprintf('无人机_%d的位置跟踪误差', i));
    h_pos = [h_pos, h];
    
    % 强制设置可见性
    set(h, 'Visible', 'on');
    
    % 验证数据非空
    if any(isnan(pos_error_norm(i, :))) || any(isinf(pos_error_norm(i, :)))
        fprintf('警告: 无人机%d位置误差数据包含NaN或Inf\n', i);
    end
end

xlabel('时间(s)', 'FontSize', 12); 
ylabel('位置跟踪误差响应 m_{pi}(t) (m)', 'FontSize', 12);
title('位置跟踪误差 m_{pi}(t)', 'FontSize', 14, 'FontWeight', 'bold');
legend(h_pos, 'Location', 'northeast', 'FontSize', 10);
xlim([0, 50]); 
ylim([0, max(pos_error_norm(:)) * 1.1]);

% 添加放大图 (内嵌子图) - 15-25秒
axes('Position', [0.15, 0.6, 0.25, 0.25]);
hold on; grid on;
t_zoom = t_span(t_span >= 15 & t_span <= 25);
idx_zoom = find(t_span >= 15 & t_span <= 25);

for i = 1:N_followers
    plot(t_zoom, pos_error_norm(i, idx_zoom), ...
         'Color', colors(i, :), ...
         'LineStyle', line_styles{i}, ...
         'LineWidth', line_widths(i));
end

xlim([15, 25]); 
ylim([0, max(pos_error_norm(:, idx_zoom), [], 'all') * 1.1]);
xlabel('时间(s)', 'FontSize', 10); 
ylabel('位置误差 (m)', 'FontSize', 10);
title('放大图: 15-25秒', 'FontSize', 12);

%% 图2: 速度跟踪误差
figure('Name', '速度跟踪误差 m_vi(t)', 'Position', [150, 150, 1200, 800]);

% 主图
hold on; grid on;

h_vel = [];
for i = 1:N_followers
    h = plot(t_span, vel_error_norm(i, :), ...
             'Color', colors(i, :), ...
             'LineStyle', line_styles{i}, ...
             'LineWidth', line_widths(i), ...
             'DisplayName', sprintf('无人机_%d的速度跟踪误差', i));
    h_vel = [h_vel, h];
    
    % 强制设置可见性
    set(h, 'Visible', 'on');
    
    % 验证数据非空
    if any(isnan(vel_error_norm(i, :))) || any(isinf(vel_error_norm(i, :)))
        fprintf('警告: 无人机%d速度误差数据包含NaN或Inf\n', i);
    end
end

xlabel('时间(s)', 'FontSize', 12); 
ylabel('速度跟踪误差响应 m_{vi}(t) (m/s)', 'FontSize', 12);
title('速度跟踪误差 m_{vi}(t)', 'FontSize', 14, 'FontWeight', 'bold');
legend(h_vel, 'Location', 'northeast', 'FontSize', 10);
xlim([0, 50]); 
ylim([0, max(vel_error_norm(:)) * 1.1]);

% 添加放大图 (内嵌子图) - 15-25秒
axes('Position', [0.15, 0.6, 0.25, 0.25]);
hold on; grid on;

for i = 1:N_followers
    plot(t_zoom, vel_error_norm(i, idx_zoom), ...
         'Color', colors(i, :), ...
         'LineStyle', line_styles{i}, ...
         'LineWidth', line_widths(i));
end

xlim([15, 25]); 
ylim([0, max(vel_error_norm(:, idx_zoom), [], 'all') * 1.1]);
xlabel('时间(s)', 'FontSize', 10); 
ylabel('速度误差 (m/s)', 'FontSize', 10);
title('放大图: 15-25秒', 'FontSize', 12);

%% 图3: 3D飞行轨迹
figure('Name', '3D飞行轨迹', 'Position', [200, 200, 1000, 800]);

% 绘制轨迹
hold on; grid on;

% 领导者轨迹
leader_traj = squeeze(x_history(1:3, 1, :));
plot3(leader_traj(1, :), leader_traj(2, :), leader_traj(3, :), ...
      'k-', 'LineWidth', 4, 'DisplayName', '领导者轨迹');

% 跟随者实际轨迹
for i = 1:N_followers
    follower_traj = squeeze(x_history(1:3, i+1, :));
    plot3(follower_traj(1, :), follower_traj(2, :), follower_traj(3, :), ...
          'Color', colors(i, :), 'LineStyle', line_styles{i}, ...
          'LineWidth', 3, 'DisplayName', sprintf('无人机_%d实际轨迹', i));
end

% 跟随者期望轨迹（虚线）
for i = 1:N_followers
    desired_traj = squeeze(x_desired_history(1:3, i+1, :));
    plot3(desired_traj(1, :), desired_traj(2, :), desired_traj(3, :), ...
          'Color', colors(i, :), 'LineStyle', ':', 'LineWidth', 2, ...
          'DisplayName', sprintf('无人机_%d期望轨迹', i));
end

xlabel('X (m)', 'FontSize', 12);
ylabel('Y (m)', 'FontSize', 12);
zlabel('Z (m)', 'FontSize', 12);
title('无人机编队3D飞行轨迹', 'FontSize', 14, 'FontWeight', 'bold');
legend('Location', 'best', 'FontSize', 10);
view(45, 30);
axis equal;

%% 验证图形对象
fprintf('\n图形验证:\n');
fprintf('位置误差图: %d条曲线\n', length(h_pos));
fprintf('速度误差图: %d条曲线\n', length(h_vel));

% 检查每条曲线的可见性
for i = 1:length(h_pos)
    visibility = get(h_pos(i), 'Visible');
    fprintf('位置误差曲线%d可见性: %s\n', i, visibility);
end

for i = 1:length(h_vel)
    visibility = get(h_vel(i), 'Visible');
    fprintf('速度误差曲线%d可见性: %s\n', i, visibility);
end

%% 性能统计
fprintf('\n=== 修复版性能统计 ===\n');
for i = 1:N_followers
    max_pos = max(pos_error_norm(i, :));
    max_vel = max(vel_error_norm(i, :));
    
    % 40-45秒误差
    idx_40_45 = find(t_span >= 40 & t_span <= 45);
    if ~isempty(idx_40_45)
        max_pos_40_45 = max(pos_error_norm(i, idx_40_45));
        max_vel_40_45 = max(vel_error_norm(i, idx_40_45));
        
        fprintf('无人机%d: 最大位置误差=%.3fm, 最大速度误差=%.3fm/s\n', i, max_pos, max_vel);
        fprintf('        40-45s: 位置误差=%.3fm, 速度误差=%.3fm/s\n', max_pos_40_45, max_vel_40_45);
    else
        fprintf('无人机%d: 最大位置误差=%.3fm, 最大速度误差=%.3fm/s\n', i, max_pos, max_vel);
    end
end

fprintf('\n✅ 修复版可视化完成！\n');
fprintf('📊 生成了3个主要图表\n');
fprintf('🔍 放大图显示15-25秒\n');
fprintf('🎯 所有无人机曲线强制可见\n');
fprintf('✨ 使用高对比度颜色和加粗线条\n');

end
