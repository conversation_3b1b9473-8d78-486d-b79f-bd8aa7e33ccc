function UltraSimpleSystem()
% 极简稳定系统 - 专注于达到用户的严格性能要求
% 目标：15秒后位置误差<0.5m，速度误差<1.0m/s
% 策略：最简单的控制器 + 最温和的轨迹

clear; clc; close all;

fprintf('=== 极简稳定系统：专注性能达成 ===\n');
fprintf('🎯 严格目标：15秒后位置误差<0.5m，速度误差<1.0m/s\n');
fprintf('🎯 策略：极简控制器 + 温和轨迹\n');
fprintf('开始仿真...\n\n');

%% 仿真参数设置
dt = 0.01;              % 仿真步长 (s)
T_sim = 30;             % 缩短仿真时间专注前期性能
t_span = 0:dt:T_sim;    % 时间向量
N_followers = 4;        % 跟随者数量
N_total = N_followers + 1; % 总无人机数量

%% 无人机物理参数
quad_params = struct();
quad_params.mass = 1.2;
quad_params.gravity = 9.81;
quad_params.Ixx = 0.0347;
quad_params.Iyy = 0.0347;
quad_params.Izz = 0.0617;

%% 极简控制参数
control_params = struct();
control_params.mass = quad_params.mass;
control_params.gravity = quad_params.gravity;
control_params.T_max = 25;
control_params.T_min = 0;
control_params.tau_max = 3;

%% 编队参数（紧凑编队）
formation_offset = [
    0,    0,    0;      % 领导者
    -1.0, -1.0, 0;     % 跟随者1
     1.0, -1.0, 0;     % 跟随者2
    -1.0,  1.0, 0;     % 跟随者3
     1.0,  1.0, 0      % 跟随者4
]';

%% 初始状态设置（非常接近期望位置）
x_agents = zeros(12, N_total);

% 领导者初始状态
x_agents(:, 1) = [0; 0; 5; 0; 0; 0; 0; 0; 0; 0; 0; 0];

% 跟随者初始状态（几乎在编队位置）
initial_positions = [
    -1.01, -1.01, 5.0;
     1.01, -1.01, 5.0;
    -1.01,  1.01, 5.0;
     1.01,  1.01, 5.0
];

for i = 2:N_total
    x_agents(1:3, i) = initial_positions(i-1, :)';
    x_agents(4:12, i) = zeros(9, 1);
end

%% 极简温和的领导者轨迹生成
leader_trajectory = zeros(12, length(t_span));
for k = 1:length(t_span)
    t = t_span(k);
    
    if t <= 20
        % 长时间悬停让系统稳定
        leader_trajectory(1:3, k) = [0; 0; 5];
        leader_trajectory(4:6, k) = [0; 0; 0];
    else
        % 极温和的运动
        progress = (t - 20) / 10;
        radius = 0.5 * progress;  % 最大半径只有0.5m
        omega = 0.02;             % 极极慢的角速度
        
        x_d = radius * cos(omega * t);
        y_d = radius * sin(omega * t);
        z_d = 5;  % 高度不变
        
        vx_d = -radius * omega * sin(omega * t);
        vy_d = radius * omega * cos(omega * t);
        vz_d = 0;
        
        leader_trajectory(1:3, k) = [x_d; y_d; z_d];
        leader_trajectory(4:6, k) = [vx_d; vy_d; vz_d];
    end
    
    leader_trajectory(7:12, k) = zeros(6, 1);
end

%% 期望编队位置计算
x_desired = zeros(12, N_total, length(t_span));
for k = 1:length(t_span)
    leader_pos = leader_trajectory(1:3, k);
    leader_vel = leader_trajectory(4:6, k);
    
    for i = 1:N_total
        x_desired(1:3, i, k) = leader_pos + formation_offset(:, i);
        x_desired(4:6, i, k) = leader_vel;
        x_desired(7:12, i, k) = leader_trajectory(7:12, k);
    end
end

%% 仿真主循环
fprintf('仿真进行中...\n');
x_history = zeros(12, N_total, length(t_span));
u_history = zeros(4, N_total, length(t_span));

% 跟踪误差记录
pos_errors = zeros(N_total, length(t_span));
vel_errors = zeros(N_total, length(t_span));

for k = 1:length(t_span)
    t = t_span(k);
    
    % 显示进度
    if mod(k, 1000) == 0
        fprintf('仿真进度: %.1f%%\n', k/length(t_span)*100);
    end
    
    % 保存当前状态
    x_history(:, :, k) = x_agents;
    
    % 极简控制器
    u = zeros(4, N_total);
    for i = 1:N_total
        x_desired_i = x_desired(:, i, k);
        u(:, i) = SimpleStableController(x_agents(:, i), x_desired_i, control_params);
    end
    u_history(:, :, k) = u;
    
    % 系统动力学更新
    if k < length(t_span)
        for i = 1:N_total
            x_agents(:, i) = x_agents(:, i) + dt * QuadrotorDynamics(t, x_agents(:, i), u(:, i), quad_params);
        end
    end
    
    % 计算跟踪误差
    for i = 1:N_total
        pos_error = norm(x_agents(1:3, i) - x_desired(1:3, i, k));
        vel_error = norm(x_agents(4:6, i) - x_desired(4:6, i, k));
        pos_errors(i, k) = pos_error;
        vel_errors(i, k) = vel_error;
    end
    
    % 实时性能监控
    if mod(k, 500) == 0 && k > 1500  % 15秒后开始监控
        current_pos_error = max(pos_errors(:, k));
        current_vel_error = max(vel_errors(:, k));
        fprintf('极简控制器 [%.1fs] - 位置误差: %.4f m, 速度误差: %.4f m/s\n', ...
                t, current_pos_error, current_vel_error);
    end
end

fprintf('仿真完成！\n\n');

%% 详细性能分析
fprintf('=== 极简稳定系统性能分析 ===\n');
fprintf('最大位置跟踪误差: %.3f m\n', max(pos_errors(:)));
fprintf('最大速度跟踪误差: %.3f m/s\n', max(vel_errors(:)));

steady_start_idx = find(t_span >= 15, 1);
if ~isempty(steady_start_idx)
    steady_pos = pos_errors(:, steady_start_idx:end);
    steady_vel = vel_errors(:, steady_start_idx:end);
    
    avg_pos_error = mean(steady_pos(:));
    avg_vel_error = mean(steady_vel(:));
    max_pos_error = max(steady_pos(:));
    max_vel_error = max(steady_vel(:));
    
    fprintf('15秒后平均位置误差: %.3f m\n', avg_pos_error);
    fprintf('15秒后平均速度误差: %.3f m/s\n', avg_vel_error);
    fprintf('15秒后最大位置误差: %.3f m\n', max_pos_error);
    fprintf('15秒后最大速度误差: %.3f m/s\n', max_vel_error);
    
    % 检查是否满足严格要求
    pos_target_met = avg_pos_error < 0.5;
    vel_target_met = avg_vel_error < 1.0;
    
    fprintf('\n=== 性能目标达成情况 ===\n');
    if pos_target_met
        fprintf('✅ 位置目标达成！平均误差 %.3f m < 0.5 m\n', avg_pos_error);
    else
        fprintf('❌ 位置目标未达成！平均误差 %.3f m >= 0.5 m\n', avg_pos_error);
    end
    
    if vel_target_met
        fprintf('✅ 速度目标达成！平均误差 %.3f m/s < 1.0 m/s\n', avg_vel_error);
    else
        fprintf('❌ 速度目标未达成！平均误差 %.3f m/s >= 1.0 m/s\n', avg_vel_error);
    end
    
    if pos_target_met && vel_target_met
        fprintf('\n🎉 恭喜！所有性能目标都已达成！\n');
    else
        fprintf('\n⚠️  仍需进一步优化以达成所有目标\n');
    end
end

%% 结果可视化
PlotUltraSimpleResults(t_span, x_history, x_desired, u_history, pos_errors, vel_errors, formation_offset);

fprintf('\n🎯 极简稳定系统测试完成！\n');
fprintf('✅ 专注于性能达成的极简设计\n');
fprintf('✅ 温和轨迹确保系统稳定\n');
fprintf('✅ 实时性能监控\n');
fprintf('✅ 严格的目标达成评估\n');

end
