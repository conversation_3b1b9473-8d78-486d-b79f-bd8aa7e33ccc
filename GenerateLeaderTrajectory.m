function [pos, vel, acc] = GenerateLeaderTrajectory(t)
% 生成领导者的复杂3D轨迹
% 输入参数:
%   t - 当前时间 (s)
% 输出参数:
%   pos - 位置 [x; y; z] (m)
%   vel - 速度 [vx; vy; vz] (m/s)
%   acc - 加速度 [ax; ay; az] (m/s^2)

% 轨迹参数
A_xy = 3;           % 水平面圆周运动半径 (m)
omega_xy = 0.2;     % 水平面角频率 (rad/s)
center_x = 8;       % 圆心x坐标 (m)
center_y = 8;       % 圆心y坐标 (m)

% 垂直运动参数
z_start = 9;        % 起始高度 (m)
z_climb_rate = 0.15; % 爬升速率 (m/s)
z_max = 18;         % 最大高度 (m)

%% 水平轨迹 (螺旋上升)
% X轴轨迹
x = center_x + A_xy * cos(omega_xy * t);
vx = -A_xy * omega_xy * sin(omega_xy * t);
ax = -A_xy * omega_xy^2 * cos(omega_xy * t);

% Y轴轨迹
y = center_y + A_xy * sin(omega_xy * t);
vy = A_xy * omega_xy * cos(omega_xy * t);
ay = -A_xy * omega_xy^2 * sin(omega_xy * t);

%% 垂直轨迹 (分阶段)
if t <= 20
    % 阶段1: 螺旋上升 (0-20秒)
    z = z_start + z_climb_rate * t;
    vz = z_climb_rate;
    az = 0;
elseif t <= 30
    % 阶段2: 保持高度 (20-30秒)
    z = z_start + z_climb_rate * 20;
    vz = 0;
    az = 0;
elseif t <= 35
    % 阶段3: 继续上升 (30-35秒)
    t_phase = t - 30;
    z = z_start + z_climb_rate * 20 + z_climb_rate * t_phase;
    vz = z_climb_rate;
    az = 0;
elseif t <= 40
    % 阶段4: 平滑减速过渡 (35-40秒)
    t_transition = t - 35;
    z_35 = z_start + z_climb_rate * 25; % 35秒时的高度

    % 使用五次多项式实现更平滑的过渡
    tau = t_transition / 5; % 归一化时间 [0,1]
    % 五次多项式: 6τ^5 - 15τ^4 + 10τ^3
    s = 6*tau^5 - 15*tau^4 + 10*tau^3;
    s_dot = (6*5*tau^4 - 15*4*tau^3 + 10*3*tau^2) / 5; % 对时间的导数

    z = z_35 + (z_max - z_35) * s;
    vz = z_climb_rate * (1 - s) + (z_max - z_35) * s_dot;
    az = 0;
else
    % 阶段5: 保持最大高度 (40秒后)
    z = z_max;
    vz = 0;
    az = 0;
end

%% 输出结果
pos = [x; y; z];
vel = [vx; vy; vz];
acc = [ax; ay; az];

end
