function u = PrecisionConvergenceController(x, x_desired, control_params)
% 精确收敛控制器 - 实现10-15秒内误差收敛到零
% 使用时间自适应增益和精确积分控制

persistent integral_pos integral_att prev_error_pos prev_error_att
persistent controller_time

% 初始化持久变量
if isempty(integral_pos)
    integral_pos = zeros(3, 1);
    integral_att = zeros(3, 1);
    prev_error_pos = zeros(3, 1);
    prev_error_att = zeros(3, 1);
    controller_time = 0;
end

% 更新控制器时间
dt = 0.01;
controller_time = controller_time + dt;

% 提取状态变量
pos = x(1:3);           % 位置 [x; y; z]
vel = x(4:6);           % 速度 [vx; vy; vz]
euler = x(7:9);         % 欧拉角 [phi; theta; psi]
omega = x(10:12);       % 角速度 [p; q; r]

% 期望状态
pos_d = x_desired(1:3);
vel_d = x_desired(4:6);

% 位置和速度误差
error_pos = pos - pos_d;
error_vel = vel - vel_d;
error_pos_norm = norm(error_pos);
error_vel_norm = norm(error_vel);

% 时间自适应增益调度
if controller_time <= 5.0
    % 0-5秒：快速响应阶段
    k_pos_base = 4.0;
    k_vel_base = 3.2;
    k_int_base = 0.8;
elseif controller_time <= 10.0
    % 5-10秒：平衡收敛阶段
    k_pos_base = 3.5;
    k_vel_base = 2.8;
    k_int_base = 1.2;
elseif controller_time <= 15.0
    % 10-15秒：精确收敛阶段
    k_pos_base = 5.0;  % 提高精度
    k_vel_base = 4.0;  % 增强阻尼
    k_int_base = 2.0;  % 强化积分
else
    % 15秒后：高精度保持阶段
    k_pos_base = 6.0;  % 最高精度
    k_vel_base = 5.0;  % 最强阻尼
    k_int_base = 2.5;  % 最强积分
end

% 误差自适应增益调整
if error_pos_norm > 1.0
    % 大误差：快速收敛
    k_pos = k_pos_base * 1.2;
    k_vel = k_vel_base * 1.1;
    k_int = k_int_base * 0.7;
elseif error_pos_norm > 0.3
    % 中等误差：标准控制
    k_pos = k_pos_base;
    k_vel = k_vel_base;
    k_int = k_int_base;
elseif error_pos_norm > 0.1
    % 小误差：精确控制
    k_pos = k_pos_base * 1.1;
    k_vel = k_vel_base * 1.3;  % 增强阻尼
    k_int = k_int_base * 1.2;
else
    % 微小误差：超精确控制
    k_pos = k_pos_base * 1.3;
    k_vel = k_vel_base * 1.5;  % 最强阻尼
    k_int = k_int_base * 1.5;
end

% 精确积分控制
if controller_time > 8.0  % 8秒后开始强化积分
    if error_pos_norm < 1.0
        integral_pos = integral_pos + error_pos * dt;
    else
        integral_pos = integral_pos * 0.98;
    end
else
    if error_pos_norm < 0.5
        integral_pos = integral_pos + error_pos * dt;
    else
        integral_pos = integral_pos * 0.95;
    end
end

% 动态积分限制
if controller_time > 10.0
    max_integral = 0.5;  % 10秒后放宽积分限制
else
    max_integral = 0.3;
end

integral_pos = max(-max_integral, min(max_integral, integral_pos));

% 微分项计算（用于抑制振荡）
derivative_pos = (error_pos - prev_error_pos) / dt;
if controller_time > 10.0
    k_der = 0.8;  % 10秒后增强微分控制
else
    k_der = 0.4;
end

% 位置控制律 - 时间自适应PID控制
acc_cmd = -k_pos * error_pos - k_vel * error_vel - k_int * integral_pos - k_der * derivative_pos;

% 推力计算
phi = euler(1); theta = euler(2);
cos_phi = cos(phi); cos_theta = cos(theta);

% 避免除零
if abs(cos_phi * cos_theta) < 0.1
    cos_phi = sign(cos_phi) * 0.1;
    cos_theta = sign(cos_theta) * 0.1;
end

T = control_params.mass * (acc_cmd(3) + control_params.gravity) / (cos_phi * cos_theta);

% 推力限幅
T = max(control_params.T_min, min(control_params.T_max, T));

% 期望姿态计算
g = control_params.gravity;
phi_d = (acc_cmd(1) * sin(euler(3)) - acc_cmd(2) * cos(euler(3))) / g;
theta_d = (acc_cmd(1) * cos(euler(3)) + acc_cmd(2) * sin(euler(3))) / g;
psi_d = 0;

% 姿态角限制
max_angle = pi/4;  % 45度限制（放宽以允许更大控制力）
phi_d = max(-max_angle, min(max_angle, phi_d));
theta_d = max(-max_angle, min(max_angle, theta_d));

% 姿态误差
euler_d = [phi_d; theta_d; psi_d];
error_att = euler - euler_d;
error_att_norm = norm(error_att);

% 时间自适应姿态增益
if controller_time <= 5.0
    k_att_base = 4.0;
    k_omega_base = 2.5;
    k_int_att_base = 0.3;
elseif controller_time <= 10.0
    k_att_base = 4.5;
    k_omega_base = 3.0;
    k_int_att_base = 0.4;
elseif controller_time <= 15.0
    k_att_base = 5.5;  % 精确收敛阶段
    k_omega_base = 4.0;
    k_int_att_base = 0.6;
else
    k_att_base = 6.0;  % 高精度保持
    k_omega_base = 4.5;
    k_int_att_base = 0.8;
end

% 姿态误差自适应调整
if error_att_norm > 0.3
    k_att = k_att_base * 1.1;
    k_omega = k_omega_base * 1.1;
    k_int_att = k_int_att_base * 0.8;
else
    k_att = k_att_base;
    k_omega = k_omega_base * 1.2;  % 增强阻尼
    k_int_att = k_int_att_base;
end

% 姿态积分项
if controller_time > 8.0
    if error_att_norm < 0.3
        integral_att = integral_att + error_att * dt;
    else
        integral_att = integral_att * 0.98;
    end
else
    if error_att_norm < 0.2
        integral_att = integral_att + error_att * dt;
    else
        integral_att = integral_att * 0.95;
    end
end

% 姿态积分限制
if controller_time > 10.0
    max_integral_att = 0.3;
else
    max_integral_att = 0.2;
end

integral_att = max(-max_integral_att, min(max_integral_att, integral_att));

% 姿态微分项
derivative_att = (error_att - prev_error_att) / dt;
if controller_time > 10.0
    k_der_att = 0.6;
else
    k_der_att = 0.3;
end

% 姿态控制律
omega_d = zeros(3, 1);
error_omega = omega - omega_d;

tau = -k_att * error_att - k_omega * error_omega - k_int_att * integral_att - k_der_att * derivative_att;

% 力矩限幅
tau = max(-control_params.tau_max, min(control_params.tau_max, tau));

% 更新历史误差
prev_error_pos = error_pos;
prev_error_att = error_att;

% 输出控制量
u = [T; tau];

% 调试信息
persistent debug_counter
if isempty(debug_counter)
    debug_counter = 0;
end
debug_counter = debug_counter + 1;

if mod(debug_counter, 500) == 0  % 每5秒输出一次
    fprintf('精确控制器 [%.1fs] - 位置误差: %.4f m, 速度误差: %.4f m/s\n', ...
            controller_time, error_pos_norm, error_vel_norm);
end

end
