function [x_dot] = QuadrotorDynamics(t, x, u, params)
% 四旋翼无人机动力学模型
% 输入参数:
%   t - 时间
%   x - 状态向量 [位置(3), 速度(3), 欧拉角(3), 角速度(3)]' (12x1)
%   u - 控制输入 [总推力, 滚转力矩, 俯仰力矩, 偏航力矩]' (4x1)
%   params - 无人机参数结构体
% 输出:
%   x_dot - 状态导数 (12x1)

% 提取状态变量
pos = x(1:3);      % 位置 [x, y, z]
vel = x(4:6);      % 速度 [vx, vy, vz]
euler = x(7:9);    % 欧拉角 [phi, theta, psi] (滚转, 俯仰, 偏航)
omega = x(10:12);  % 角速度 [p, q, r]

% 提取控制输入
T = u(1);          % 总推力
tau_phi = u(2);    % 滚转力矩
tau_theta = u(3);  % 俯仰力矩
tau_psi = u(4);    % 偏航力矩

% 无人机参数
m = params.mass;           % 质量 (kg)
g = params.gravity;        % 重力加速度 (m/s^2)
Ixx = params.Ixx;         % x轴转动惯量 (kg*m^2)
Iyy = params.Iyy;         % y轴转动惯量 (kg*m^2)
Izz = params.Izz;         % z轴转动惯量 (kg*m^2)

% 欧拉角
phi = euler(1);    % 滚转角
theta = euler(2);  % 俯仰角
psi = euler(3);    % 偏航角

% 角速度
p = omega(1);      % 滚转角速度
q = omega(2);      % 俯仰角速度
r = omega(3);      % 偏航角速度

% 旋转矩阵 (机体坐标系到惯性坐标系)
R = [cos(theta)*cos(psi), sin(phi)*sin(theta)*cos(psi) - cos(phi)*sin(psi), cos(phi)*sin(theta)*cos(psi) + sin(phi)*sin(psi);
     cos(theta)*sin(psi), sin(phi)*sin(theta)*sin(psi) + cos(phi)*cos(psi), cos(phi)*sin(theta)*sin(psi) - sin(phi)*cos(psi);
     -sin(theta),         sin(phi)*cos(theta),                              cos(phi)*cos(theta)];

% 位置动力学: 位置导数 = 速度
pos_dot = vel;

% 平移动力学: F = ma
% 机体坐标系下的推力向量
F_body = [0; 0; T];
% 转换到惯性坐标系
F_inertial = R * F_body;
% 加上重力
F_total = F_inertial + [0; 0; -m*g];
% 加速度
vel_dot = F_total / m;

% 欧拉角动力学
% 欧拉角导数与角速度的关系
W = [1, sin(phi)*tan(theta), cos(phi)*tan(theta);
     0, cos(phi),            -sin(phi);
     0, sin(phi)/cos(theta), cos(phi)/cos(theta)];
euler_dot = W * omega;

% 旋转动力学: 欧拉方程
% I * omega_dot + omega × (I * omega) = tau
I_omega = [Ixx*p; Iyy*q; Izz*r];
omega_cross_I_omega = [q*Izz*r - r*Iyy*q;
                       r*Ixx*p - p*Izz*r;
                       p*Iyy*q - q*Ixx*p];

tau = [tau_phi; tau_theta; tau_psi];
omega_dot = [tau_phi/Ixx - (Izz - Iyy)*q*r/Ixx;
             tau_theta/Iyy - (Ixx - Izz)*p*r/Iyy;
             tau_psi/Izz - (Iyy - Ixx)*p*q/Izz];

% 组合状态导数
x_dot = [pos_dot; vel_dot; euler_dot; omega_dot];

end
