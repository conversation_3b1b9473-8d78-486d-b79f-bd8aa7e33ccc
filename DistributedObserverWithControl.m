function [x_hat_dot, x_hat] = DistributedObserverWithControl(agent_id, x_agents, y_agents, x_hat_agents, neighbors, params, u_i)
% 分布式观测器 - 支持控制输入的版本
% 专门为领导者-跟随者系统设计
% 输入参数:
%   agent_id - 当前无人机ID
%   x_agents - 所有无人机真实状态矩阵 (12 x N) [仅用于仿真]
%   y_agents - 所有无人机测量输出矩阵 (6 x N) [位置和姿态测量]
%   x_hat_agents - 所有无人机状态估计矩阵 (12 x N)
%   neighbors - 邻接矩阵，表示通信拓扑 (N x N)
%   params - 观测器参数结构体
%   u_i - 当前无人机的控制输入 (4x1)
% 输出:
%   x_hat_dot - 状态估计导数 (12x1)
%   x_hat - 当前状态估计 (12x1)

% 获取当前无人机的状态估计和测量
x_hat_i = x_hat_agents(:, agent_id);
y_i = y_agents(:, agent_id);

% 观测器参数
L = params.observer_gain;          % 观测器增益矩阵 (12x6)
k_consensus_obs = params.k_consensus_obs;  % 观测器一致性增益
A = params.A;                      % 系统矩阵 (线性化后)
B = params.B;                      % 输入矩阵
C = params.C;                      % 输出矩阵 (6x12)

% 如果没有提供控制输入，使用零输入
if nargin < 7 || isempty(u_i)
    u_i = zeros(4, 1);
end

% 预测的输出
y_hat_i = C * x_hat_i;

% 输出误差
e_y_i = y_i - y_hat_i;

%% 一致性项计算
N = size(x_hat_agents, 2);
consensus_term = zeros(12, 1);

% 计算与邻居的一致性误差
neighbor_count = 0;
for j = 1:N
    if neighbors(agent_id, j) == 1 && j ~= agent_id
        % 状态估计一致性误差
        consensus_error = x_hat_agents(:, j) - x_hat_i;
        consensus_term = consensus_term + consensus_error;
        neighbor_count = neighbor_count + 1;
    end
end

% 归一化一致性项
if neighbor_count > 0
    consensus_term = consensus_term / neighbor_count;
end

%% 分布式观测器动态方程
% 标准观测器项 + 一致性项
x_hat_dot = A * x_hat_i + B * u_i + L * e_y_i + k_consensus_obs * consensus_term;

% 输出当前状态估计
x_hat = x_hat_i;

%% 数值稳定性保护
% 限制状态估计的范围，防止发散
max_pos = 50;    % 最大位置 (m)
max_vel = 20;    % 最大速度 (m/s)
max_att = pi/3;  % 最大姿态角 (rad)
max_omega = 5;   % 最大角速度 (rad/s)

% 位置限制
x_hat_dot(1:3) = max(-max_pos, min(max_pos, x_hat_dot(1:3)));
% 速度限制
x_hat_dot(4:6) = max(-max_vel, min(max_vel, x_hat_dot(4:6)));
% 姿态限制
x_hat_dot(7:9) = max(-max_att, min(max_att, x_hat_dot(7:9)));
% 角速度限制
x_hat_dot(10:12) = max(-max_omega, min(max_omega, x_hat_dot(10:12)));

%% 调试信息 (可选)
persistent debug_counter
if isempty(debug_counter)
    debug_counter = zeros(1, 10);  % 支持最多10个无人机
end

if agent_id <= length(debug_counter)
    debug_counter(agent_id) = debug_counter(agent_id) + 1;
    
    % 每5秒输出一次调试信息
    if mod(debug_counter(agent_id), 500) == 0
        est_error_norm = norm(e_y_i);
        consensus_norm = norm(consensus_term);
        fprintf('观测器[UAV%d] [%.1fs] - 估计误差: %.4f, 一致性项: %.4f, 邻居数: %d\n', ...
                agent_id, debug_counter(agent_id)*0.01, est_error_norm, consensus_norm, neighbor_count);
    end
end

end
