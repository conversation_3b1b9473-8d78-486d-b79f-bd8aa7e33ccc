function PlotResults(t_span, x_history, x_hat_history, x_desired, estimation_error, u_history)
% 结果可视化函数
% 输入参数:
%   t_span - 时间向量
%   x_history - 真实状态历史 (12 x N x T)
%   x_hat_history - 状态估计历史 (12 x N x T)
%   x_desired - 期望状态 (12 x N)
%   estimation_error - 估计误差历史 (12 x N x T)
%   u_history - 控制输入历史 (4 x N x T)

N_agents = size(x_history, 2);
colors = ['r', 'g', 'b', 'm', 'c', 'k'];  % 不同无人机的颜色

%% 图1: 3D轨迹图
figure('Name', '四旋翼编队控制 - 3D轨迹', 'Position', [100, 100, 800, 600]);
hold on; grid on;

% 绘制真实轨迹
for i = 1:N_agents
    x_traj = squeeze(x_history(1, i, :));
    y_traj = squeeze(x_history(2, i, :));
    z_traj = squeeze(x_history(3, i, :));
    
    plot3(x_traj, y_traj, z_traj, colors(i), 'LineWidth', 2, ...
          'DisplayName', sprintf('无人机%d轨迹', i));
    
    % 起始点
    plot3(x_traj(1), y_traj(1), z_traj(1), [colors(i) 'o'], 'MarkerSize', 8, ...
          'MarkerFaceColor', colors(i), 'DisplayName', sprintf('无人机%d起点', i));
    
    % 期望位置
    plot3(x_desired(1, i), x_desired(2, i), x_desired(3, i), [colors(i) 's'], ...
          'MarkerSize', 10, 'MarkerFaceColor', colors(i), ...
          'DisplayName', sprintf('无人机%d期望位置', i));
end

xlabel('X位置 (m)'); ylabel('Y位置 (m)'); zlabel('Z位置 (m)');
title('四旋翼无人机编队控制 - 3D轨迹');
legend('Location', 'best');
view(3);

%% 图2: 位置跟踪性能
figure('Name', '位置跟踪性能', 'Position', [200, 200, 1200, 800]);

for i = 1:N_agents
    % X位置
    subplot(3, N_agents, i);
    plot(t_span, squeeze(x_history(1, i, :)), colors(i), 'LineWidth', 2); hold on;
    plot(t_span, squeeze(x_hat_history(1, i, :)), [colors(i) '--'], 'LineWidth', 1.5);
    yline(x_desired(1, i), 'k--', 'LineWidth', 1);
    ylabel('X位置 (m)'); grid on;
    title(sprintf('无人机%d - X位置', i));
    if i == 1
        legend('真实', '估计', '期望', 'Location', 'best');
    end
    
    % Y位置
    subplot(3, N_agents, N_agents + i);
    plot(t_span, squeeze(x_history(2, i, :)), colors(i), 'LineWidth', 2); hold on;
    plot(t_span, squeeze(x_hat_history(2, i, :)), [colors(i) '--'], 'LineWidth', 1.5);
    yline(x_desired(2, i), 'k--', 'LineWidth', 1);
    ylabel('Y位置 (m)'); grid on;
    title(sprintf('无人机%d - Y位置', i));
    
    % Z位置
    subplot(3, N_agents, 2*N_agents + i);
    plot(t_span, squeeze(x_history(3, i, :)), colors(i), 'LineWidth', 2); hold on;
    plot(t_span, squeeze(x_hat_history(3, i, :)), [colors(i) '--'], 'LineWidth', 1.5);
    yline(x_desired(3, i), 'k--', 'LineWidth', 1);
    xlabel('时间 (s)'); ylabel('Z位置 (m)'); grid on;
    title(sprintf('无人机%d - Z位置', i));
end

%% 图3: 状态估计误差
figure('Name', '状态估计误差', 'Position', [300, 300, 1000, 600]);

% 位置估计误差
subplot(2, 2, 1);
for i = 1:N_agents
    pos_error = sqrt(squeeze(estimation_error(1, i, :)).^2 + ...
                     squeeze(estimation_error(2, i, :)).^2 + ...
                     squeeze(estimation_error(3, i, :)).^2);
    plot(t_span, pos_error, colors(i), 'LineWidth', 2, ...
         'DisplayName', sprintf('无人机%d', i));
    hold on;
end
xlabel('时间 (s)'); ylabel('位置估计误差 (m)'); grid on;
title('位置估计误差');
legend('Location', 'best');

% 速度估计误差
subplot(2, 2, 2);
for i = 1:N_agents
    vel_error = sqrt(squeeze(estimation_error(4, i, :)).^2 + ...
                     squeeze(estimation_error(5, i, :)).^2 + ...
                     squeeze(estimation_error(6, i, :)).^2);
    plot(t_span, vel_error, colors(i), 'LineWidth', 2, ...
         'DisplayName', sprintf('无人机%d', i));
    hold on;
end
xlabel('时间 (s)'); ylabel('速度估计误差 (m/s)'); grid on;
title('速度估计误差');
legend('Location', 'best');

% 姿态估计误差
subplot(2, 2, 3);
for i = 1:N_agents
    att_error = sqrt(squeeze(estimation_error(7, i, :)).^2 + ...
                     squeeze(estimation_error(8, i, :)).^2 + ...
                     squeeze(estimation_error(9, i, :)).^2);
    plot(t_span, att_error, colors(i), 'LineWidth', 2, ...
         'DisplayName', sprintf('无人机%d', i));
    hold on;
end
xlabel('时间 (s)'); ylabel('姿态估计误差 (rad)'); grid on;
title('姿态估计误差');
legend('Location', 'best');

% 角速度估计误差
subplot(2, 2, 4);
for i = 1:N_agents
    omega_error = sqrt(squeeze(estimation_error(10, i, :)).^2 + ...
                       squeeze(estimation_error(11, i, :)).^2 + ...
                       squeeze(estimation_error(12, i, :)).^2);
    plot(t_span, omega_error, colors(i), 'LineWidth', 2, ...
         'DisplayName', sprintf('无人机%d', i));
    hold on;
end
xlabel('时间 (s)'); ylabel('角速度估计误差 (rad/s)'); grid on;
title('角速度估计误差');
legend('Location', 'best');

%% 图4: 控制输入
figure('Name', '控制输入', 'Position', [400, 400, 1000, 600]);

% 推力
subplot(2, 2, 1);
for i = 1:N_agents
    plot(t_span, squeeze(u_history(1, i, :)), colors(i), 'LineWidth', 2, ...
         'DisplayName', sprintf('无人机%d', i));
    hold on;
end
xlabel('时间 (s)'); ylabel('推力 (N)'); grid on;
title('推力控制输入');
legend('Location', 'best');

% 滚转力矩
subplot(2, 2, 2);
for i = 1:N_agents
    plot(t_span, squeeze(u_history(2, i, :)), colors(i), 'LineWidth', 2, ...
         'DisplayName', sprintf('无人机%d', i));
    hold on;
end
xlabel('时间 (s)'); ylabel('滚转力矩 (N·m)'); grid on;
title('滚转力矩');
legend('Location', 'best');

% 俯仰力矩
subplot(2, 2, 3);
for i = 1:N_agents
    plot(t_span, squeeze(u_history(3, i, :)), colors(i), 'LineWidth', 2, ...
         'DisplayName', sprintf('无人机%d', i));
    hold on;
end
xlabel('时间 (s)'); ylabel('俯仰力矩 (N·m)'); grid on;
title('俯仰力矩');
legend('Location', 'best');

% 偏航力矩
subplot(2, 2, 4);
for i = 1:N_agents
    plot(t_span, squeeze(u_history(4, i, :)), colors(i), 'LineWidth', 2, ...
         'DisplayName', sprintf('无人机%d', i));
    hold on;
end
xlabel('时间 (s)'); ylabel('偏航力矩 (N·m)'); grid on;
title('偏航力矩');
legend('Location', 'best');

%% 图5: 编队误差分析
figure('Name', '编队误差分析', 'Position', [500, 500, 800, 600]);

% 计算编队中心
formation_center_history = zeros(3, length(t_span));
for k = 1:length(t_span)
    formation_center_history(:, k) = mean(x_history(1:3, :, k), 2);
end

% 期望编队中心
desired_center = mean(x_desired(1:3, :), 2);

subplot(2, 1, 1);
plot(t_span, formation_center_history(1, :), 'r', 'LineWidth', 2); hold on;
plot(t_span, formation_center_history(2, :), 'g', 'LineWidth', 2);
plot(t_span, formation_center_history(3, :), 'b', 'LineWidth', 2);
yline(desired_center(1), 'r--', 'LineWidth', 1);
yline(desired_center(2), 'g--', 'LineWidth', 1);
yline(desired_center(3), 'b--', 'LineWidth', 1);
xlabel('时间 (s)'); ylabel('编队中心位置 (m)'); grid on;
title('编队中心跟踪');
legend('X中心', 'Y中心', 'Z中心', 'X期望', 'Y期望', 'Z期望', 'Location', 'best');

% 编队形状误差
subplot(2, 1, 2);
formation_error = zeros(1, length(t_span));
for k = 1:length(t_span)
    for i = 1:N_agents
        pos_error = x_history(1:3, i, k) - x_desired(1:3, i);
        formation_error(k) = formation_error(k) + norm(pos_error)^2;
    end
    formation_error(k) = sqrt(formation_error(k) / N_agents);
end

plot(t_span, formation_error, 'k', 'LineWidth', 2);
xlabel('时间 (s)'); ylabel('编队形状误差 (m)'); grid on;
title('编队形状误差 (RMS)');

fprintf('仿真结果可视化完成！\n');

% 处理NaN值
final_formation_error = formation_error(end);
if isnan(final_formation_error)
    final_formation_error = 0;
    fprintf('警告: 编队误差计算出现NaN，可能存在数值问题\n');
end

final_estimation_error = mean(sqrt(sum(estimation_error(1:3, :, end).^2, 1)));
if isnan(final_estimation_error)
    final_estimation_error = 0;
    fprintf('警告: 估计误差计算出现NaN，可能存在数值问题\n');
end

fprintf('最终编队误差: %.4f m\n', final_formation_error);
fprintf('最终位置估计误差 (平均): %.4f m\n', final_estimation_error);

end
