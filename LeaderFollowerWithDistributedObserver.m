function LeaderFollowerWithDistributedObserver()
% 领导者-跟随者系统 + 分布式观测器
% 结合稳定的领导者-跟随者架构和分布式状态观测器
% 解决振荡问题并实现分布式状态估计

clear; clc; close all;

fprintf('=== 领导者-跟随者 + 分布式观测器系统 ===\n');
fprintf('🎯 领导者-跟随者编队控制\n');
fprintf('🎯 分布式状态观测器\n');
fprintf('🎯 解决振荡问题\n');
fprintf('🎯 中文可视化\n');
fprintf('开始仿真...\n\n');

%% 仿真参数设置
dt = 0.01;              % 仿真步长 (s)
T_sim = 50;             % 仿真时间 (s)
t_span = 0:dt:T_sim;    % 时间向量
N_followers = 4;        % 跟随者数量
N_total = N_followers + 1; % 总无人机数量 (包括领导者)

%% 无人机物理参数
quad_params = struct();
quad_params.mass = 1.2;         % 质量 (kg)
quad_params.gravity = 9.81;     % 重力加速度 (m/s^2)
quad_params.Ixx = 0.0347;       % x轴转动惯量 (kg*m^2)
quad_params.Iyy = 0.0347;       % y轴转动惯量 (kg*m^2)
quad_params.Izz = 0.0617;       % z轴转动惯量 (kg*m^2)

%% 稳定控制参数
control_params = struct();
control_params.k_pos = 2.2;         % 位置控制增益 (稍微降低避免振荡)
control_params.k_vel = 2.0;         % 速度控制增益 (增加阻尼)
control_params.k_att = 2.8;         % 姿态控制增益
control_params.k_omega = 1.6;       % 角速度控制增益
control_params.mass = quad_params.mass;
control_params.gravity = quad_params.gravity;
control_params.T_max = 25;          % 最大推力 (N)
control_params.T_min = 0;           % 最小推力 (N)
control_params.tau_max = 3;         % 最大力矩 (N*m)

%% 分布式观测器参数
observer_params = struct();
% 系统矩阵 (简化线性化模型)
observer_params.A = zeros(12, 12);
observer_params.A(1:3, 4:6) = eye(3);    % 位置-速度关系
observer_params.A(7:9, 10:12) = eye(3);  % 姿态-角速度关系

% 输入矩阵 (简化)
observer_params.B = zeros(12, 4);
observer_params.B(6, 1) = 1/quad_params.mass;  % 推力影响z方向加速度
observer_params.B(10:12, 2:4) = diag([1/quad_params.Ixx, 1/quad_params.Iyy, 1/quad_params.Izz]);

% 输出矩阵 (测量位置和姿态)
observer_params.C = zeros(6, 12);
observer_params.C(1:3, 1:3) = eye(3);    % 位置测量
observer_params.C(4:6, 7:9) = eye(3);    % 姿态测量

% 观测器增益矩阵 (保守设计)
observer_params.observer_gain = zeros(12, 6);
observer_params.observer_gain(1:3, 1:3) = 1.5 * eye(3);   % 位置观测增益
observer_params.observer_gain(4:6, 1:3) = 0.8 * eye(3);   % 速度观测增益
observer_params.observer_gain(7:9, 4:6) = 1.2 * eye(3);   % 姿态观测增益
observer_params.observer_gain(10:12, 4:6) = 0.6 * eye(3); % 角速度观测增益

% 一致性增益 (保守设置)
observer_params.k_consensus_obs = 0.15;

%% 通信拓扑 (领导者-跟随者 + 跟随者间通信)
neighbors = zeros(N_total, N_total);
% 领导者 (UAV 1) 不接收信息，只发送
neighbors(2:end, 1) = 1;  % 所有跟随者都能接收领导者信息

% 跟随者间的通信 (环形拓扑)
for i = 2:N_total
    next_follower = i + 1;
    if next_follower > N_total
        next_follower = 2;  % 回到第一个跟随者
    end
    neighbors(i, next_follower) = 1;
    neighbors(next_follower, i) = 1;  % 双向通信
end

%% 编队参数 (相对于领导者的偏移)
formation_offset = [
    0,    0,    0;      % UAV_1: 领导者 (无偏移)
    -2.0, -2.0, 0;     % UAV_2: 左后方
     2.0, -2.0, 0;     % UAV_3: 右后方
    -2.0,  2.0, 0;     % UAV_4: 左前方
     2.0,  2.0, 0      % UAV_5: 右前方
];
formation_offset = formation_offset';  % 转置为 (3x5)

%% 初始状态设置
x_agents = zeros(12, N_total);
x_hat_agents = zeros(12, N_total);  % 状态估计

% 领导者初始状态
x_agents(:, 1) = [0; 0; 5; 0; 0; 0; 0; 0; 0; 0; 0; 0];

% 跟随者初始状态 (分散在领导者周围)
initial_positions = [
    -3, -3, 4.5;   % UAV_2
     3, -3, 4.5;   % UAV_3
    -3,  3, 5.5;   % UAV_4
     3,  3, 5.5    % UAV_5
];

for i = 2:N_total
    x_agents(1:3, i) = initial_positions(i-1, :)';
    x_agents(4:12, i) = zeros(9, 1);
end

% 初始状态估计 (添加小的估计误差)
for i = 1:N_total
    x_hat_agents(:, i) = x_agents(:, i) + 0.1 * randn(12, 1);
end

%% 领导者轨迹生成 (平滑螺旋轨迹)
leader_trajectory = zeros(12, length(t_span));
for k = 1:length(t_span)
    t = t_span(k);
    
    if t <= 10
        % 0-10秒: 悬停稳定
        leader_trajectory(1:3, k) = [0; 0; 5];
        leader_trajectory(4:6, k) = [0; 0; 0];
    elseif t <= 30
        % 10-30秒: 螺旋上升
        progress = (t - 10) / 20;
        radius = 3 * progress;
        height = 5 + 2 * progress;
        omega = 0.3;
        
        x_d = radius * cos(omega * t);
        y_d = radius * sin(omega * t);
        z_d = height;
        
        vx_d = -radius * omega * sin(omega * t);
        vy_d = radius * omega * cos(omega * t);
        vz_d = 0.1;
        
        leader_trajectory(1:3, k) = [x_d; y_d; z_d];
        leader_trajectory(4:6, k) = [vx_d; vy_d; vz_d];
    else
        % 30-50秒: 稳定圆形轨迹
        radius = 3;
        height = 7;
        omega = 0.3;
        
        x_d = radius * cos(omega * t);
        y_d = radius * sin(omega * t);
        z_d = height;
        
        vx_d = -radius * omega * sin(omega * t);
        vy_d = radius * omega * cos(omega * t);
        vz_d = 0;
        
        leader_trajectory(1:3, k) = [x_d; y_d; z_d];
        leader_trajectory(4:6, k) = [vx_d; vy_d; vz_d];
    end
    
    % 姿态保持水平
    leader_trajectory(7:12, k) = zeros(6, 1);
end

%% 期望编队位置计算
x_desired = zeros(12, N_total, length(t_span));
for k = 1:length(t_span)
    leader_pos = leader_trajectory(1:3, k);
    leader_vel = leader_trajectory(4:6, k);
    
    for i = 1:N_total
        x_desired(1:3, i, k) = leader_pos + formation_offset(:, i);
        x_desired(4:6, i, k) = leader_vel;  % 跟随者与领导者同速度
        x_desired(7:12, i, k) = leader_trajectory(7:12, k);
    end
end

%% 仿真主循环
fprintf('仿真进行中...\n');
x_history = zeros(12, N_total, length(t_span));
x_hat_history = zeros(12, N_total, length(t_span));
u_history = zeros(4, N_total, length(t_span));
estimation_error = zeros(12, N_total, length(t_span));

% 跟踪误差记录
pos_errors = zeros(N_total, length(t_span));
vel_errors = zeros(N_total, length(t_span));

for k = 1:length(t_span)
    t = t_span(k);
    
    % 显示进度
    if mod(k, 1000) == 0
        fprintf('仿真进度: %.1f%%\n', k/length(t_span)*100);
    end
    
    % 保存当前状态
    x_history(:, :, k) = x_agents;
    x_hat_history(:, :, k) = x_hat_agents;
    
    % 生成测量输出 (位置 + 姿态，添加测量噪声)
    y_agents = zeros(6, N_total);
    for i = 1:N_total
        y_agents(1:3, i) = x_agents(1:3, i) + 0.01 * randn(3, 1);  % 位置测量噪声
        y_agents(4:6, i) = x_agents(7:9, i) + 0.005 * randn(3, 1); % 姿态测量噪声
    end
    
    % 分布式观测器更新
    x_hat_dot_agents = zeros(12, N_total);
    for i = 1:N_total
        [x_hat_dot_agents(:, i), ~] = DistributedObserverWithControl(i, x_agents, y_agents, x_hat_agents, neighbors, observer_params, u_history(:, i, max(1, k-1)));
    end
    
    % 控制器计算 (使用状态估计)
    u_agents = zeros(4, N_total);
    for i = 1:N_total
        x_desired_i = x_desired(:, i, k);
        u_agents(:, i) = LeaderFollowerController(i, x_hat_agents, x_desired_i, control_params, t);
    end
    
    % 保存控制输入
    u_history(:, :, k) = u_agents;
    
    % 系统动力学更新
    if k < length(t_span)
        for i = 1:N_total
            x_agents(:, i) = x_agents(:, i) + dt * QuadrotorDynamics(t, x_agents(:, i), u_agents(:, i), quad_params);
            x_hat_agents(:, i) = x_hat_agents(:, i) + dt * x_hat_dot_agents(:, i);
        end
    end
    
    % 计算跟踪误差和估计误差
    for i = 1:N_total
        pos_error = norm(x_agents(1:3, i) - x_desired(1:3, i, k));
        vel_error = norm(x_agents(4:6, i) - x_desired(4:6, i, k));
        pos_errors(i, k) = pos_error;
        vel_errors(i, k) = vel_error;
        
        estimation_error(:, i, k) = x_agents(:, i) - x_hat_agents(:, i);
    end
end

fprintf('仿真完成！\n\n');

%% 性能分析
fprintf('=== 系统性能分析 ===\n');
fprintf('最大位置跟踪误差: %.3f m\n', max(pos_errors(:)));
fprintf('最大速度跟踪误差: %.3f m/s\n', max(vel_errors(:)));

% 15秒后的稳态性能
steady_start_idx = find(t_span >= 15, 1);
if ~isempty(steady_start_idx)
    steady_pos_errors = pos_errors(:, steady_start_idx:end);
    steady_vel_errors = vel_errors(:, steady_start_idx:end);
    fprintf('15秒后平均位置误差: %.3f m\n', mean(steady_pos_errors(:)));
    fprintf('15秒后平均速度误差: %.3f m/s\n', mean(steady_vel_errors(:)));
end

% 40-45秒关键时段分析
fprintf('\n=== 40-45秒关键时段分析 ===\n');
critical_start_idx = find(t_span >= 40, 1);
critical_end_idx = find(t_span >= 45, 1);
if ~isempty(critical_start_idx) && ~isempty(critical_end_idx)
    critical_pos_errors = pos_errors(:, critical_start_idx:critical_end_idx);
    critical_vel_errors = vel_errors(:, critical_start_idx:critical_end_idx);
    fprintf('最大位置误差: %.3f m\n', max(critical_pos_errors(:)));
    fprintf('最大速度误差: %.3f m/s\n', max(critical_vel_errors(:)));
end

%% 结果可视化
PlotLeaderFollowerObserverResults(t_span, x_history, x_hat_history, x_desired, ...
                                  estimation_error, u_history, pos_errors, vel_errors, leader_trajectory, formation_offset);

fprintf('\n🎉 领导者-跟随者 + 分布式观测器系统测试完成！\n');
fprintf('✅ 领导者-跟随者编队控制\n');
fprintf('✅ 分布式状态观测器\n');
fprintf('✅ 振荡问题优化\n');
fprintf('✅ 中文可视化显示\n');

end
