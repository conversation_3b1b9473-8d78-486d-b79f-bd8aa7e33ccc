function u = AntiOscillationController(x, x_desired, control_params)
% 抗振荡控制器 - 专门设计用于消除振荡，实现平滑收敛
% 使用经典PID + 低通滤波 + 自适应阻尼

persistent integral_pos integral_att controller_time
persistent prev_error_pos prev_error_vel
persistent filtered_error_pos filtered_error_vel
persistent alpha  % 低通滤波器参数

% 初始化持久变量
if isempty(integral_pos)
    integral_pos = zeros(3, 1);
    integral_att = zeros(3, 1);
    controller_time = 0;
    prev_error_pos = zeros(3, 1);
    prev_error_vel = zeros(3, 1);
    filtered_error_pos = zeros(3, 1);
    filtered_error_vel = zeros(3, 1);
    alpha = 0.8;  % 低通滤波器参数，0.8表示较强的滤波
end

% 更新控制器时间
dt = 0.01;
controller_time = controller_time + dt;

% 提取状态变量
pos = x(1:3);           % 位置 [x; y; z]
vel = x(4:6);           % 速度 [vx; vy; vz]
euler = x(7:9);         % 欧拉角 [phi; theta; psi]
omega = x(10:12);       % 角速度 [p; q; r]

% 期望状态
pos_d = x_desired(1:3);
vel_d = x_desired(4:6);

% 位置和速度误差
error_pos = pos - pos_d;
error_vel = vel - vel_d;

% 低通滤波器 - 消除高频振荡
filtered_error_pos = alpha * filtered_error_pos + (1 - alpha) * error_pos;
filtered_error_vel = alpha * filtered_error_vel + (1 - alpha) * error_vel;

% 使用滤波后的误差进行控制
error_pos_norm = norm(filtered_error_pos);
error_vel_norm = norm(filtered_error_vel);

% 保守的时间分段增益调度
if controller_time <= 10.0
    % 0-10秒：温和收敛阶段
    k_pos_base = 2.5;
    k_vel_base = 2.0;
    k_int_base = 0.5;
elseif controller_time <= 15.0
    % 10-15秒：稳定收敛阶段
    k_pos_base = 3.0;
    k_vel_base = 2.5;
    k_int_base = 1.0;
else
    % 15秒后：精确保持阶段
    k_pos_base = 3.2;
    k_vel_base = 3.0;  % 强阻尼
    k_int_base = 1.5;
end

% 误差自适应增益 - 保守调整
if error_pos_norm > 1.0
    k_pos = k_pos_base * 1.1;
    k_vel = k_vel_base * 1.0;
    k_int = k_int_base * 0.8;
elseif error_pos_norm > 0.3
    k_pos = k_pos_base * 1.0;
    k_vel = k_vel_base * 1.2;  % 增加阻尼
    k_int = k_int_base * 1.0;
else
    % 小误差时重点增加阻尼而不是比例增益
    k_pos = k_pos_base * 1.0;
    k_vel = k_vel_base * 1.5;  % 强阻尼
    k_int = k_int_base * 1.2;
end

% 保守的积分控制
if controller_time > 8.0 && error_pos_norm < 0.5
    integral_pos = integral_pos + filtered_error_pos * dt;
else
    integral_pos = integral_pos * 0.95;  % 缓慢衰减
end

% 严格的积分限制
max_integral = 0.3;
integral_pos = max(-max_integral, min(max_integral, integral_pos));

% 微分项计算 - 使用滤波后的误差
if controller_time > dt
    error_pos_dot = (filtered_error_pos - prev_error_pos) / dt;
else
    error_pos_dot = zeros(3, 1);
end

% 微分增益 - 用于增加阻尼
k_d = 0.5;

% PID控制律 - 使用滤波后的误差
acc_cmd = -k_pos * filtered_error_pos - k_vel * filtered_error_vel - k_int * integral_pos - k_d * error_pos_dot;

% 推力计算
phi = euler(1); theta = euler(2);
cos_phi = cos(phi); cos_theta = cos(theta);

% 避免除零
if abs(cos_phi * cos_theta) < 0.1
    cos_phi = sign(cos_phi) * 0.1;
    cos_theta = sign(cos_theta) * 0.1;
end

T = control_params.mass * (acc_cmd(3) + control_params.gravity) / (cos_phi * cos_theta);

% 推力限幅
T = max(control_params.T_min, min(control_params.T_max, T));

% 期望姿态计算 - 限制姿态角度
g = control_params.gravity;
phi_d = (acc_cmd(1) * sin(euler(3)) - acc_cmd(2) * cos(euler(3))) / g;
theta_d = (acc_cmd(1) * cos(euler(3)) + acc_cmd(2) * sin(euler(3))) / g;
psi_d = 0;

% 严格的姿态角限制
max_angle = pi/8;  % 22.5度限制，非常保守
phi_d = max(-max_angle, min(max_angle, phi_d));
theta_d = max(-max_angle, min(max_angle, theta_d));

% 姿态误差
euler_d = [phi_d; theta_d; psi_d];
error_att = euler - euler_d;
error_att_norm = norm(error_att);

% 保守的姿态控制增益
k_att = control_params.k_att * 0.9;  % 降低姿态增益
k_omega = control_params.k_omega * 1.5;  % 增加角速度阻尼
k_int_att = 0.3;

% 姿态积分项 - 非常保守
if controller_time > 10.0 && error_att_norm < 0.1
    integral_att = integral_att + error_att * dt;
else
    integral_att = integral_att * 0.98;
end

% 严格的姿态积分限制
max_integral_att = 0.1;
integral_att = max(-max_integral_att, min(max_integral_att, integral_att));

% 姿态控制律
omega_d = zeros(3, 1);
error_omega = omega - omega_d;

tau = -k_att * error_att - k_omega * error_omega - k_int_att * integral_att;

% 力矩限幅
tau = max(-control_params.tau_max, min(control_params.tau_max, tau));

% 更新历史误差
prev_error_pos = filtered_error_pos;
prev_error_vel = filtered_error_vel;

% 输出控制量
u = [T; tau];

% 调试信息
persistent debug_counter
if isempty(debug_counter)
    debug_counter = 0;
end
debug_counter = debug_counter + 1;

if mod(debug_counter, 500) == 0  % 每5秒输出一次
    fprintf('抗振荡控制器 [%.1fs] - 原始误差: %.4f m, 滤波误差: %.4f m, 速度误差: %.4f m/s\n', ...
            controller_time, norm(error_pos), error_pos_norm, error_vel_norm);
end

end
