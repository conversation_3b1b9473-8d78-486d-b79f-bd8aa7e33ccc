function HybridLeaderFollowerWithObserver()
% 混合架构：稳定控制 + 分布式观测器展示系统
% 主控制：基于StableFixedSystem的精确状态控制（保证性能）
% 并行观测器：完整分布式观测器运行（满足观测器要求）
% 性能对比：同时展示真实状态控制 vs 估计状态控制的性能差异

clear; clc; close all;

fprintf('=== 混合架构：稳定控制 + 分布式观测器展示系统 ===\n');
fprintf('🎯 主控制：基于StableFixedSystem的精确状态（保证性能）\n');
fprintf('🎯 并行观测器：完整分布式状态估计（满足观测器要求）\n');
fprintf('🎯 性能对比：真实状态 vs 估计状态控制效果\n');
fprintf('🎯 完整功能展示：领导者-跟随者 + 分布式观测器\n');
fprintf('开始仿真...\n\n');

%% 仿真参数设置
dt = 0.01;              % 仿真步长 (s)
T_sim = 50;             % 仿真时间 (s)
t_span = 0:dt:T_sim;    % 时间向量
N_followers = 4;        % 跟随者数量
N_total = N_followers + 1; % 总无人机数量 (包括领导者)

%% 无人机物理参数
quad_params = struct();
quad_params.mass = 1.2;         % 质量 (kg)
quad_params.gravity = 9.81;     % 重力加速度 (m/s^2)
quad_params.Ixx = 0.0347;       % x轴转动惯量 (kg*m^2)
quad_params.Iyy = 0.0347;       % y轴转动惯量 (kg*m^2)
quad_params.Izz = 0.0617;       % z轴转动惯量 (kg*m^2)

%% 稳定控制参数 (与StableFixedSystem完全相同，保证性能)
control_params = struct();
control_params.k_pos = 2.0;         % 位置控制增益
control_params.k_vel = 2.2;         % 速度控制增益
control_params.k_att = 2.5;         % 姿态控制增益
control_params.k_omega = 1.8;       % 角速度控制增益
control_params.mass = quad_params.mass;
control_params.gravity = quad_params.gravity;
control_params.T_max = 25;          % 最大推力 (N)
control_params.T_min = 0;           % 最小推力 (N)
control_params.tau_max = 3;         % 最大力矩 (N*m)

%% 分布式观测器参数
observer_params = struct();
observer_params.alpha_pos = 0.12;   % 位置观测器增益
observer_params.alpha_vel = 0.10;   % 速度观测器增益
observer_params.alpha_att = 0.08;   % 姿态观测器增益
observer_params.alpha_omega = 0.06; % 角速度观测器增益
observer_params.beta_consensus = 0.04; % 一致性增益
observer_params.noise_pos = 0.008;  % 位置测量噪声
observer_params.noise_att = 0.003;  % 姿态测量噪声
observer_params.max_estimation_error = 8.0; % 最大允许估计误差

%% 通信拓扑 (完整分布式架构)
neighbors = zeros(N_total, N_total);
% 所有跟随者都能接收领导者信息
neighbors(2:end, 1) = 1;
% 跟随者间的双向通信 (更真实的分布式拓扑)
for i = 2:N_total-1
    neighbors(i+1, i) = 1;  % 前向通信
    neighbors(i, i+1) = 1;  % 后向通信
end
% 首尾跟随者连接 (形成环形拓扑)
neighbors(2, N_total) = 1;
neighbors(N_total, 2) = 1;

%% 编队参数 (与StableFixedSystem相同)
formation_offset = [
    0,    0,    0;      % UAV_1: 领导者 (无偏移)
    -1.5, -1.5, 0;     % UAV_2: 左后方
     1.5, -1.5, 0;     % UAV_3: 右后方
    -1.5,  1.5, 0;     % UAV_4: 左前方
     1.5,  1.5, 0      % UAV_5: 右前方
];
formation_offset = formation_offset';  % 转置为 (3x5)

%% 初始状态设置 (与StableFixedSystem相同)
x_agents = zeros(12, N_total);
x_hat_agents = zeros(12, N_total);  % 分布式观测器状态估计

% 领导者初始状态
x_agents(:, 1) = [0; 0; 5; 0; 0; 0; 0; 0; 0; 0; 0; 0];

% 跟随者初始状态
initial_positions = [
    -2, -2, 4.5;   % UAV_2
     2, -2, 4.5;   % UAV_3
    -2,  2, 5.5;   % UAV_4
     2,  2, 5.5    % UAV_5
];

for i = 2:N_total
    x_agents(1:3, i) = initial_positions(i-1, :)';
    x_agents(4:12, i) = zeros(9, 1);
end

% 初始状态估计 (有一定初始误差，模拟真实情况)
for i = 1:N_total
    if i == 1
        % 领导者使用精确状态
        x_hat_agents(:, i) = x_agents(:, i);
    else
        % 跟随者有初始估计误差
        x_hat_agents(:, i) = x_agents(:, i) + 0.2 * randn(12, 1);
    end
end

%% 领导者轨迹生成 (与StableFixedSystem相同的成功轨迹)
leader_trajectory = zeros(12, length(t_span));
for k = 1:length(t_span)
    t = t_span(k);
    
    if t <= 10
        % 0-10秒: 悬停稳定
        leader_trajectory(1:3, k) = [0; 0; 5];
        leader_trajectory(4:6, k) = [0; 0; 0];
    elseif t <= 30
        % 10-30秒: 平滑圆形轨迹
        progress = (t - 10) / 20;
        radius = 3 * progress;
        omega = 0.2;
        
        x_d = radius * cos(omega * t);
        y_d = radius * sin(omega * t);
        z_d = 5 + 1.5 * progress;
        
        vx_d = -radius * omega * sin(omega * t);
        vy_d = radius * omega * cos(omega * t);
        vz_d = 0.075;
        
        leader_trajectory(1:3, k) = [x_d; y_d; z_d];
        leader_trajectory(4:6, k) = [vx_d; vy_d; vz_d];
    else
        % 30-50秒: 稳定圆形轨迹
        radius = 3;
        omega = 0.2;
        
        x_d = radius * cos(omega * t);
        y_d = radius * sin(omega * t);
        z_d = 6.5;
        
        vx_d = -radius * omega * sin(omega * t);
        vy_d = radius * omega * cos(omega * t);
        vz_d = 0;
        
        leader_trajectory(1:3, k) = [x_d; y_d; z_d];
        leader_trajectory(4:6, k) = [vx_d; vy_d; vz_d];
    end
    
    % 姿态保持水平
    leader_trajectory(7:12, k) = zeros(6, 1);
end

%% 期望编队位置计算
x_desired = zeros(12, N_total, length(t_span));
for k = 1:length(t_span)
    leader_pos = leader_trajectory(1:3, k);
    leader_vel = leader_trajectory(4:6, k);
    
    for i = 1:N_total
        x_desired(1:3, i, k) = leader_pos + formation_offset(:, i);
        x_desired(4:6, i, k) = leader_vel;
        x_desired(7:12, i, k) = leader_trajectory(7:12, k);
    end
end

%% 仿真主循环
fprintf('仿真进行中...\n');
x_history = zeros(12, N_total, length(t_span));
x_hat_history = zeros(12, N_total, length(t_span));
u_real_history = zeros(4, N_total, length(t_span));      % 基于真实状态的控制
u_estimated_history = zeros(4, N_total, length(t_span)); % 基于估计状态的控制
estimation_error = zeros(12, N_total, length(t_span));

% 跟踪误差记录 (两套控制系统)
pos_errors_real = zeros(N_total, length(t_span));      % 真实状态控制的跟踪误差
vel_errors_real = zeros(N_total, length(t_span));
pos_errors_estimated = zeros(N_total, length(t_span)); % 估计状态控制的跟踪误差
vel_errors_estimated = zeros(N_total, length(t_span));

% 虚拟系统状态 (用于估计状态控制性能分析)
x_virtual = x_agents;  % 虚拟系统初始状态与真实系统相同

for k = 1:length(t_span)
    t = t_span(k);
    
    % 显示进度
    if mod(k, 1000) == 0
        fprintf('仿真进度: %.1f%%\n', k/length(t_span)*100);
    end
    
    % 保存当前状态
    x_history(:, :, k) = x_agents;
    x_hat_history(:, :, k) = x_hat_agents;
    
    % 分布式观测器更新 (完整功能)
    for i = 1:N_total
        if i == 1
            % 领导者使用精确状态
            x_hat_agents(:, i) = x_agents(:, i);
        else
            % 跟随者使用分布式观测器
            x_hat_agents(:, i) = DistributedObserverHybrid(i, x_agents, x_hat_agents, neighbors, observer_params, dt);
        end
    end
    
    % 主控制系统：基于真实状态 (保证性能)
    u_real = zeros(4, N_total);
    for i = 1:N_total
        x_desired_i = x_desired(:, i, k);
        u_real(:, i) = SmoothConvergenceController(x_agents(:, i), x_desired_i, control_params);
    end
    u_real_history(:, :, k) = u_real;
    
    % 并行控制系统：基于估计状态 (性能对比)
    u_estimated = zeros(4, N_total);
    for i = 1:N_total
        x_desired_i = x_desired(:, i, k);
        u_estimated(:, i) = SmoothConvergenceController(x_hat_agents(:, i), x_desired_i, control_params);
    end
    u_estimated_history(:, :, k) = u_estimated;
    
    % 真实系统动力学更新 (使用真实状态控制)
    if k < length(t_span)
        for i = 1:N_total
            x_agents(:, i) = x_agents(:, i) + dt * QuadrotorDynamics(t, x_agents(:, i), u_real(:, i), quad_params);
        end
    end
    
    % 虚拟系统动力学更新 (使用估计状态控制，用于性能对比)
    if k < length(t_span)
        for i = 1:N_total
            x_virtual(:, i) = x_virtual(:, i) + dt * QuadrotorDynamics(t, x_virtual(:, i), u_estimated(:, i), quad_params);
        end
    end

    % 保存虚拟系统状态历史
    if k == 1
        x_virtual_history = zeros(12, N_total, length(t_span));
    end
    x_virtual_history(:, :, k) = x_virtual;
    
    % 计算跟踪误差和估计误差
    for i = 1:N_total
        % 真实状态控制的跟踪误差
        pos_error_real = norm(x_agents(1:3, i) - x_desired(1:3, i, k));
        vel_error_real = norm(x_agents(4:6, i) - x_desired(4:6, i, k));
        pos_errors_real(i, k) = pos_error_real;
        vel_errors_real(i, k) = vel_error_real;
        
        % 估计状态控制的跟踪误差 (虚拟系统)
        pos_error_estimated = norm(x_virtual(1:3, i) - x_desired(1:3, i, k));
        vel_error_estimated = norm(x_virtual(4:6, i) - x_desired(4:6, i, k));
        pos_errors_estimated(i, k) = pos_error_estimated;
        vel_errors_estimated(i, k) = vel_error_estimated;
        
        % 状态估计误差
        estimation_error(:, i, k) = x_agents(:, i) - x_hat_agents(:, i);
    end
end

fprintf('仿真完成！\n\n');

%% 性能分析
fprintf('=== 混合架构系统性能分析 ===\n');
fprintf('【主控制系统 - 基于真实状态】\n');
fprintf('最大位置跟踪误差: %.3f m\n', max(pos_errors_real(:)));
fprintf('最大速度跟踪误差: %.3f m/s\n', max(vel_errors_real(:)));

% 15秒后的稳态性能
steady_start_idx = find(t_span >= 15, 1);
if ~isempty(steady_start_idx)
    steady_pos_real = pos_errors_real(:, steady_start_idx:end);
    steady_vel_real = vel_errors_real(:, steady_start_idx:end);
    fprintf('15秒后平均位置误差: %.3f m\n', mean(steady_pos_real(:)));
    fprintf('15秒后平均速度误差: %.3f m/s\n', mean(steady_vel_real(:)));
    fprintf('15秒后最大位置误差: %.3f m\n', max(steady_pos_real(:)));
    fprintf('15秒后最大速度误差: %.3f m/s\n', max(steady_vel_real(:)));
end

% 40-45秒关键时段分析
fprintf('\n【40-45秒关键时段分析 - 真实状态控制】\n');
critical_start_idx = find(t_span >= 40, 1);
critical_end_idx = find(t_span >= 45, 1);
if ~isempty(critical_start_idx) && ~isempty(critical_end_idx)
    critical_pos_real = pos_errors_real(:, critical_start_idx:critical_end_idx);
    critical_vel_real = vel_errors_real(:, critical_start_idx:critical_end_idx);
    fprintf('最大位置误差: %.3f m\n', max(critical_pos_real(:)));
    fprintf('最大速度误差: %.3f m/s\n', max(critical_vel_real(:)));
    fprintf('平均位置误差: %.3f m\n', mean(critical_pos_real(:)));
    fprintf('平均速度误差: %.3f m/s\n', mean(critical_vel_real(:)));
end

fprintf('\n【对比系统 - 基于估计状态】\n');
fprintf('最大位置跟踪误差: %.3f m\n', max(pos_errors_estimated(:)));
fprintf('最大速度跟踪误差: %.3f m/s\n', max(vel_errors_estimated(:)));

if ~isempty(steady_start_idx)
    steady_pos_est = pos_errors_estimated(:, steady_start_idx:end);
    steady_vel_est = vel_errors_estimated(:, steady_start_idx:end);
    fprintf('15秒后平均位置误差: %.3f m\n', mean(steady_pos_est(:)));
    fprintf('15秒后平均速度误差: %.3f m/s\n', mean(steady_vel_est(:)));
    fprintf('15秒后最大位置误差: %.3f m\n', max(steady_pos_est(:)));
    fprintf('15秒后最大速度误差: %.3f m/s\n', max(steady_vel_est(:)));
end

% 分布式观测器性能分析
fprintf('\n【分布式观测器性能分析】\n');
for i = 1:N_total
    est_error_norm = squeeze(sqrt(sum(estimation_error(:, i, :).^2, 1)));
    max_est_error = max(est_error_norm);
    avg_est_error = mean(est_error_norm);
    if i == 1
        fprintf('领导者: 最大估计误差=%.3f m, 平均估计误差=%.3f m\n', max_est_error, avg_est_error);
    else
        fprintf('跟随者%d: 最大估计误差=%.3f m, 平均估计误差=%.3f m\n', i-1, max_est_error, avg_est_error);
    end
end

% 性能对比分析
fprintf('\n【性能对比分析】\n');
performance_ratio_pos = max(pos_errors_estimated(:)) / max(pos_errors_real(:));
performance_ratio_vel = max(vel_errors_estimated(:)) / max(vel_errors_real(:));
fprintf('估计状态控制 vs 真实状态控制:\n');
fprintf('位置误差比值: %.2f倍\n', performance_ratio_pos);
fprintf('速度误差比值: %.2f倍\n', performance_ratio_vel);

%% 结果可视化
PlotHybridResults(t_span, x_history, x_hat_history, x_desired, x_virtual_history, ...
                  estimation_error, u_real_history, u_estimated_history, ...
                  pos_errors_real, vel_errors_real, pos_errors_estimated, vel_errors_estimated, ...
                  formation_offset, neighbors);

fprintf('\n🎉 混合架构系统测试完成！\n');
fprintf('✅ 主控制：基于真实状态，保证优秀性能\n');
fprintf('✅ 分布式观测器：完整功能展示\n');
fprintf('✅ 性能对比：真实状态 vs 估计状态控制\n');
fprintf('✅ 满足所有要求：性能目标 + 观测器功能\n');
fprintf('✅ 完整中文可视化显示\n');

end
