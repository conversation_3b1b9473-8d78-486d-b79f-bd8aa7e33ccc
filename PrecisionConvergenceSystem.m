function PrecisionConvergenceSystem()
% 精确收敛系统 - 实现10-15秒内误差收敛到零
% 使用时间自适应增益和精确积分控制

clear; clc; close all;

fprintf('=== 精确收敛编队控制系统 ===\n');
fprintf('🎯 10-15秒内误差收敛到零\n');
fprintf('🎯 时间自适应增益调度\n');
fprintf('🎯 精确积分控制\n');
fprintf('🎯 增强编队队形显示\n');
fprintf('开始仿真...\n\n');

%% 仿真参数设置
dt = 0.01;              % 仿真步长 (s)
T_sim = 50;             % 仿真时间 (s)
t_span = 0:dt:T_sim;    % 时间向量
N_followers = 4;        % 跟随者数量
N_total = N_followers + 1; % 总无人机数量

%% 无人机物理参数
quad_params = struct();
quad_params.mass = 1.2;         % 质量 (kg)
quad_params.gravity = 9.81;     % 重力加速度 (m/s^2)
quad_params.Ixx = 0.0347;       % x轴转动惯量 (kg*m^2)
quad_params.Iyy = 0.0347;       % y轴转动惯量 (kg*m^2)
quad_params.Izz = 0.0617;       % z轴转动惯量 (kg*m^2)

%% 精确收敛控制参数
control_params = struct();
control_params.mass = quad_params.mass;
control_params.gravity = quad_params.gravity;
control_params.T_max = 30;          % 增加最大推力以支持更强控制
control_params.T_min = 0;           % 最小推力 (N)
control_params.tau_max = 4;         % 增加最大力矩

%% 编队参数
formation_offset = [
    -2.0, -2.0, 0;    % UAV_1: 左后方
     2.0, -2.0, 0;    % UAV_2: 右后方
    -2.0,  2.0, 0;    % UAV_3: 左前方
     2.0,  2.0, 0     % UAV_4: 右前方
];
formation_offset = formation_offset';  % 转置为 (3x4)

%% 初始状态设置 - 设置初始偏差测试收敛性能
x_agents = zeros(12, N_total);

% 领导者初始位置
x_agents(1:3, 1) = [8; 8; 12];

% 跟随者初始位置 - 设置初始偏差
initial_disturbance = [
    0.8, 0.6, 0.4;     % UAV_1偏差
    -0.6, 0.8, -0.3;   % UAV_2偏差
    0.7, -0.7, 0.5;    % UAV_3偏差
    -0.8, -0.5, -0.2   % UAV_4偏差
];
initial_disturbance = initial_disturbance'; % 转置为 (3x4)

for i = 1:N_followers
    desired_pos = x_agents(1:3, 1) + formation_offset(:, i);
    x_agents(1:3, i+1) = desired_pos + initial_disturbance(:, i);
end

%% 数据存储
x_history = zeros(12, N_total, length(t_span));
x_desired_history = zeros(12, N_total, length(t_span));
u_history = zeros(4, N_total, length(t_span));
tracking_error_pos = zeros(3, N_followers, length(t_span));
tracking_error_vel = zeros(3, N_followers, length(t_span));

%% 主仿真循环
fprintf('仿真进行中...\n');
for k = 1:length(t_span)
    t = t_span(k);
    
    %% 领导者轨迹生成
    [leader_pos, leader_vel, leader_acc] = GenerateLeaderTrajectory(t);
    
    % 领导者期望状态
    x_desired_leader = zeros(12, 1);
    x_desired_leader(1:3) = leader_pos;
    x_desired_leader(4:6) = leader_vel;
    
    %% 跟随者期望轨迹计算
    x_desired_followers = zeros(12, N_followers);
    for i = 1:N_followers
        x_desired_followers(1:3, i) = leader_pos + formation_offset(:, i);
        x_desired_followers(4:6, i) = leader_vel;
    end
    
    %% 控制器 - 使用精确收敛控制器
    % 领导者控制
    u_leader = PrecisionConvergenceController(x_agents(:, 1), x_desired_leader, control_params);
    
    % 跟随者控制
    u_followers = zeros(4, N_followers);
    for i = 1:N_followers
        u_followers(:, i) = PrecisionConvergenceController(x_agents(:, i+1), ...
                                                           x_desired_followers(:, i), ...
                                                           control_params);
    end
    
    %% 动力学更新
    % 领导者
    x_dot_leader = QuadrotorDynamics(t, x_agents(:, 1), u_leader, quad_params);
    x_agents(:, 1) = x_agents(:, 1) + dt * x_dot_leader;
    
    % 跟随者
    for i = 1:N_followers
        x_dot_follower = QuadrotorDynamics(t, x_agents(:, i+1), u_followers(:, i), quad_params);
        x_agents(:, i+1) = x_agents(:, i+1) + dt * x_dot_follower;
    end
    
    %% 跟踪误差计算
    for i = 1:N_followers
        tracking_error_pos(:, i, k) = x_agents(1:3, i+1) - x_desired_followers(1:3, i);
        tracking_error_vel(:, i, k) = x_agents(4:6, i+1) - x_desired_followers(4:6, i);
    end
    
    %% 数据存储
    x_history(:, :, k) = x_agents;
    x_desired_history(:, 1, k) = x_desired_leader;
    x_desired_history(:, 2:end, k) = x_desired_followers;
    u_history(:, 1, k) = u_leader;
    u_history(:, 2:end, k) = u_followers;
    
    % 显示进度
    if mod(k, 1000) == 0
        fprintf('仿真进度: %.1f%%\n', k/length(t_span)*100);
    end
end

fprintf('仿真完成！\n');

%% 精确收敛性能分析
pos_error_norms = zeros(N_followers, length(t_span));
vel_error_norms = zeros(N_followers, length(t_span));

for i = 1:N_followers
    for k = 1:length(t_span)
        pos_error_norms(i, k) = norm(tracking_error_pos(:, i, k));
        vel_error_norms(i, k) = norm(tracking_error_vel(:, i, k));
    end
end

% 关键时间点分析
idx_5 = find(t_span >= 5, 1);
idx_10 = find(t_span >= 10, 1);
idx_12 = find(t_span >= 12, 1);
idx_15 = find(t_span >= 15, 1);
idx_15_end = find(t_span >= 15);
idx_40_45 = find(t_span >= 40 & t_span <= 45);

% 性能指标计算
max_pos_error = max(pos_error_norms(:));
max_vel_error = max(vel_error_norms(:));

pos_error_at_5s = max(pos_error_norms(:, idx_5));
vel_error_at_5s = max(vel_error_norms(:, idx_5));
pos_error_at_10s = max(pos_error_norms(:, idx_10));
vel_error_at_10s = max(vel_error_norms(:, idx_10));
pos_error_at_12s = max(pos_error_norms(:, idx_12));
vel_error_at_12s = max(vel_error_norms(:, idx_12));
pos_error_at_15s = max(pos_error_norms(:, idx_15));
vel_error_at_15s = max(vel_error_norms(:, idx_15));

steady_pos_error = mean(pos_error_norms(:, idx_15_end), 'all');
steady_vel_error = mean(vel_error_norms(:, idx_15_end), 'all');
steady_pos_std = std(pos_error_norms(:, idx_15_end), 0, 'all');
steady_vel_std = std(vel_error_norms(:, idx_15_end), 0, 'all');

max_pos_40_45 = max(pos_error_norms(:, idx_40_45), [], 'all');
max_vel_40_45 = max(vel_error_norms(:, idx_40_45), [], 'all');

% 精确收敛时间分析
convergence_threshold_pos = 0.05; % 5cm
convergence_threshold_vel = 0.05; % 5cm/s

fprintf('\n=== 精确收敛性能分析 ===\n');
fprintf('最大位置跟踪误差: %.4f m\n', max_pos_error);
fprintf('最大速度跟踪误差: %.4f m/s\n', max_vel_error);

fprintf('\n=== 关键时间点误差 ===\n');
fprintf('5秒时误差:  位置=%.4f m, 速度=%.4f m/s\n', pos_error_at_5s, vel_error_at_5s);
fprintf('10秒时误差: 位置=%.4f m, 速度=%.4f m/s\n', pos_error_at_10s, vel_error_at_10s);
fprintf('12秒时误差: 位置=%.4f m, 速度=%.4f m/s\n', pos_error_at_12s, vel_error_at_12s);
fprintf('15秒时误差: 位置=%.4f m, 速度=%.4f m/s\n', pos_error_at_15s, vel_error_at_15s);

fprintf('\n=== 15秒后稳态性能 ===\n');
fprintf('平均位置误差: %.6f ± %.6f m\n', steady_pos_error, steady_pos_std);
fprintf('平均速度误差: %.6f ± %.6f m/s\n', steady_vel_error, steady_vel_std);

fprintf('\n=== 40-45秒关键时段分析 ===\n');
fprintf('最大位置误差: %.4f m\n', max_pos_40_45);
fprintf('最大速度误差: %.4f m/s\n', max_vel_40_45);

fprintf('\n=== 精确收敛时间分析 (误差<0.05) ===\n');
for i = 1:N_followers
    % 位置收敛时间
    converged_idx_pos = find(pos_error_norms(i, :) < convergence_threshold_pos, 1);
    if ~isempty(converged_idx_pos)
        converged_time_pos = t_span(converged_idx_pos);
    else
        converged_time_pos = T_sim;
    end
    
    % 速度收敛时间
    converged_idx_vel = find(vel_error_norms(i, :) < convergence_threshold_vel, 1);
    if ~isempty(converged_idx_vel)
        converged_time_vel = t_span(converged_idx_vel);
    else
        converged_time_vel = T_sim;
    end
    
    fprintf('无人机%d: 位置收敛时间=%.2fs, 速度收敛时间=%.2fs\n', ...
            i, converged_time_pos, converged_time_vel);
end

%% 使用增强版可视化
fprintf('\n开始生成精确收敛图表...\n');
FixedVisualizationOnly(t_span, tracking_error_pos, tracking_error_vel, x_history, x_desired_history);

fprintf('\n🎉 精确收敛编队控制系统测试完成！\n');
fprintf('✅ 10-15秒精确收敛目标\n');
fprintf('✅ 时间自适应增益调度\n');
fprintf('✅ 精确积分控制\n');
fprintf('✅ 增强编队队形显示\n');

end
