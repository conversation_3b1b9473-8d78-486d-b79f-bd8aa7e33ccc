function OptimizedLeaderFollower()
% 优化版领导者-跟随者无人机编队控制仿真
% 改进的控制参数和更好的跟踪性能

clear; clc; close all;

fprintf('=== 优化版领导者-跟随者无人机编队控制仿真 ===\n');
fprintf('改进特点:\n');
fprintf('• 优化的控制参数\n');
fprintf('• 更好的跟踪性能\n');
fprintf('• 改进的数值稳定性\n');
fprintf('开始仿真...\n\n');

%% 仿真参数设置
dt = 0.01;              % 仿真步长 (s)
T_sim = 50;             % 仿真时间 (s)
t_span = 0:dt:T_sim;    % 时间向量
N_followers = 4;        % 跟随者数量
N_total = N_followers + 1; % 总无人机数量

%% 无人机物理参数
quad_params = struct();
quad_params.mass = 1.2;         % 质量 (kg)
quad_params.gravity = 9.81;     % 重力加速度 (m/s^2)
quad_params.Ixx = 0.0347;       % x轴转动惯量 (kg*m^2)
quad_params.Iyy = 0.0347;       % y轴转动惯量 (kg*m^2)
quad_params.Izz = 0.0617;       % z轴转动惯量 (kg*m^2)

%% 优化的控制参数
control_params = struct();
control_params.k_pos = 2.0;         % 位置控制增益 (优化)
control_params.k_vel = 1.5;         % 速度控制增益 (优化)
control_params.k_att = 2.5;         % 姿态控制增益 (优化)
control_params.k_omega = 1.2;       % 角速度控制增益 (优化)
control_params.mass = quad_params.mass;
control_params.gravity = quad_params.gravity;
control_params.T_max = 25;          % 最大推力 (N)
control_params.T_min = 0;           % 最小推力 (N)
control_params.tau_max = 3;         % 最大力矩 (N*m)

%% 编队参数 (稍微调整)
formation_offset = [
    -1.5, -1.5, 0;    % UAV_1: 左后方
     1.5, -1.5, 0;    % UAV_2: 右后方
    -1.5,  1.5, 0;    % UAV_3: 左前方
     1.5,  1.5, 0     % UAV_4: 右前方
];
formation_offset = formation_offset';  % 转置为 (3x4)

%% 初始状态设置
x_agents = zeros(12, N_total);

% 领导者初始位置
x_agents(1:3, 1) = [8; 8; 12];  % 领导者起始位置 (调整到轨迹中心)

% 跟随者初始位置 (更接近期望编队)
for i = 1:N_followers
    x_agents(1:3, i+1) = x_agents(1:3, 1) + formation_offset(:, i);
end

%% 数据存储
x_history = zeros(12, N_total, length(t_span));
x_desired_history = zeros(12, N_total, length(t_span));
u_history = zeros(4, N_total, length(t_span));
tracking_error_pos = zeros(3, N_followers, length(t_span));
tracking_error_vel = zeros(3, N_followers, length(t_span));

%% 主仿真循环
fprintf('仿真进行中...\n');
for k = 1:length(t_span)
    t = t_span(k);
    
    %% 领导者轨迹生成
    [leader_pos, leader_vel, leader_acc] = GenerateLeaderTrajectory(t);
    
    % 领导者期望状态
    x_desired_leader = zeros(12, 1);
    x_desired_leader(1:3) = leader_pos;
    x_desired_leader(4:6) = leader_vel;
    
    %% 跟随者期望轨迹计算
    x_desired_followers = zeros(12, N_followers);
    for i = 1:N_followers
        % 跟随者期望位置 = 领导者位置 + 编队偏移
        x_desired_followers(1:3, i) = leader_pos + formation_offset(:, i);
        x_desired_followers(4:6, i) = leader_vel;  % 期望速度与领导者相同
    end
    
    %% 领导者控制 (轨迹跟踪)
    u_leader = LeaderController(x_agents(:, 1), x_desired_leader, control_params);
    
    %% 跟随者控制 (编队跟踪)
    u_followers = zeros(4, N_followers);
    for i = 1:N_followers
        u_followers(:, i) = FollowerController(x_agents(:, i+1), ...
                                               x_desired_followers(:, i), ...
                                               control_params);
    end
    
    %% 动力学更新
    % 领导者动力学
    x_dot_leader = QuadrotorDynamics(t, x_agents(:, 1), u_leader, quad_params);
    x_agents(:, 1) = x_agents(:, 1) + dt * x_dot_leader;
    
    % 跟随者动力学
    for i = 1:N_followers
        x_dot_follower = QuadrotorDynamics(t, x_agents(:, i+1), u_followers(:, i), quad_params);
        x_agents(:, i+1) = x_agents(:, i+1) + dt * x_dot_follower;
    end
    
    %% 跟踪误差计算
    for i = 1:N_followers
        tracking_error_pos(:, i, k) = x_agents(1:3, i+1) - x_desired_followers(1:3, i);
        tracking_error_vel(:, i, k) = x_agents(4:6, i+1) - x_desired_followers(4:6, i);
    end
    
    %% 数据存储
    x_history(:, :, k) = x_agents;
    x_desired_history(:, 1, k) = x_desired_leader;
    x_desired_history(:, 2:end, k) = x_desired_followers;
    u_history(:, 1, k) = u_leader;
    u_history(:, 2:end, k) = u_followers;
    
    % 显示进度
    if mod(k, 1000) == 0
        fprintf('仿真进度: %.1f%%\n', k/length(t_span)*100);
    end
end

fprintf('仿真完成！\n');

%% 性能统计
pos_error_norms = zeros(N_followers, length(t_span));
vel_error_norms = zeros(N_followers, length(t_span));

for i = 1:N_followers
    for k = 1:length(t_span)
        pos_error_norms(i, k) = norm(tracking_error_pos(:, i, k));
        vel_error_norms(i, k) = norm(tracking_error_vel(:, i, k));
    end
end

% 计算性能指标
steady_state_start = round(0.8 * length(t_span)); % 最后20%为稳态
max_pos_error = max(pos_error_norms(:));
max_vel_error = max(vel_error_norms(:));
steady_pos_error = mean(pos_error_norms(:, steady_state_start:end), 'all');
steady_vel_error = mean(vel_error_norms(:, steady_state_start:end), 'all');

fprintf('\n=== 性能统计 ===\n');
fprintf('最大位置跟踪误差: %.3f m\n', max_pos_error);
fprintf('最大速度跟踪误差: %.3f m/s\n', max_vel_error);
fprintf('稳态位置跟踪误差: %.3f m\n', steady_pos_error);
fprintf('稳态速度跟踪误差: %.3f m/s\n', steady_vel_error);

%% 结果可视化
PlotLeaderFollowerResults(t_span, x_history, x_desired_history, ...
                          tracking_error_pos, tracking_error_vel, u_history);

% 编队形状专门可视化
PlotFormationShape(t_span, x_history, x_desired_history);

% 额外的性能分析图
figure('Name', '性能分析', 'Position', [400, 400, 1000, 600]);

% 位置误差统计
subplot(2, 2, 1);
boxplot(pos_error_norms', 'Labels', {'无人机_1', '无人机_2', '无人机_3', '无人机_4'});
ylabel('位置误差 (m)');
title('位置跟踪误差分布');
grid on;

% 速度误差统计
subplot(2, 2, 2);
boxplot(vel_error_norms', 'Labels', {'无人机_1', '无人机_2', '无人机_3', '无人机_4'});
ylabel('速度误差 (m/s)');
title('速度跟踪误差分布');
grid on;

% 收敛性分析
subplot(2, 2, 3);
convergence_window = 500; % 5秒窗口
conv_pos_error = zeros(1, length(t_span) - convergence_window + 1);
for k = 1:(length(t_span) - convergence_window + 1)
    conv_pos_error(k) = mean(pos_error_norms(:, k:k+convergence_window-1), 'all');
end
plot(t_span(1:length(conv_pos_error)), conv_pos_error, 'b-', 'LineWidth', 2);
xlabel('时间 (s)'); ylabel('平均位置误差 (m)');
title('收敛性分析 (5秒滑动平均)');
grid on;

% 编队保持性能
subplot(2, 2, 4);
formation_errors = zeros(1, length(t_span));
for k = 1:length(t_span)
    % 计算跟随者之间的相对位置误差
    actual_formation = x_history(1:3, 2:end, k);
    desired_formation = x_desired_history(1:3, 2:end, k);
    formation_errors(k) = norm(actual_formation - desired_formation, 'fro');
end
plot(t_span, formation_errors, 'r-', 'LineWidth', 1.5);
xlabel('时间 (s)'); ylabel('编队误差 (m)');
title('编队保持误差');
grid on;

fprintf('\n优化版仿真完成！\n');
fprintf('生成了额外的性能分析图表。\n');

end
