# 分布式观测器四旋翼编队控制系统

## 系统概述

本项目实现了基于分布式观测器的四旋翼无人机编队控制系统，主要特点包括：

- **分布式状态观测器**: 基于一致性算法的状态估计
- **编队控制**: 实现多无人机协调编队飞行
- **完整动力学模型**: 考虑四旋翼的完整6自由度动力学
- **鲁棒性设计**: 考虑测量噪声和模型不确定性
- **可视化分析**: 提供丰富的仿真结果可视化

## 文件结构

```
├── QuadrotorDynamics.m      # 四旋翼动力学模型
├── DistributedController.m  # 分布式编队控制器
├── DistributedObserver.m    # 分布式状态观测器
├── FormationControlMain.m   # 主仿真程序
├── PlotResults.m           # 结果可视化函数
├── RunFormationControl.m   # 演示运行脚本
├── ConfigParameters.m      # 参数配置函数
└── README.md              # 说明文档
```

## 快速开始

### 1. 基本运行

在MATLAB命令窗口中运行：

```matlab
RunFormationControl
```

### 2. 自定义参数运行

```matlab
% 获取参数配置
[quad_params, control_params, observer_params, sim_params] = ConfigParameters();

% 修改参数（可选）
sim_params.T_sim = 30;  % 仿真时间30秒
sim_params.N_agents = 6; % 6架无人机

% 运行仿真
FormationControlMain();
```

## 系统架构

### 1. 四旋翼动力学模型

- **状态向量**: [位置(3), 速度(3), 欧拉角(3), 角速度(3)]
- **控制输入**: [总推力, 滚转力矩, 俯仰力矩, 偏航力矩]
- **完整的6DOF非线性动力学**

### 2. 分布式观测器

```
状态估计方程:
x̂̇ᵢ = Ax̂ᵢ + Buᵢ + L(yᵢ - Cx̂ᵢ) + Σⱼ∈Nᵢ kc(x̂ⱼ - x̂ᵢ)
```

其中：
- `A, B, C`: 系统矩阵（线性化后）
- `L`: 观测器增益矩阵
- `kc`: 一致性增益
- `Nᵢ`: 无人机i的邻居集合

### 3. 分布式控制器

采用分层控制结构：

**位置控制层**:
```
upos = -kp(pi - pd) - kv(vi - vd) - kc Σⱼ∈Nᵢ [(pi-pd) - (pj-pd)]
```

**姿态控制层**:
```
τ = -katt(θi - θd) - kω(ωi - ωd) - kc Σⱼ∈Nᵢ [(θi-θd) - (θj-θd)]
```

## 参数说明

### 控制参数
- `k_pos`: 位置控制增益 (默认: 2.0)
- `k_vel`: 速度控制增益 (默认: 1.5)
- `k_att`: 姿态控制增益 (默认: 1.0)
- `k_omega`: 角速度控制增益 (默认: 0.5)
- `k_consensus`: 一致性增益 (默认: 0.8)

### 观测器参数
- `observer_gain`: 观测器增益矩阵
- `k_consensus_obs`: 观测器一致性增益 (默认: 0.5)

### 仿真参数
- `dt`: 仿真步长 (默认: 0.01s)
- `T_sim`: 仿真时间 (默认: 20s)
- `N_agents`: 无人机数量 (默认: 4)
- `formation_size`: 编队尺寸 (默认: 2m)

## 通信拓扑

系统支持多种通信拓扑：

1. **完全图**: 所有无人机相互通信
2. **环形图**: 无人机按环形连接
3. **星形图**: 一个中心节点连接所有其他节点
4. **自定义图**: 用户自定义邻接矩阵

默认使用自定义拓扑：
```
无人机1 ↔ 无人机2,3
无人机2 ↔ 无人机1,3,4
无人机3 ↔ 无人机1,2,4
无人机4 ↔ 无人机2,3
```

## 结果分析

仿真完成后会生成5个图表：

1. **3D轨迹图**: 显示无人机飞行轨迹和编队形成过程
2. **位置跟踪性能**: 真实位置vs估计位置vs期望位置
3. **状态估计误差**: 分布式观测器的估计精度分析
4. **控制输入**: 推力和力矩控制信号
5. **编队误差分析**: 编队中心和形状误差

## 性能指标

- **编队误差**: RMS位置误差
- **估计精度**: 状态估计误差
- **收敛时间**: 达到稳态的时间
- **控制能耗**: 控制输入的能量消耗

## 扩展功能

### 1. 添加新的编队形状

在`ConfigParameters.m`中修改编队配置：

```matlab
% 三角形编队
if strcmp(formation_type, 'triangle')
    x_desired(1:3, 1) = center + [0; formation_size; 0];
    x_desired(1:3, 2) = center + [-formation_size*sqrt(3)/2; -formation_size/2; 0];
    x_desired(1:3, 3) = center + [formation_size*sqrt(3)/2; -formation_size/2; 0];
end
```

### 2. 修改通信拓扑

```matlab
% 环形拓扑
neighbors = [0, 1, 0, 1;
             1, 0, 1, 0;
             0, 1, 0, 1;
             1, 0, 1, 0];
```

### 3. 添加外部干扰

在`QuadrotorDynamics.m`中添加风扰动：

```matlab
% 风扰动
wind_force = [0.1*sin(t); 0.1*cos(t); 0];
F_total = F_total + wind_force;
```

## 故障排除

### 常见问题

1. **仿真不收敛**
   - 检查控制增益是否合适
   - 确认通信拓扑连通性
   - 减小仿真步长

2. **估计误差过大**
   - 调整观测器增益
   - 检查测量噪声设置
   - 增加一致性增益

3. **控制输入饱和**
   - 调整控制增益
   - 检查期望轨迹的合理性
   - 增加推力/力矩限制

## 技术支持

如有问题或建议，请检查：
1. MATLAB版本兼容性
2. 参数设置的合理性
3. 通信拓扑的连通性

## 参考文献

1. Olfati-Saber, R. (2006). Flocking for multi-agent dynamic systems: Algorithms and theory.
2. Ren, W., & Beard, R. W. (2008). Distributed consensus in multi-vehicle cooperative control.
3. Beard, R. W., & McLain, T. W. (2012). Small unmanned aircraft: Theory and practice.

---

**版本**: 1.0  
**作者**: AI助手  
**日期**: 2025年7月2日
