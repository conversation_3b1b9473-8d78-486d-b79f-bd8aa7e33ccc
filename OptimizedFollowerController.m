function u = OptimizedFollowerController(x, x_desired, params)
% 优化版跟随者控制器 - 提高跟踪性能，减少40-45秒误差峰值
% 输入:
%   x - 当前状态 [12x1]: [位置; 速度; 姿态角; 角速度]
%   x_desired - 期望状态 [12x1]
%   params - 控制参数结构体
% 输出:
%   u - 控制输入 [4x1]: [推力; 滚转力矩; 俯仰力矩; 偏航力矩]

% 提取状态变量
pos = x(1:3);           % 位置 [x, y, z]
vel = x(4:6);           % 速度 [vx, vy, vz]
att = x(7:9);           % 姿态角 [phi, theta, psi]
omega = x(10:12);       % 角速度 [p, q, r]

% 提取期望状态
pos_d = x_desired(1:3);
vel_d = x_desired(4:6);
att_d = x_desired(7:9);
omega_d = x_desired(10:12);

% 优化后的控制参数 - 提高增益以减少跟踪误差
k_pos = params.k_pos * 1.5;     % 位置增益提高50%
k_vel = params.k_vel * 1.3;     % 速度增益提高30%
k_att = params.k_att * 1.2;     % 姿态增益提高20%
k_omega = params.k_omega * 1.1; % 角速度增益提高10%

% 添加积分控制以消除稳态误差
persistent pos_error_integral vel_error_integral
if isempty(pos_error_integral)
    pos_error_integral = zeros(3, 1);
    vel_error_integral = zeros(3, 1);
end

% 位置和速度误差
pos_error = pos_d - pos;
vel_error = vel_d - vel;

% 积分项更新（带抗饱和）
dt = 0.01; % 仿真步长
ki_pos = 0.1; % 位置积分增益
ki_vel = 0.05; % 速度积分增益

% 积分抗饱和
max_integral = 2.0;
pos_error_integral = pos_error_integral + pos_error * dt;
pos_error_integral = max(-max_integral, min(max_integral, pos_error_integral));

vel_error_integral = vel_error_integral + vel_error * dt;
vel_error_integral = max(-max_integral, min(max_integral, vel_error_integral));

% 位置控制器 (PID控制)
acc_desired = k_pos * pos_error + k_vel * vel_error + ...
              ki_pos * pos_error_integral + ki_vel * vel_error_integral;

% 添加前馈控制
if length(x_desired) >= 12 && any(x_desired(10:12) ~= 0)
    % 如果有期望加速度信息，添加前馈
    acc_desired = acc_desired + x_desired(10:12);
end

% 期望推力 (考虑重力补偿)
mass = params.mass;
gravity = params.gravity;
T_desired = mass * (acc_desired(3) + gravity);

% 期望姿态角计算 (从期望加速度推导)
phi_desired = asin((acc_desired(1) * sin(att(3)) - acc_desired(2) * cos(att(3))) / gravity);
theta_desired = atan2(acc_desired(1) * cos(att(3)) + acc_desired(2) * sin(att(3)), ...
                      acc_desired(3) + gravity);
psi_desired = att_d(3); % 期望偏航角

att_desired = [phi_desired; theta_desired; psi_desired];

% 姿态误差
att_error = att_desired - att;
omega_error = omega_d - omega;

% 姿态控制器 (PD控制)
tau_desired = k_att * att_error + k_omega * omega_error;

% 控制输入限幅
T_max = params.T_max;
T_min = params.T_min;
tau_max = params.tau_max;

T = max(T_min, min(T_max, T_desired));
tau = max(-tau_max, min(tau_max, tau_desired));

% 输出控制信号
u = [T; tau];

% 添加自适应控制以应对轨迹变化
persistent adaptation_gain
if isempty(adaptation_gain)
    adaptation_gain = 1.0;
end

% 根据跟踪误差动态调整控制增益
error_norm = norm(pos_error) + norm(vel_error);
if error_norm > 1.0
    adaptation_gain = min(1.5, adaptation_gain * 1.01); % 增加增益
elseif error_norm < 0.1
    adaptation_gain = max(0.8, adaptation_gain * 0.999); % 减少增益
end

% 应用自适应增益
u(1) = u(1) * adaptation_gain;
u(2:4) = u(2:4) * adaptation_gain;

end
