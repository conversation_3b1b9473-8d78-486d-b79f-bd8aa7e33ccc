% 领导者-跟随者无人机编队控制演示脚本
% 
% 系统特点:
% - 1个领导者无人机 (UAV_leader) 按预设轨迹飞行
% - 4个跟随者无人机 (UAV_1, UAV_2, UAV_3, UAV_4) 保持编队跟踪领导者
% - 生成与参考文献相似的仿真结果图表
%
% 作者: AI助手
% 日期: 2025年7月2日

clear; clc; close all;

fprintf('===============================================\n');
fprintf('    领导者-跟随者无人机编队控制仿真系统\n');
fprintf('===============================================\n');
fprintf('系统配置:\n');
fprintf('• 领导者: 1架 (UAV_leader) - 螺旋上升轨迹\n');
fprintf('• 跟随者: 4架 (UAV_1~UAV_4) - 编队跟踪\n');
fprintf('• 仿真时间: 50秒\n');
fprintf('• 编队形状: 矩形编队 (4m x 4m)\n');
fprintf('• 控制策略: 分层PD控制\n');
fprintf('===============================================\n\n');

% 询问用户运行选项
fprintf('请选择运行模式:\n');
fprintf('1. 快速测试 (5秒仿真)\n');
fprintf('2. 完整仿真 (50秒仿真)\n');
fprintf('3. 仅测试系统功能\n');

choice = input('请输入选择 (1/2/3): ');

switch choice
    case 1
        fprintf('\n运行快速测试...\n');
        RunQuickTest();
        
    case 2
        fprintf('\n运行完整仿真...\n');
        LeaderFollowerFormation();
        
    case 3
        fprintf('\n运行系统测试...\n');
        TestLeaderFollower();
        
    otherwise
        fprintf('\n无效选择，运行完整仿真...\n');
        LeaderFollowerFormation();
end

fprintf('\n===============================================\n');
fprintf('仿真完成！\n');
fprintf('===============================================\n');
fprintf('生成的图表说明:\n');
fprintf('图1: 位置跟踪误差对比 - 显示跟随者的位置跟踪性能\n');
fprintf('图2: 速度跟踪误差对比 - 显示跟随者的速度跟踪性能\n');
fprintf('图3: 3D飞行轨迹 - 显示所有无人机的飞行轨迹\n');
fprintf('===============================================\n\n');

fprintf('技术特点:\n');
fprintf('• 领导者轨迹: 螺旋上升 + 圆周飞行\n');
fprintf('• 跟随者控制: 基于位置偏移的编队保持\n');
fprintf('• 控制算法: 分层PD控制 (位置控制 + 姿态控制)\n');
fprintf('• 数值稳定: 包含饱和限制和NaN检查\n');
fprintf('• 可视化: 仿照学术论文的图表样式\n\n');

end

function RunQuickTest()
% 快速测试函数 - 5秒仿真
fprintf('修改仿真时间为5秒...\n');

% 这里可以调用修改后的仿真函数
% 为简化，直接提示用户
fprintf('快速测试功能需要修改 LeaderFollowerFormation.m 中的 T_sim 参数\n');
fprintf('请将第15行的 T_sim = 50; 改为 T_sim = 5;\n');
fprintf('然后运行 LeaderFollowerFormation\n');

end
