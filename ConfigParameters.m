function [quad_params, control_params, observer_params, sim_params] = ConfigParameters()
% 系统参数配置函数
% 该函数定义了分布式观测器四旋翼编队控制系统的所有参数
% 
% 输出参数:
%   quad_params - 四旋翼物理参数
%   control_params - 控制器参数
%   observer_params - 观测器参数
%   sim_params - 仿真参数

%% 四旋翼物理参数
quad_params = struct();
quad_params.mass = 1.2;         % 质量 (kg)
quad_params.gravity = 9.81;     % 重力加速度 (m/s^2)
quad_params.Ixx = 0.0347;       % x轴转动惯量 (kg*m^2)
quad_params.Iyy = 0.0347;       % y轴转动惯量 (kg*m^2)
quad_params.Izz = 0.0617;       % z轴转动惯量 (kg*m^2)

% 空气动力学参数 (可选，用于更精确的模型)
quad_params.drag_coeff = 0.01;  % 阻力系数
quad_params.rotor_radius = 0.1; % 旋翼半径 (m)
quad_params.arm_length = 0.25;  % 机臂长度 (m)

%% 控制器参数
control_params = struct();

% 位置控制增益
control_params.k_pos = 2.0;         % 位置比例增益
control_params.k_vel = 1.5;         % 速度比例增益
control_params.k_pos_i = 0.1;       % 位置积分增益 (可选)
control_params.k_pos_d = 0.5;       % 位置微分增益 (可选)

% 姿态控制增益
control_params.k_att = 1.0;         % 姿态比例增益
control_params.k_omega = 0.5;       % 角速度比例增益
control_params.k_att_i = 0.05;      % 姿态积分增益 (可选)
control_params.k_att_d = 0.1;       % 姿态微分增益 (可选)

% 一致性控制增益
control_params.k_consensus = 0.8;   % 一致性增益
control_params.k_formation = 1.0;   % 编队保持增益

% 物理参数 (从四旋翼参数复制)
control_params.mass = quad_params.mass;
control_params.gravity = quad_params.gravity;

% 控制输入限制
control_params.T_max = 20;          % 最大推力 (N)
control_params.T_min = 0;           % 最小推力 (N)
control_params.tau_max = 2;         % 最大力矩 (N*m)

% 控制器类型选择
control_params.use_pid = false;     % 是否使用PID控制器
control_params.use_lqr = false;     % 是否使用LQR控制器

%% 观测器参数
observer_params = struct();

% 系统矩阵 (线性化模型)
% 状态向量: [位置(3), 速度(3), 欧拉角(3), 角速度(3)]
observer_params.A = zeros(12, 12);
observer_params.A(1:3, 4:6) = eye(3);      % 位置-速度关系
observer_params.A(7:9, 10:12) = eye(3);    % 欧拉角-角速度关系

% 输入矩阵 (简化模型)
observer_params.B = zeros(12, 4);

% 输出矩阵 (测量位置和姿态)
observer_params.C = [eye(3), zeros(3, 9);          % 位置测量
                     zeros(3, 6), eye(3), zeros(3, 3)]; % 姿态测量

% 观测器增益矩阵设计
% 方法1: 简单增益设计
observer_params.observer_gain = 2 * eye(12, 6);

% 方法2: 极点配置设计 (可选)
% desired_poles = [-2, -2.1, -2.2, -2.3, -2.4, -2.5, -3, -3.1, -3.2, -3.3, -3.4, -3.5];
% observer_params.observer_gain = place(observer_params.A', observer_params.C', desired_poles)';

% 一致性观测器参数
observer_params.k_consensus_obs = 0.5;  % 观测器一致性增益
observer_params.use_adaptive_gain = false; % 是否使用自适应增益

% 噪声参数
observer_params.process_noise_cov = 0.01 * eye(12);  % 过程噪声协方差
observer_params.measurement_noise_cov = 0.01 * eye(6); % 测量噪声协方差

%% 仿真参数
sim_params = struct();

% 时间参数
sim_params.dt = 0.01;               % 仿真步长 (s)
sim_params.T_sim = 20;              % 仿真时间 (s)
sim_params.t_span = 0:sim_params.dt:sim_params.T_sim;

% 无人机数量和编队参数
sim_params.N_agents = 4;            % 无人机数量
sim_params.formation_type = 'square'; % 编队类型: 'square', 'triangle', 'line', 'circle'
sim_params.formation_size = 2;      % 编队尺寸 (m)
sim_params.formation_center = [5; 5; 5]; % 编队中心位置 (m)

% 初始条件
sim_params.initial_pos_noise = 0.1; % 初始位置噪声标准差 (m)
sim_params.initial_vel_noise = 0.05; % 初始速度噪声标准差 (m/s)
sim_params.initial_att_noise = 0.05; % 初始姿态噪声标准差 (rad)

% 测量噪声
sim_params.pos_measurement_noise = 0.01;  % 位置测量噪声标准差 (m)
sim_params.att_measurement_noise = 0.005; % 姿态测量噪声标准差 (rad)

% 通信拓扑类型
sim_params.topology_type = 'custom'; % 'complete', 'ring', 'star', 'custom'

% 自定义通信拓扑 (邻接矩阵)
sim_params.neighbors = [0, 1, 1, 0;    % 无人机1与2,3连接
                        1, 0, 1, 1;    % 无人机2与1,3,4连接
                        1, 1, 0, 1;    % 无人机3与1,2,4连接
                        0, 1, 1, 0];   % 无人机4与2,3连接

% 可视化参数
sim_params.plot_real_time = false;  % 是否实时绘图
sim_params.save_results = false;    % 是否保存结果
sim_params.animation_speed = 1;     % 动画播放速度

%% 参数验证
% 检查参数的合理性
if control_params.k_pos <= 0 || control_params.k_vel <= 0
    warning('控制增益应为正数');
end

if observer_params.k_consensus_obs <= 0
    warning('观测器一致性增益应为正数');
end

if sim_params.dt <= 0 || sim_params.T_sim <= 0
    error('仿真时间参数必须为正数');
end

if sim_params.N_agents < 2
    error('无人机数量至少为2架');
end

% 检查通信拓扑的连通性
if ~isConnected(sim_params.neighbors)
    warning('通信拓扑不连通，可能影响一致性收敛');
end

fprintf('参数配置完成！\n');
fprintf('无人机数量: %d\n', sim_params.N_agents);
fprintf('仿真时间: %.1f秒\n', sim_params.T_sim);
fprintf('编队类型: %s\n', sim_params.formation_type);
fprintf('编队尺寸: %.1f米\n', sim_params.formation_size);

end

function connected = isConnected(adj_matrix)
% 检查邻接矩阵表示的图是否连通
% 使用深度优先搜索算法

n = size(adj_matrix, 1);
visited = false(n, 1);

% 从第一个节点开始DFS
dfs(1, adj_matrix, visited);

% 如果所有节点都被访问，则图连通
connected = all(visited);

end

function dfs(node, adj_matrix, visited)
% 深度优先搜索
visited(node) = true;
n = size(adj_matrix, 1);

for i = 1:n
    if adj_matrix(node, i) == 1 && ~visited(i)
        dfs(i, adj_matrix, visited);
    end
end

end
