% 调试版本 - 检查数值问题
clear; clc; close all;

fprintf('=== 调试领导者-跟随者系统 ===\n');

%% 仿真参数设置
dt = 0.01;              % 仿真步长 (s)
T_sim = 10;             % 短时间仿真 (s)
t_span = 0:dt:T_sim;    % 时间向量
N_followers = 4;        % 跟随者数量
N_total = N_followers + 1; % 总无人机数量

%% 无人机物理参数
quad_params = struct();
quad_params.mass = 1.2;         % 质量 (kg)
quad_params.gravity = 9.81;     % 重力加速度 (m/s^2)
quad_params.Ixx = 0.0347;       % x轴转动惯量 (kg*m^2)
quad_params.Iyy = 0.0347;       % y轴转动惯量 (kg*m^2)
quad_params.Izz = 0.0617;       % z轴转动惯量 (kg*m^2)

%% 控制参数 (保守设置)
control_params = struct();
control_params.k_pos = 1.0;         % 位置控制增益
control_params.k_vel = 0.8;         % 速度控制增益
control_params.k_att = 1.5;         % 姿态控制增益
control_params.k_omega = 0.8;       % 角速度控制增益
control_params.mass = quad_params.mass;
control_params.gravity = quad_params.gravity;
control_params.T_max = 25;          % 最大推力 (N)
control_params.T_min = 0;           % 最小推力 (N)
control_params.tau_max = 3;         % 最大力矩 (N*m)

%% 编队参数 (更小的偏移)
formation_offset = [
    -1, -1, 0;    % UAV_1: 左后方
     1, -1, 0;    % UAV_2: 右后方
    -1,  1, 0;    % UAV_3: 左前方
     1,  1, 0     % UAV_4: 右前方
];
formation_offset = formation_offset';  % 转置为 (3x4)

%% 初始状态设置
x_agents = zeros(12, N_total);

% 领导者初始位置
x_agents(1:3, 1) = [5; 5; 9];  % 领导者起始位置

% 跟随者初始位置 (相对于领导者)
for i = 1:N_followers
    x_agents(1:3, i+1) = x_agents(1:3, 1) + formation_offset(:, i);
end

fprintf('初始状态:\n');
fprintf('领导者位置: [%.2f, %.2f, %.2f]\n', x_agents(1:3, 1));
for i = 1:N_followers
    fprintf('跟随者%d位置: [%.2f, %.2f, %.2f]\n', i, x_agents(1:3, i+1));
end

%% 数据存储
x_history = zeros(12, N_total, length(t_span));
tracking_error_pos = zeros(3, N_followers, length(t_span));
tracking_error_vel = zeros(3, N_followers, length(t_span));

%% 主仿真循环
fprintf('\n开始仿真...\n');
for k = 1:length(t_span)
    t = t_span(k);
    
    %% 领导者轨迹生成
    [leader_pos, leader_vel, leader_acc] = GenerateLeaderTrajectory(t);
    
    % 领导者期望状态
    x_desired_leader = zeros(12, 1);
    x_desired_leader(1:3) = leader_pos;
    x_desired_leader(4:6) = leader_vel;
    
    %% 跟随者期望轨迹计算
    x_desired_followers = zeros(12, N_followers);
    for i = 1:N_followers
        % 跟随者期望位置 = 领导者位置 + 编队偏移
        x_desired_followers(1:3, i) = leader_pos + formation_offset(:, i);
        x_desired_followers(4:6, i) = leader_vel;  % 期望速度与领导者相同
    end
    
    %% 领导者控制 (轨迹跟踪)
    u_leader = LeaderController(x_agents(:, 1), x_desired_leader, control_params);
    
    %% 跟随者控制 (编队跟踪)
    u_followers = zeros(4, N_followers);
    for i = 1:N_followers
        u_followers(:, i) = FollowerController(x_agents(:, i+1), ...
                                               x_desired_followers(:, i), ...
                                               control_params);
    end
    
    %% 动力学更新
    % 领导者动力学
    x_dot_leader = QuadrotorDynamics(t, x_agents(:, 1), u_leader, quad_params);
    x_agents(:, 1) = x_agents(:, 1) + dt * x_dot_leader;
    
    % 跟随者动力学
    for i = 1:N_followers
        x_dot_follower = QuadrotorDynamics(t, x_agents(:, i+1), u_followers(:, i), quad_params);
        x_agents(:, i+1) = x_agents(:, i+1) + dt * x_dot_follower;
    end
    
    %% 跟踪误差计算
    for i = 1:N_followers
        tracking_error_pos(:, i, k) = x_agents(1:3, i+1) - x_desired_followers(1:3, i);
        tracking_error_vel(:, i, k) = x_agents(4:6, i+1) - x_desired_followers(4:6, i);
    end

    %% 数据存储
    x_history(:, :, k) = x_agents;
    
    % 每秒输出一次调试信息
    if mod(k, 100) == 0
        fprintf('t=%.1fs: ', t);
        fprintf('领导者位置=[%.2f,%.2f,%.2f] ', x_agents(1:3, 1));
        pos_err_norm = norm(tracking_error_pos(:, 1, k));
        fprintf('跟随者1位置误差=%.3f\n', pos_err_norm);
        
        % 检查异常值
        if pos_err_norm > 10
            fprintf('警告: 位置误差过大!\n');
            fprintf('跟随者1当前位置: [%.2f, %.2f, %.2f]\n', x_agents(1:3, 2));
            fprintf('跟随者1期望位置: [%.2f, %.2f, %.2f]\n', x_desired_followers(1:3, 1));
            fprintf('控制输入: [%.2f, %.2f, %.2f, %.2f]\n', u_followers(:, 1));
            break;
        end
    end
end

%% 简单可视化
figure('Name', '调试结果');

% 位置跟踪误差
subplot(2, 2, 1);
pos_error_norm = zeros(N_followers, length(t_span));
for i = 1:N_followers
    for k = 1:length(t_span)
        pos_error_norm(i, k) = norm(tracking_error_pos(:, i, k));
    end
    plot(t_span, pos_error_norm(i, :), 'LineWidth', 1.5, ...
         'DisplayName', sprintf('UAV_%d', i));
    hold on;
end
xlabel('Time(s)'); ylabel('Position Error (m)');
title('位置跟踪误差');
legend; grid on;

% 速度跟踪误差
subplot(2, 2, 2);
vel_error_norm = zeros(N_followers, length(t_span));
for i = 1:N_followers
    for k = 1:length(t_span)
        vel_error_norm(i, k) = norm(tracking_error_vel(:, i, k));
    end
    plot(t_span, vel_error_norm(i, :), 'LineWidth', 1.5, ...
         'DisplayName', sprintf('UAV_%d', i));
    hold on;
end
xlabel('Time(s)'); ylabel('Velocity Error (m/s)');
title('速度跟踪误差');
legend; grid on;

% 3D轨迹
subplot(2, 2, [3, 4]);
% 领导者轨迹
leader_traj = squeeze(x_history(1:3, 1, :));
plot3(leader_traj(1, :), leader_traj(2, :), leader_traj(3, :), 'k-', 'LineWidth', 2, 'DisplayName', 'Leader');
hold on;

% 跟随者轨迹
colors = ['r', 'g', 'c', 'm'];
for i = 1:N_followers
    follower_traj = squeeze(x_history(1:3, i+1, :));
    plot3(follower_traj(1, :), follower_traj(2, :), follower_traj(3, :), ...
          [colors(i), '-'], 'LineWidth', 1.5, 'DisplayName', sprintf('Follower_%d', i));
end

xlabel('X (m)'); ylabel('Y (m)'); zlabel('Z (m)');
title('3D轨迹');
legend; grid on; axis equal;

fprintf('\n调试完成!\n');
fprintf('最大位置误差: %.3f m\n', max(pos_error_norm(:)));
fprintf('最大速度误差: %.3f m/s\n', max(vel_error_norm(:)));
