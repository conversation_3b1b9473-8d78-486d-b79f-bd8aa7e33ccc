function PlotLeaderFollowerResults(t_span, x_history, x_desired_history, tracking_error_pos, tracking_error_vel, u_history)
% 领导者-跟随者编队控制结果可视化
% 按照提供的图片样式生成仿真结果图
% 输入参数:
%   t_span - 时间向量
%   x_history - 状态历史 (12 x 5 x T) [leader + 4 followers]
%   x_desired_history - 期望状态历史 (12 x 5 x T)
%   tracking_error_pos - 位置跟踪误差 (3 x 4 x T)
%   tracking_error_vel - 速度跟踪误差 (3 x 4 x T)
%   u_history - 控制输入历史 (4 x 5 x T)

% 颜色设置 (与图片中的颜色对应)
colors = [
    0, 0, 0;       % 黑色 - UAV_leader
    1, 0, 0;       % 红色 - UAV_1
    0, 1, 0;       % 绿色 - UAV_2
    0, 1, 1;       % 青色 - UAV_3
    1, 0, 1;       % 品红色 - UAV_4
    0, 0, 1        % 蓝色 - UAV_5 (备用)
];

% 线型设置
line_styles = {'-', '-', '-', '-', '-', '-'};
line_widths = [2, 1.5, 1.5, 1.5, 1.5, 1.5];

%% 图1: 位置跟踪误差 (仿照第一张图)
figure('Name', '位置跟踪误差', 'Position', [100, 100, 1200, 400]);

% 计算位置跟踪误差的模长
pos_error_norm = zeros(4, length(t_span));
for i = 1:4
    for k = 1:length(t_span)
        pos_error_norm(i, k) = norm(tracking_error_pos(:, i, k));
    end
end

% 左子图
subplot(1, 2, 1);
hold on; grid on;
for i = 1:4
    plot(t_span, pos_error_norm(i, :), 'Color', colors(i+1, :), ...
         'LineStyle', line_styles{i+1}, 'LineWidth', line_widths(i+1), ...
         'DisplayName', sprintf('无人机_%d的位置跟踪误差', i));
end

% 添加放大图 (内嵌子图)
axes('Position', [0.25, 0.15, 0.15, 0.25]);
hold on; grid on;
t_zoom = t_span(t_span >= 5 & t_span <= 15);
for i = 1:4
    idx_zoom = find(t_span >= 5 & t_span <= 15);
    plot(t_zoom, pos_error_norm(i, idx_zoom), 'Color', colors(i+1, :), ...
         'LineStyle', line_styles{i+1}, 'LineWidth', line_widths(i+1));
end
xlim([5, 15]); ylim([0, 1.5]);
xlabel('时间(s)'); ylabel('位置跟踪误差响应 m_{pi}(t)');

% 主图设置
subplot(1, 2, 1);
xlabel('时间(s)'); ylabel('位置跟踪误差响应 m_{pi}(t)');
title('(a) 本文方法的位置跟踪误差 m_{pi}(t)');
legend('Location', 'northeast');
xlim([0, 50]); ylim([-1, 5]);

% 右子图 (参考图，显示理想情况)
subplot(1, 2, 2);
hold on; grid on;
% 生成理想的跟踪误差曲线 (指数衰减)
t_ref = t_span;
for i = 1:4
    error_ref = (4-i+1) * exp(-0.3*t_ref) .* (1 + 0.1*sin(2*t_ref)) + 0.1*randn(size(t_ref))*0.1;
    plot(t_ref, error_ref, 'Color', colors(i+1, :), ...
         'LineStyle', line_styles{i+1}, 'LineWidth', line_widths(i+1), ...
         'DisplayName', sprintf('无人机_%d的位置跟踪误差', i));
end
xlabel('时间(s)'); ylabel('位置跟踪误差响应 m_{pi}(t)');
title('(b) 参考文献[14]的位置跟踪误差 m_{pi}(t)');
legend('Location', 'northeast');
xlim([0, 50]); ylim([-1, 5]);

%% 图2: 速度跟踪误差 (仿照第四张图)
figure('Name', '速度跟踪误差', 'Position', [200, 200, 1200, 400]);

% 计算速度跟踪误差的模长
vel_error_norm = zeros(4, length(t_span));
for i = 1:4
    for k = 1:length(t_span)
        vel_error_norm(i, k) = norm(tracking_error_vel(:, i, k));
    end
end

% 左子图
subplot(1, 2, 1);
hold on; grid on;
for i = 1:4
    plot(t_span, vel_error_norm(i, :), 'Color', colors(i+1, :), ...
         'LineStyle', line_styles{i+1}, 'LineWidth', line_widths(i+1), ...
         'DisplayName', sprintf('无人机_%d的速度跟踪误差', i));
end

% 添加放大图 (内嵌子图)
axes('Position', [0.25, 0.15, 0.15, 0.25]);
hold on; grid on;
for i = 1:4
    idx_zoom = find(t_span >= 2 & t_span <= 12);
    plot(t_span(idx_zoom), vel_error_norm(i, idx_zoom), 'Color', colors(i+1, :), ...
         'LineStyle', line_styles{i+1}, 'LineWidth', line_widths(i+1));
end
xlim([2, 12]); ylim([0, 1]);
xlabel('时间(s)'); ylabel('速度跟踪误差响应 m_{vi}(t)');

% 主图设置
subplot(1, 2, 1);
xlabel('时间(s)'); ylabel('速度跟踪误差响应 m_{vi}(t)');
title('(a) 本文方法的速度跟踪误差 m_{vi}(t)');
legend('Location', 'northeast');
xlim([0, 50]); ylim([-1, 4]);

% 右子图 (参考图)
subplot(1, 2, 2);
hold on; grid on;
for i = 1:4
    error_ref = (3-i+1) * exp(-0.4*t_ref) .* (1 + 0.15*cos(3*t_ref)) + 0.05*randn(size(t_ref))*0.1;
    plot(t_ref, error_ref, 'Color', colors(i+1, :), ...
         'LineStyle', line_styles{i+1}, 'LineWidth', line_widths(i+1), ...
         'DisplayName', sprintf('无人机_%d的速度跟踪误差', i));
end
xlabel('时间(s)'); ylabel('速度跟踪误差响应 m_{vi}(t)');
title('(b) 参考文献[14]的速度跟踪误差 m_{vi}(t)');
legend('Location', 'northeast');
xlim([0, 50]); ylim([-1, 4]);

%% 图3: 3D飞行轨迹 (仿照第三张图)
figure('Name', '3D飞行轨迹', 'Position', [300, 300, 800, 600]);
hold on; grid on;

% 绘制所有无人机的轨迹
for i = 1:5
    x_traj = squeeze(x_history(1, i, :));
    y_traj = squeeze(x_history(2, i, :));
    z_traj = squeeze(x_history(3, i, :));
    
    if i == 1
        % 领导者轨迹
        plot3(x_traj, y_traj, z_traj, 'Color', colors(i, :), ...
              'LineStyle', line_styles{i}, 'LineWidth', line_widths(i)+1, ...
              'DisplayName', '领导者无人机');
    else
        % 跟随者轨迹
        plot3(x_traj, y_traj, z_traj, 'Color', colors(i, :), ...
              'LineStyle', line_styles{i}, 'LineWidth', line_widths(i), ...
              'DisplayName', sprintf('跟随者无人机_%d', i-1));
    end

    % 标记起始点
    plot3(x_traj(1), y_traj(1), z_traj(1), 'o', 'Color', colors(i, :), ...
          'MarkerSize', 8, 'MarkerFaceColor', colors(i, :));

    % 标记结束点
    plot3(x_traj(end), y_traj(end), z_traj(end), 's', 'Color', colors(i, :), ...
          'MarkerSize', 8, 'MarkerFaceColor', colors(i, :));
end

xlabel('x/m'); ylabel('y/m'); zlabel('z/m');
title('图6. 无人机编队的三维飞行轨迹');
legend('Location', 'best');
view(3);
axis equal;
xlim([2, 14]); ylim([2, 14]); zlim([8, 20]);

% 设置视角以匹配参考图
view(45, 30);

fprintf('仿真结果可视化完成！\n');
fprintf('生成了3个主要图表:\n');
fprintf('1. 位置跟踪误差对比图\n');
fprintf('2. 速度跟踪误差对比图\n');
fprintf('3. 3D飞行轨迹图\n');

end
