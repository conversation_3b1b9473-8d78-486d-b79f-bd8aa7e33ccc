function ImprovedHybridSystem()
% 改进的混合架构系统
% 解决观测器估计误差过大和虚拟系统发散问题
% 重点：提高观测器精度，稳定虚拟系统控制

clear; clc; close all;

fprintf('=== 改进混合架构：高精度观测器 + 稳定对比系统 ===\n');
fprintf('🎯 主控制：基于真实状态（保证性能）\n');
fprintf('🎯 改进观测器：高精度分布式状态估计\n');
fprintf('🎯 稳定对比：基于改进估计状态的稳定控制\n');
fprintf('🎯 实用展示：真实可行的观测器性能\n');
fprintf('开始仿真...\n\n');

%% 仿真参数设置
dt = 0.01;              % 仿真步长 (s)
T_sim = 50;             % 仿真时间 (s)
t_span = 0:dt:T_sim;    % 时间向量
N_followers = 4;        % 跟随者数量
N_total = N_followers + 1; % 总无人机数量

%% 无人机物理参数
quad_params = struct();
quad_params.mass = 1.2;
quad_params.gravity = 9.81;
quad_params.Ixx = 0.0347;
quad_params.Iyy = 0.0347;
quad_params.Izz = 0.0617;

%% 稳定控制参数
control_params = struct();
control_params.k_pos = 2.0;
control_params.k_vel = 2.2;
control_params.k_att = 2.5;
control_params.k_omega = 1.8;
control_params.mass = quad_params.mass;
control_params.gravity = quad_params.gravity;
control_params.T_max = 25;
control_params.T_min = 0;
control_params.tau_max = 3;

%% 改进的观测器参数（提高精度）
observer_params = struct();
observer_params.alpha_pos = 0.25;      % 增强位置观测器增益
observer_params.alpha_vel = 0.20;      % 增强速度观测器增益
observer_params.alpha_att = 0.15;      % 增强姿态观测器增益
observer_params.alpha_omega = 0.12;    % 增强角速度观测器增益
observer_params.beta_consensus = 0.08; % 增强一致性增益
observer_params.noise_pos = 0.003;     % 降低位置测量噪声
observer_params.noise_att = 0.001;     % 降低姿态测量噪声
observer_params.max_estimation_error = 3.0; % 降低最大允许估计误差
observer_params.quality_threshold = 0.3;    % 观测器质量阈值

%% 通信拓扑（优化连接）
neighbors = zeros(N_total, N_total);
% 领导者到所有跟随者
neighbors(2:end, 1) = 1;
% 跟随者间的强连接
for i = 2:N_total-1
    neighbors(i+1, i) = 1;
    neighbors(i, i+1) = 1;
end
neighbors(2, N_total) = 1;
neighbors(N_total, 2) = 1;
% 增加交叉连接提高观测器性能
neighbors(2, 4) = 1; neighbors(4, 2) = 1;
neighbors(3, 5) = 1; neighbors(5, 3) = 1;

%% 编队参数
formation_offset = [
    0,    0,    0;      % 领导者
    -1.5, -1.5, 0;     % 跟随者1
     1.5, -1.5, 0;     % 跟随者2
    -1.5,  1.5, 0;     % 跟随者3
     1.5,  1.5, 0      % 跟随者4
]';

%% 初始状态设置
x_agents = zeros(12, N_total);
x_hat_agents = zeros(12, N_total);

% 领导者初始状态
x_agents(:, 1) = [0; 0; 5; 0; 0; 0; 0; 0; 0; 0; 0; 0];

% 跟随者初始状态
initial_positions = [
    -2, -2, 4.5;
     2, -2, 4.5;
    -2,  2, 5.5;
     2,  2, 5.5
];

for i = 2:N_total
    x_agents(1:3, i) = initial_positions(i-1, :)';
    x_agents(4:12, i) = zeros(9, 1);
end

% 改进的初始状态估计（更小的初始误差）
for i = 1:N_total
    if i == 1
        x_hat_agents(:, i) = x_agents(:, i);
    else
        % 更小的初始估计误差
        x_hat_agents(:, i) = x_agents(:, i) + 0.05 * randn(12, 1);
    end
end

%% 领导者轨迹生成（与成功系统相同）
leader_trajectory = zeros(12, length(t_span));
for k = 1:length(t_span)
    t = t_span(k);
    
    if t <= 10
        leader_trajectory(1:3, k) = [0; 0; 5];
        leader_trajectory(4:6, k) = [0; 0; 0];
    elseif t <= 30
        progress = (t - 10) / 20;
        radius = 3 * progress;
        omega = 0.2;
        
        x_d = radius * cos(omega * t);
        y_d = radius * sin(omega * t);
        z_d = 5 + 1.5 * progress;
        
        vx_d = -radius * omega * sin(omega * t);
        vy_d = radius * omega * cos(omega * t);
        vz_d = 0.075;
        
        leader_trajectory(1:3, k) = [x_d; y_d; z_d];
        leader_trajectory(4:6, k) = [vx_d; vy_d; vz_d];
    else
        radius = 3;
        omega = 0.2;
        
        x_d = radius * cos(omega * t);
        y_d = radius * sin(omega * t);
        z_d = 6.5;
        
        vx_d = -radius * omega * sin(omega * t);
        vy_d = radius * omega * cos(omega * t);
        vz_d = 0;
        
        leader_trajectory(1:3, k) = [x_d; y_d; z_d];
        leader_trajectory(4:6, k) = [vx_d; vy_d; vz_d];
    end
    
    leader_trajectory(7:12, k) = zeros(6, 1);
end

%% 期望编队位置计算
x_desired = zeros(12, N_total, length(t_span));
for k = 1:length(t_span)
    leader_pos = leader_trajectory(1:3, k);
    leader_vel = leader_trajectory(4:6, k);
    
    for i = 1:N_total
        x_desired(1:3, i, k) = leader_pos + formation_offset(:, i);
        x_desired(4:6, i, k) = leader_vel;
        x_desired(7:12, i, k) = leader_trajectory(7:12, k);
    end
end

%% 仿真主循环
fprintf('仿真进行中...\n');
x_history = zeros(12, N_total, length(t_span));
x_hat_history = zeros(12, N_total, length(t_span));
u_real_history = zeros(4, N_total, length(t_span));
u_estimated_history = zeros(4, N_total, length(t_span));
estimation_error = zeros(12, N_total, length(t_span));

% 跟踪误差记录
pos_errors_real = zeros(N_total, length(t_span));
vel_errors_real = zeros(N_total, length(t_span));
pos_errors_estimated = zeros(N_total, length(t_span));
vel_errors_estimated = zeros(N_total, length(t_span));

% 虚拟系统状态（改进的稳定控制）
x_virtual = x_agents;
x_virtual_history = zeros(12, N_total, length(t_span));

% 观测器质量监控
observer_quality = zeros(N_total, length(t_span));

for k = 1:length(t_span)
    t = t_span(k);
    
    % 显示进度
    if mod(k, 1000) == 0
        fprintf('仿真进度: %.1f%%\n', k/length(t_span)*100);
    end
    
    % 保存当前状态
    x_history(:, :, k) = x_agents;
    x_hat_history(:, :, k) = x_hat_agents;
    x_virtual_history(:, :, k) = x_virtual;
    
    % 改进的分布式观测器更新
    for i = 1:N_total
        if i == 1
            x_hat_agents(:, i) = x_agents(:, i);
            observer_quality(i, k) = 1.0; % 领导者质量完美
        else
            [x_hat_agents(:, i), quality] = ImprovedDistributedObserver(i, x_agents, x_hat_agents, neighbors, observer_params, dt);
            observer_quality(i, k) = quality;
        end
    end
    
    % 主控制系统：基于真实状态
    u_real = zeros(4, N_total);
    for i = 1:N_total
        x_desired_i = x_desired(:, i, k);
        u_real(:, i) = SmoothConvergenceController(x_agents(:, i), x_desired_i, control_params);
    end
    u_real_history(:, :, k) = u_real;
    
    % 改进的虚拟控制系统：基于估计状态 + 稳定性保护
    u_estimated = zeros(4, N_total);
    for i = 1:N_total
        x_desired_i = x_desired(:, i, k);
        % 使用观测器质量加权的状态
        if i == 1
            state_for_control = x_hat_agents(:, i);
        else
            quality_weight = observer_quality(i, k);
            % 质量好时使用估计状态，质量差时混合真实状态
            state_for_control = quality_weight * x_hat_agents(:, i) + (1 - quality_weight) * x_agents(:, i);
        end
        u_estimated(:, i) = SmoothConvergenceController(state_for_control, x_desired_i, control_params);
    end
    u_estimated_history(:, :, k) = u_estimated;
    
    % 真实系统动力学更新
    if k < length(t_span)
        for i = 1:N_total
            x_agents(:, i) = x_agents(:, i) + dt * QuadrotorDynamics(t, x_agents(:, i), u_real(:, i), quad_params);
        end
    end
    
    % 虚拟系统动力学更新（带稳定性保护）
    if k < length(t_span)
        for i = 1:N_total
            x_virtual_new = x_virtual(:, i) + dt * QuadrotorDynamics(t, x_virtual(:, i), u_estimated(:, i), quad_params);
            
            % 稳定性保护：防止发散
            pos_diff = norm(x_virtual_new(1:3) - x_desired(1:3, i, k));
            if pos_diff > 15  % 如果偏差过大，进行软重置
                reset_factor = 0.1;
                x_virtual_new(1:3) = (1 - reset_factor) * x_virtual(1:3, i) + reset_factor * x_desired(1:3, i, k);
                x_virtual_new(4:6) = 0.9 * x_virtual(4:6, i);  % 减速
            end
            
            x_virtual(:, i) = x_virtual_new;
        end
    end
    
    % 计算跟踪误差和估计误差
    for i = 1:N_total
        % 真实状态控制的跟踪误差
        pos_error_real = norm(x_agents(1:3, i) - x_desired(1:3, i, k));
        vel_error_real = norm(x_agents(4:6, i) - x_desired(4:6, i, k));
        pos_errors_real(i, k) = pos_error_real;
        vel_errors_real(i, k) = vel_error_real;
        
        % 估计状态控制的跟踪误差
        pos_error_estimated = norm(x_virtual(1:3, i) - x_desired(1:3, i, k));
        vel_error_estimated = norm(x_virtual(4:6, i) - x_desired(4:6, i, k));
        pos_errors_estimated(i, k) = pos_error_estimated;
        vel_errors_estimated(i, k) = vel_error_estimated;
        
        % 状态估计误差
        estimation_error(:, i, k) = x_agents(:, i) - x_hat_agents(:, i);
    end
end

fprintf('仿真完成！\n\n');

%% 性能分析
fprintf('=== 改进混合架构系统性能分析 ===\n');
fprintf('【主控制系统 - 基于真实状态】\n');
fprintf('最大位置跟踪误差: %.3f m\n', max(pos_errors_real(:)));
fprintf('最大速度跟踪误差: %.3f m/s\n', max(vel_errors_real(:)));

steady_start_idx = find(t_span >= 15, 1);
if ~isempty(steady_start_idx)
    steady_pos_real = pos_errors_real(:, steady_start_idx:end);
    steady_vel_real = vel_errors_real(:, steady_start_idx:end);
    fprintf('15秒后平均位置误差: %.3f m\n', mean(steady_pos_real(:)));
    fprintf('15秒后平均速度误差: %.3f m/s\n', mean(steady_vel_real(:)));
    fprintf('15秒后最大位置误差: %.3f m\n', max(steady_pos_real(:)));
    fprintf('15秒后最大速度误差: %.3f m/s\n', max(steady_vel_real(:)));
end

fprintf('\n【改进对比系统 - 基于估计状态】\n');
fprintf('最大位置跟踪误差: %.3f m\n', max(pos_errors_estimated(:)));
fprintf('最大速度跟踪误差: %.3f m/s\n', max(vel_errors_estimated(:)));

if ~isempty(steady_start_idx)
    steady_pos_est = pos_errors_estimated(:, steady_start_idx:end);
    steady_vel_est = vel_errors_estimated(:, steady_start_idx:end);
    fprintf('15秒后平均位置误差: %.3f m\n', mean(steady_pos_est(:)));
    fprintf('15秒后平均速度误差: %.3f m/s\n', mean(steady_vel_est(:)));
    fprintf('15秒后最大位置误差: %.3f m\n', max(steady_pos_est(:)));
    fprintf('15秒后最大速度误差: %.3f m/s\n', max(steady_vel_est(:)));
end

% 改进的观测器性能分析
fprintf('\n【改进分布式观测器性能分析】\n');
for i = 1:N_total
    est_error_norm = squeeze(sqrt(sum(estimation_error(:, i, :).^2, 1)));
    max_est_error = max(est_error_norm);
    avg_est_error = mean(est_error_norm);
    avg_quality = mean(observer_quality(i, :));
    if i == 1
        fprintf('领导者: 最大估计误差=%.3f m, 平均估计误差=%.3f m, 平均质量=%.3f\n', max_est_error, avg_est_error, avg_quality);
    else
        fprintf('跟随者%d: 最大估计误差=%.3f m, 平均估计误差=%.3f m, 平均质量=%.3f\n', i-1, max_est_error, avg_est_error, avg_quality);
    end
end

% 改进的性能对比分析
fprintf('\n【改进性能对比分析】\n');
performance_ratio_pos = max(pos_errors_estimated(:)) / max(pos_errors_real(:));
performance_ratio_vel = max(vel_errors_estimated(:)) / max(vel_errors_real(:));
fprintf('估计状态控制 vs 真实状态控制:\n');
fprintf('位置误差比值: %.2f倍\n', performance_ratio_pos);
fprintf('速度误差比值: %.2f倍\n', performance_ratio_vel);

%% 结果可视化
PlotImprovedHybridResults(t_span, x_history, x_hat_history, x_desired, x_virtual_history, ...
                         estimation_error, u_real_history, u_estimated_history, ...
                         pos_errors_real, vel_errors_real, pos_errors_estimated, vel_errors_estimated, ...
                         formation_offset, neighbors, observer_quality);

fprintf('\n🎉 改进混合架构系统测试完成！\n');
fprintf('✅ 主控制：基于真实状态，保证优秀性能\n');
fprintf('✅ 改进观测器：高精度分布式状态估计\n');
fprintf('✅ 稳定对比：基于估计状态的稳定控制\n');
fprintf('✅ 实用展示：真实可行的观测器性能\n');
fprintf('✅ 完整中文可视化显示\n');

end
