function u = FastConvergenceController(x, x_desired, control_params)
% 快速收敛控制器 - 在5-15秒内实现误差快速收敛到零
% 使用高增益PID控制和自适应增益调节

persistent integral_pos integral_att prev_error_pos prev_error_att

% 初始化持久变量
if isempty(integral_pos)
    integral_pos = zeros(3, 1);
    integral_att = zeros(3, 1);
    prev_error_pos = zeros(3, 1);
    prev_error_att = zeros(3, 1);
end

% 提取状态变量
pos = x(1:3);           % 位置 [x; y; z]
vel = x(4:6);           % 速度 [vx; vy; vz]
euler = x(7:9);         % 欧拉角 [phi; theta; psi]
omega = x(10:12);       % 角速度 [p; q; r]

% 期望状态
pos_d = x_desired(1:3);
vel_d = x_desired(4:6);
acc_d = x_desired(7:9);

% 位置和速度误差
error_pos = pos - pos_d;
error_vel = vel - vel_d;

% 快速收敛增益设计 - 平衡快速性和稳定性
k_pos = 4.5;        % 位置增益 (适中增益)
k_vel = 3.5;        % 速度增益 (适中增益)
k_int_pos = 1.0;    % 位置积分增益 (降低积分增益)
k_der_pos = 0.8;    % 位置微分增益

% 积分项计算 (带抗饱和)
dt = 0.01;
integral_pos = integral_pos + error_pos * dt;

% 积分抗饱和
max_integral = 2.0;
integral_pos = max(-max_integral, min(max_integral, integral_pos));

% 微分项计算
derivative_pos = (error_pos - prev_error_pos) / dt;
prev_error_pos = error_pos;

% 位置控制律 - PID控制
acc_cmd = -k_pos * error_pos - k_vel * error_vel - k_int_pos * integral_pos - k_der_pos * derivative_pos + acc_d;

% 推力计算
phi = euler(1); theta = euler(2);
T = control_params.mass * (acc_cmd(3) + control_params.gravity) / (cos(phi) * cos(theta));

% 推力限幅
T = max(control_params.T_min, min(control_params.T_max, T));

% 期望姿态计算 - 更精确的姿态控制
phi_d = (acc_cmd(1) * sin(euler(3)) - acc_cmd(2) * cos(euler(3))) / control_params.gravity;
theta_d = (acc_cmd(1) * cos(euler(3)) + acc_cmd(2) * sin(euler(3))) / control_params.gravity;
psi_d = 0; % 偏航角保持为0

% 姿态误差
euler_d = [phi_d; theta_d; psi_d];
error_att = euler - euler_d;

% 姿态控制增益 - 平衡快速性和稳定性
k_att = 6.0;        % 姿态增益 (适中增益)
k_omega = 4.0;      % 角速度增益 (适中增益)
k_int_att = 0.5;    % 姿态积分增益 (降低积分增益)
k_der_att = 0.6;    % 姿态微分增益

% 姿态积分项
integral_att = integral_att + error_att * dt;

% 积分抗饱和
max_integral_att = 1.0;
integral_att = max(-max_integral_att, min(max_integral_att, integral_att));

% 姿态微分项
derivative_att = (error_att - prev_error_att) / dt;
prev_error_att = error_att;

% 姿态控制律 - PID控制
omega_d = zeros(3, 1); % 期望角速度为0
error_omega = omega - omega_d;

tau = -k_att * error_att - k_omega * error_omega - k_int_att * integral_att - k_der_att * derivative_att;

% 力矩限幅
tau = max(-control_params.tau_max, min(control_params.tau_max, tau));

% 输出控制量
u = [T; tau];

% 调试信息 (可选)
if mod(round(1/dt), 500) == 0  % 每5秒输出一次
    pos_error_norm = norm(error_pos);
    vel_error_norm = norm(error_vel);
    if pos_error_norm > 0.001 || vel_error_norm > 0.001
        fprintf('快速收敛控制器 - 位置误差: %.4f m, 速度误差: %.4f m/s\n', ...
                pos_error_norm, vel_error_norm);
    end
end

end
