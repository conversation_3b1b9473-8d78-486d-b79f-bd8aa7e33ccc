% 领导者-跟随者系统测试脚本
clear; clc; close all;

fprintf('=== 领导者-跟随者系统测试 ===\n');

%% 测试1: 领导者轨迹生成
fprintf('测试1: 领导者轨迹生成...\n');
try
    t_test = 0:0.1:10;
    pos_history = zeros(3, length(t_test));
    
    for i = 1:length(t_test)
        [pos, vel, acc] = GenerateLeaderTrajectory(t_test(i));
        pos_history(:, i) = pos;
        
        if any(isnan(pos)) || any(isnan(vel)) || any(isnan(acc))
            error('轨迹生成包含NaN值');
        end
    end
    
    % 简单可视化
    figure('Name', '领导者轨迹测试');
    plot3(pos_history(1, :), pos_history(2, :), pos_history(3, :), 'b-', 'LineWidth', 2);
    xlabel('X (m)'); ylabel('Y (m)'); zlabel('Z (m)');
    title('领导者轨迹测试');
    grid on; axis equal;
    
    fprintf('  ✓ 领导者轨迹生成测试通过\n');
catch ME
    fprintf('  ✗ 领导者轨迹生成测试失败: %s\n', ME.message);
end

%% 测试2: 控制器测试
fprintf('测试2: 控制器测试...\n');
try
    % 控制参数
    params.k_pos = 3.0;
    params.k_vel = 2.0;
    params.k_att = 1.5;
    params.k_omega = 0.8;
    params.mass = 1.2;
    params.gravity = 9.81;
    params.T_max = 25;
    params.T_min = 0;
    params.tau_max = 3;
    
    % 测试状态
    x_current = zeros(12, 1);
    x_desired = [5; 5; 10; 0; 0; 0; 0; 0; 0; 0; 0; 0];
    
    % 测试领导者控制器
    u_leader = LeaderController(x_current, x_desired, params);
    if any(isnan(u_leader)) || any(isinf(u_leader))
        error('领导者控制器输出异常');
    end
    
    % 测试跟随者控制器
    u_follower = FollowerController(x_current, x_desired, params);
    if any(isnan(u_follower)) || any(isinf(u_follower))
        error('跟随者控制器输出异常');
    end
    
    fprintf('  ✓ 控制器测试通过\n');
catch ME
    fprintf('  ✗ 控制器测试失败: %s\n', ME.message);
end

%% 测试3: 短时间仿真
fprintf('测试3: 短时间仿真 (5秒)...\n');
try
    % 仿真参数
    dt = 0.01;
    T_sim = 5;
    t_span = 0:dt:T_sim;
    
    % 无人机参数
    quad_params.mass = 1.2;
    quad_params.gravity = 9.81;
    quad_params.Ixx = 0.0347;
    quad_params.Iyy = 0.0347;
    quad_params.Izz = 0.0617;
    
    % 初始状态
    x_leader = [5; 5; 9; 0; 0; 0; 0; 0; 0; 0; 0; 0];
    x_follower = [3; 3; 9; 0; 0; 0; 0; 0; 0; 0; 0; 0];
    
    % 运行几个仿真步骤
    for k = 1:min(100, length(t_span))
        t = t_span(k);
        
        % 领导者轨迹
        [leader_pos, leader_vel, ~] = GenerateLeaderTrajectory(t);
        x_desired_leader = [leader_pos; leader_vel; zeros(6, 1)];
        
        % 跟随者期望
        x_desired_follower = x_desired_leader;
        x_desired_follower(1:3) = leader_pos + [-2; -2; 0];  % 编队偏移
        
        % 控制器
        u_leader = LeaderController(x_leader, x_desired_leader, params);
        u_follower = FollowerController(x_follower, x_desired_follower, params);
        
        % 动力学更新
        x_dot_leader = QuadrotorDynamics(t, x_leader, u_leader, quad_params);
        x_dot_follower = QuadrotorDynamics(t, x_follower, u_follower, quad_params);
        
        x_leader = x_leader + dt * x_dot_leader;
        x_follower = x_follower + dt * x_dot_follower;
        
        % 检查数值稳定性
        if any(isnan(x_leader)) || any(isnan(x_follower))
            error('仿真中出现NaN值');
        end
    end
    
    fprintf('  ✓ 短时间仿真测试通过\n');
catch ME
    fprintf('  ✗ 短时间仿真测试失败: %s\n', ME.message);
end

%% 测试总结
fprintf('\n=== 测试总结 ===\n');
fprintf('领导者-跟随者系统基本功能测试完成\n');
fprintf('如果所有测试都通过，可以运行完整仿真:\n');
fprintf('>> LeaderFollowerFormation\n');
fprintf('\n预期结果:\n');
fprintf('- 1个领导者按螺旋轨迹飞行\n');
fprintf('- 4个跟随者保持编队跟踪领导者\n');
fprintf('- 生成位置/速度跟踪误差图和3D轨迹图\n');
