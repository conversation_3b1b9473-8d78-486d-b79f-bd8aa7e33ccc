function u = SimpleStableController(x, x_desired, control_params)
% 极简稳定控制器 - 确保基本的跟踪性能
% 基于经典PD控制，专注于稳定性而非复杂功能

% 提取状态变量
pos = x(1:3);           % 位置 [x; y; z]
vel = x(4:6);           % 速度 [vx; vy; vz]
euler = x(7:9);         % 欧拉角 [phi; theta; psi]
omega = x(10:12);       % 角速度 [p; q; r]

% 期望状态
pos_d = x_desired(1:3);
vel_d = x_desired(4:6);

% 位置和速度误差
error_pos = pos_d - pos;  % 注意：这里是期望减去实际
error_vel = vel_d - vel;

%% 位置控制器（外环）
% 简单PD控制
kp_pos = 3.0;  % 位置增益
kd_pos = 2.5;  % 速度增益

% 期望加速度
acc_desired = kp_pos * error_pos + kd_pos * error_vel;

% 添加重力补偿
gravity_compensation = [0; 0; control_params.gravity];
acc_desired = acc_desired + gravity_compensation;

% 期望推力（总推力）
T_desired = control_params.mass * norm(acc_desired);

% 限制推力范围
T_desired = max(control_params.T_min, min(control_params.T_max, T_desired));

%% 姿态控制器（内环）
% 期望姿态角（从期望加速度计算）
if norm(acc_desired) > 0.1
    % 期望的机体z轴方向
    z_body_desired = acc_desired / norm(acc_desired);
    
    % 期望的roll和pitch角
    phi_desired = asin(-z_body_desired(2));
    theta_desired = atan2(z_body_desired(1), z_body_desired(3));
else
    phi_desired = 0;
    theta_desired = 0;
end

% 期望偏航角（保持当前偏航或设为0）
psi_desired = 0;

% 期望姿态
euler_desired = [phi_desired; theta_desired; psi_desired];

% 姿态误差
error_att = euler_desired - euler;

% 处理角度wrap-around
for i = 1:3
    while error_att(i) > pi
        error_att(i) = error_att(i) - 2*pi;
    end
    while error_att(i) < -pi
        error_att(i) = error_att(i) + 2*pi;
    end
end

% 简单PD姿态控制
kp_att = 4.0;  % 姿态增益
kd_att = 2.0;  % 角速度增益

% 期望角速度（简化为0）
omega_desired = zeros(3, 1);
error_omega = omega_desired - omega;

% 期望力矩
tau_desired = kp_att * error_att + kd_att * error_omega;

% 限制力矩范围
for i = 1:3
    tau_desired(i) = max(-control_params.tau_max, min(control_params.tau_max, tau_desired(i)));
end

%% 输出控制量
u = [T_desired; tau_desired];

% 确保输出在合理范围内
u(1) = max(0, min(30, u(1)));  % 推力限制
for i = 2:4
    u(i) = max(-5, min(5, u(i)));  % 力矩限制
end

end
