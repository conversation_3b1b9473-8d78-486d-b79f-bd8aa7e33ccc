function u = ZeroConvergenceController(x, x_desired, control_params)
% 零收敛控制器 - 专门解决振荡问题，实现真正的零收敛
% 使用滑模控制+自适应阻尼+振荡抑制技术

persistent integral_pos integral_att controller_time
persistent prev_error_pos prev_error_vel oscillation_detector

% 初始化持久变量
if isempty(integral_pos)
    integral_pos = zeros(3, 1);
    integral_att = zeros(3, 1);
    controller_time = 0;
    prev_error_pos = zeros(3, 1);
    prev_error_vel = zeros(3, 1);
    oscillation_detector = 0;
end

% 更新控制器时间
dt = 0.01;
controller_time = controller_time + dt;

% 提取状态变量
pos = x(1:3);           % 位置 [x; y; z]
vel = x(4:6);           % 速度 [vx; vy; vz]
euler = x(7:9);         % 欧拉角 [phi; theta; psi]
omega = x(10:12);       % 角速度 [p; q; r]

% 期望状态
pos_d = x_desired(1:3);
vel_d = x_desired(4:6);

% 位置和速度误差
error_pos = pos - pos_d;
error_vel = vel - vel_d;
error_pos_norm = norm(error_pos);
error_vel_norm = norm(error_vel);

% 振荡检测算法
error_change_pos = error_pos - prev_error_pos;
error_change_vel = error_vel - prev_error_vel;

% 检测振荡模式
if controller_time > 5.0
    % 检测位置误差方向变化（振荡指标）
    pos_direction_change = sum(sign(error_pos) ~= sign(prev_error_pos));
    vel_direction_change = sum(sign(error_vel) ~= sign(prev_error_vel));
    
    if pos_direction_change >= 2 || vel_direction_change >= 2
        oscillation_detector = min(oscillation_detector + 1, 100);
    else
        oscillation_detector = max(oscillation_detector - 1, 0);
    end
end

% 判断是否处于振荡状态
is_oscillating = oscillation_detector > 20;

% 滑模面设计 - 用于消除振荡
lambda = 3.0;  % 滑模面参数
s = error_vel + lambda * error_pos;  % 滑模面
s_norm = norm(s);

% 时间分段控制策略
if controller_time <= 8.0
    % 0-8秒：快速收敛阶段
    k_pos_base = 4.0;
    k_vel_base = 3.5;
    k_int_base = 1.0;
    damping_factor = 1.0;
elseif controller_time <= 12.0
    % 8-12秒：精确收敛阶段
    k_pos_base = 5.0;
    k_vel_base = 4.5;
    k_int_base = 2.0;
    damping_factor = 1.5;
else
    % 12秒后：零误差保持阶段
    k_pos_base = 6.0;
    k_vel_base = 6.0;  % 强阻尼
    k_int_base = 3.0;  % 强积分
    damping_factor = 2.0;
end

% 振荡抑制增益调整
if is_oscillating
    % 检测到振荡时，大幅增加阻尼，减少比例增益
    k_pos = k_pos_base * 0.7;
    k_vel = k_vel_base * 2.0;  % 双倍阻尼
    k_int = k_int_base * 0.5;  % 减少积分避免积分饱和
    damping_factor = damping_factor * 2.0;
else
    % 正常状态下的增益
    if error_pos_norm > 0.5
        k_pos = k_pos_base * 1.2;
        k_vel = k_vel_base * 1.1;
        k_int = k_int_base * 0.8;
    elseif error_pos_norm > 0.1
        k_pos = k_pos_base * 1.1;
        k_vel = k_vel_base * 1.3;
        k_int = k_int_base * 1.2;
    else
        % 小误差时使用最强控制
        k_pos = k_pos_base * 1.5;
        k_vel = k_vel_base * 1.8;
        k_int = k_int_base * 2.0;
    end
end

% 滑模控制项 - 用于消除振荡和实现有限时间收敛
if s_norm > 0.01
    sliding_control = -2.0 * s / s_norm;  % 滑模控制律
else
    sliding_control = -200.0 * s;  % 线性区域
end

% 自适应积分控制
if controller_time > 10.0
    % 10秒后强化积分以消除稳态误差
    if error_pos_norm < 0.5 && ~is_oscillating
        integral_pos = integral_pos + error_pos * dt;
    else
        integral_pos = integral_pos * 0.99;
    end
else
    if error_pos_norm < 0.3
        integral_pos = integral_pos + error_pos * dt;
    else
        integral_pos = integral_pos * 0.98;
    end
end

% 动态积分限制
if is_oscillating
    max_integral = 0.1;  % 振荡时严格限制积分
elseif controller_time > 12.0
    max_integral = 1.0;  % 12秒后放宽积分限制
else
    max_integral = 0.5;
end

integral_pos = max(-max_integral, min(max_integral, integral_pos));

% 超强阻尼项 - 专门用于抑制振荡
super_damping = -damping_factor * 0.5 * error_change_vel / dt;

% 位置控制律 - 组合PID + 滑模 + 超强阻尼
acc_cmd = -k_pos * error_pos - k_vel * error_vel - k_int * integral_pos + sliding_control + super_damping;

% 推力计算
phi = euler(1); theta = euler(2);
cos_phi = cos(phi); cos_theta = cos(theta);

% 避免除零
if abs(cos_phi * cos_theta) < 0.1
    cos_phi = sign(cos_phi) * 0.1;
    cos_theta = sign(cos_theta) * 0.1;
end

T = control_params.mass * (acc_cmd(3) + control_params.gravity) / (cos_phi * cos_theta);

% 推力限幅
T = max(control_params.T_min, min(control_params.T_max, T));

% 期望姿态计算
g = control_params.gravity;
phi_d = (acc_cmd(1) * sin(euler(3)) - acc_cmd(2) * cos(euler(3))) / g;
theta_d = (acc_cmd(1) * cos(euler(3)) + acc_cmd(2) * sin(euler(3))) / g;
psi_d = 0;

% 姿态角限制
max_angle = pi/6;  % 30度限制，保持保守
phi_d = max(-max_angle, min(max_angle, phi_d));
theta_d = max(-max_angle, min(max_angle, theta_d));

% 姿态误差
euler_d = [phi_d; theta_d; psi_d];
error_att = euler - euler_d;
error_att_norm = norm(error_att);

% 姿态控制增益
if is_oscillating
    k_att = control_params.k_att * 0.8;
    k_omega = control_params.k_omega * 2.0;  % 强阻尼
    k_int_att = 0.2;
else
    if controller_time > 12.0
        k_att = control_params.k_att * 1.5;
        k_omega = control_params.k_omega * 1.8;
        k_int_att = 0.8;
    else
        k_att = control_params.k_att * 1.2;
        k_omega = control_params.k_omega * 1.4;
        k_int_att = 0.5;
    end
end

% 姿态积分项
if controller_time > 10.0 && error_att_norm < 0.2 && ~is_oscillating
    integral_att = integral_att + error_att * dt;
else
    integral_att = integral_att * 0.99;
end

% 姿态积分限制
if is_oscillating
    max_integral_att = 0.05;
else
    max_integral_att = 0.3;
end

integral_att = max(-max_integral_att, min(max_integral_att, integral_att));

% 姿态控制律
omega_d = zeros(3, 1);
error_omega = omega - omega_d;

tau = -k_att * error_att - k_omega * error_omega - k_int_att * integral_att;

% 力矩限幅
tau = max(-control_params.tau_max, min(control_params.tau_max, tau));

% 更新历史误差
prev_error_pos = error_pos;
prev_error_vel = error_vel;

% 输出控制量
u = [T; tau];

% 调试信息
persistent debug_counter
if isempty(debug_counter)
    debug_counter = 0;
end
debug_counter = debug_counter + 1;

if mod(debug_counter, 500) == 0  % 每5秒输出一次
    oscillation_status = '';
    if is_oscillating
        oscillation_status = '振荡抑制中';
    else
        oscillation_status = '正常';
    end
    fprintf('零收敛控制器 [%.1fs] - 位置误差: %.4f m, 速度误差: %.4f m/s, 状态: %s\n', ...
            controller_time, error_pos_norm, error_vel_norm, oscillation_status);
end

end
