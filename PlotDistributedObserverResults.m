function PlotDistributedObserverResults(t_span, x_history, x_hat_history, x_desired, ...
                                        estimation_error, u_history, tracking_error_pos, tracking_error_vel, pos_errors, vel_errors)
% 分布式观测器结果可视化函数 - 中文版本
% 专门针对振荡问题分析的可视化

N_agents = size(x_history, 2);

% 设置中文字体
try
    set(0, 'DefaultAxesFontName', 'SimHei');
    set(0, 'DefaultTextFontName', 'SimHei');
catch
    % 如果SimHei不可用，使用默认字体
    warning('无法设置中文字体，使用默认字体');
end

% 高对比度颜色设置
colors = [
    1.0, 0.0, 0.0;  % 红色 - UAV1
    0.0, 0.8, 0.0;  % 绿色 - UAV2  
    0.0, 0.0, 1.0;  % 蓝色 - UAV3
    1.0, 0.0, 1.0   % 洋红 - UAV4
];

line_styles = {'-', '--', '-.', ':'};
line_width = 2.5;

%% 图1: 位置跟踪误差图 (重点关注振荡问题)
figure('Name', '分布式观测器 - 位置跟踪误差', 'Position', [100, 100, 1200, 800]);

% 主图 - 完整时间范围
subplot(2, 2, [1, 2]);
hold on; grid on;
for i = 1:N_agents
    plot(t_span, pos_errors(i, :), 'Color', colors(i, :), 'LineStyle', line_styles{i}, ...
         'LineWidth', line_width, 'DisplayName', sprintf('无人机%d', i));
end
xlabel('时间 (s)', 'FontSize', 12);
ylabel('位置跟踪误差 (m)', 'FontSize', 12);
title('位置跟踪误差 - 分布式观测器系统', 'FontSize', 14, 'FontWeight', 'bold');
legend('Location', 'northeast', 'FontSize', 10);
xlim([0, max(t_span)]);
ylim([0, max(pos_errors(:)) * 1.1]);

% 放大图 - 25-35秒关键时段
subplot(2, 2, 3);
hold on; grid on;
zoom_start = 25; zoom_end = 35;
zoom_idx_start = find(t_span >= zoom_start, 1);
zoom_idx_end = find(t_span >= zoom_end, 1);

if ~isempty(zoom_idx_start) && ~isempty(zoom_idx_end)
    for i = 1:N_agents
        plot(t_span(zoom_idx_start:zoom_idx_end), pos_errors(i, zoom_idx_start:zoom_idx_end), ...
             'Color', colors(i, :), 'LineStyle', line_styles{i}, 'LineWidth', line_width, ...
             'DisplayName', sprintf('无人机%d', i));
    end
    xlabel('时间 (s)', 'FontSize', 12);
    ylabel('位置跟踪误差 (m)', 'FontSize', 12);
    title(sprintf('位置跟踪误差放大图 (%d-%d秒)', zoom_start, zoom_end), 'FontSize', 12, 'FontWeight', 'bold');
    legend('Location', 'northeast', 'FontSize', 9);
    xlim([zoom_start, zoom_end]);
end

% 振荡分析图 - 15秒后的表现
subplot(2, 2, 4);
hold on; grid on;
steady_start = 15;
steady_idx = find(t_span >= steady_start, 1);

if ~isempty(steady_idx)
    for i = 1:N_agents
        plot(t_span(steady_idx:end), pos_errors(i, steady_idx:end), ...
             'Color', colors(i, :), 'LineStyle', line_styles{i}, 'LineWidth', line_width, ...
             'DisplayName', sprintf('无人机%d', i));
    end
    xlabel('时间 (s)', 'FontSize', 12);
    ylabel('位置跟踪误差 (m)', 'FontSize', 12);
    title('15秒后稳态性能分析', 'FontSize', 12, 'FontWeight', 'bold');
    legend('Location', 'northeast', 'FontSize', 9);
    xlim([steady_start, max(t_span)]);
end

%% 图2: 速度跟踪误差图
figure('Name', '分布式观测器 - 速度跟踪误差', 'Position', [150, 150, 1200, 800]);

% 主图 - 完整时间范围
subplot(2, 2, [1, 2]);
hold on; grid on;
for i = 1:N_agents
    plot(t_span, vel_errors(i, :), 'Color', colors(i, :), 'LineStyle', line_styles{i}, ...
         'LineWidth', line_width, 'DisplayName', sprintf('无人机%d', i));
end
xlabel('时间 (s)', 'FontSize', 12);
ylabel('速度跟踪误差 (m/s)', 'FontSize', 12);
title('速度跟踪误差 - 分布式观测器系统', 'FontSize', 14, 'FontWeight', 'bold');
legend('Location', 'northeast', 'FontSize', 10);
xlim([0, max(t_span)]);
ylim([0, max(vel_errors(:)) * 1.1]);

% 放大图 - 25-35秒关键时段
subplot(2, 2, 3);
hold on; grid on;
if ~isempty(zoom_idx_start) && ~isempty(zoom_idx_end)
    for i = 1:N_agents
        plot(t_span(zoom_idx_start:zoom_idx_end), vel_errors(i, zoom_idx_start:zoom_idx_end), ...
             'Color', colors(i, :), 'LineStyle', line_styles{i}, 'LineWidth', line_width, ...
             'DisplayName', sprintf('无人机%d', i));
    end
    xlabel('时间 (s)', 'FontSize', 12);
    ylabel('速度跟踪误差 (m/s)', 'FontSize', 12);
    title(sprintf('速度跟踪误差放大图 (%d-%d秒)', zoom_start, zoom_end), 'FontSize', 12, 'FontWeight', 'bold');
    legend('Location', 'northeast', 'FontSize', 9);
    xlim([zoom_start, zoom_end]);
end

% 振荡分析图 - 15秒后的表现
subplot(2, 2, 4);
hold on; grid on;
if ~isempty(steady_idx)
    for i = 1:N_agents
        plot(t_span(steady_idx:end), vel_errors(i, steady_idx:end), ...
             'Color', colors(i, :), 'LineStyle', line_styles{i}, 'LineWidth', line_width, ...
             'DisplayName', sprintf('无人机%d', i));
    end
    xlabel('时间 (s)', 'FontSize', 12);
    ylabel('速度跟踪误差 (m/s)', 'FontSize', 12);
    title('15秒后速度稳态性能分析', 'FontSize', 12, 'FontWeight', 'bold');
    legend('Location', 'northeast', 'FontSize', 9);
    xlim([steady_start, max(t_span)]);
end

%% 图3: 状态估计误差 (分布式观测器性能)
figure('Name', '分布式观测器 - 状态估计误差', 'Position', [200, 200, 1200, 600]);

% 位置估计误差
subplot(1, 2, 1);
hold on; grid on;
for i = 1:N_agents
    pos_est_error = squeeze(sqrt(sum(estimation_error(1:3, i, :).^2, 1)));
    plot(t_span, pos_est_error, 'Color', colors(i, :), 'LineStyle', line_styles{i}, ...
         'LineWidth', line_width, 'DisplayName', sprintf('无人机%d', i));
end
xlabel('时间 (s)', 'FontSize', 12);
ylabel('位置估计误差 (m)', 'FontSize', 12);
title('分布式观测器 - 位置估计误差', 'FontSize', 14, 'FontWeight', 'bold');
legend('Location', 'northeast', 'FontSize', 10);
xlim([0, max(t_span)]);

% 速度估计误差
subplot(1, 2, 2);
hold on; grid on;
for i = 1:N_agents
    vel_est_error = squeeze(sqrt(sum(estimation_error(4:6, i, :).^2, 1)));
    plot(t_span, vel_est_error, 'Color', colors(i, :), 'LineStyle', line_styles{i}, ...
         'LineWidth', line_width, 'DisplayName', sprintf('无人机%d', i));
end
xlabel('时间 (s)', 'FontSize', 12);
ylabel('速度估计误差 (m/s)', 'FontSize', 12);
title('分布式观测器 - 速度估计误差', 'FontSize', 14, 'FontWeight', 'bold');
legend('Location', 'northeast', 'FontSize', 10);
xlim([0, max(t_span)]);

%% 图4: 3D轨迹图 + 编队形成过程
figure('Name', '分布式观测器 - 3D轨迹与编队', 'Position', [250, 250, 1200, 800]);

% 3D轨迹图
subplot(2, 2, [1, 3]);
hold on; grid on;

% 绘制真实轨迹
for i = 1:N_agents
    plot3(squeeze(x_history(1, i, :)), squeeze(x_history(2, i, :)), squeeze(x_history(3, i, :)), ...
          'Color', colors(i, :), 'LineStyle', '-', 'LineWidth', 2, ...
          'DisplayName', sprintf('无人机%d轨迹', i));
end

% 绘制期望位置
for i = 1:N_agents
    plot3(x_desired(1, i), x_desired(2, i), x_desired(3, i), 'o', ...
          'Color', colors(i, :), 'MarkerSize', 12, 'MarkerFaceColor', colors(i, :), ...
          'DisplayName', sprintf('无人机%d目标', i));
end

% 绘制初始位置
for i = 1:N_agents
    plot3(x_history(1, i, 1), x_history(2, i, 1), x_history(3, i, 1), 's', ...
          'Color', colors(i, :), 'MarkerSize', 10, 'MarkerFaceColor', 'white', ...
          'MarkerEdgeColor', colors(i, :), 'LineWidth', 2);
end

xlabel('X (m)', 'FontSize', 12);
ylabel('Y (m)', 'FontSize', 12);
zlabel('Z (m)', 'FontSize', 12);
title('分布式观测器编队控制 - 3D飞行轨迹', 'FontSize', 14, 'FontWeight', 'bold');
legend('Location', 'best', 'FontSize', 9);
view(45, 30);
axis equal;

% 编队形成过程 - 关键时刻快照
subplot(2, 2, 2);
hold on; grid on;
time_snapshots = [0, 10, 20, 30, 40];
snapshot_colors = [0.8, 0.8, 0.8; 0.6, 0.6, 0.6; 0.4, 0.4, 0.4; 0.2, 0.2, 0.2; 0.0, 0.0, 0.0];

for t_idx = 1:length(time_snapshots)
    t_snap = time_snapshots(t_idx);
    k_snap = find(t_span >= t_snap, 1);
    if ~isempty(k_snap)
        for i = 1:N_agents
            plot(x_history(1, i, k_snap), x_history(2, i, k_snap), 'o', ...
                 'Color', snapshot_colors(t_idx, :), 'MarkerSize', 8, ...
                 'MarkerFaceColor', snapshot_colors(t_idx, :));
            if i == 1  % 只为第一个无人机添加时间标签
                text(x_history(1, i, k_snap), x_history(2, i, k_snap) + 0.3, ...
                     sprintf('%.0fs', t_snap), 'FontSize', 8, 'HorizontalAlignment', 'center');
            end
        end
        % 连接编队成员
        if t_idx == length(time_snapshots)  % 最终编队用实线
            formation_x = [x_history(1, 1, k_snap), x_history(1, 2, k_snap), ...
                          x_history(1, 3, k_snap), x_history(1, 4, k_snap), x_history(1, 1, k_snap)];
            formation_y = [x_history(2, 1, k_snap), x_history(2, 2, k_snap), ...
                          x_history(2, 3, k_snap), x_history(2, 4, k_snap), x_history(2, 1, k_snap)];
            plot(formation_x, formation_y, '-', 'Color', snapshot_colors(t_idx, :), 'LineWidth', 2);
        end
    end
end

% 绘制期望编队形状
desired_x = [x_desired(1, 1), x_desired(1, 2), x_desired(1, 3), x_desired(1, 4), x_desired(1, 1)];
desired_y = [x_desired(2, 1), x_desired(2, 2), x_desired(2, 3), x_desired(2, 4), x_desired(2, 1)];
plot(desired_x, desired_y, 'r--', 'LineWidth', 3, 'DisplayName', '期望编队');

xlabel('X (m)', 'FontSize', 12);
ylabel('Y (m)', 'FontSize', 12);
title('编队形成过程 (俯视图)', 'FontSize', 12, 'FontWeight', 'bold');
legend('Location', 'best', 'FontSize', 9);
axis equal;

% 控制输入分析
subplot(2, 2, 4);
hold on; grid on;
for i = 1:N_agents
    thrust_history = squeeze(u_history(1, i, :));
    plot(t_span, thrust_history, 'Color', colors(i, :), 'LineStyle', line_styles{i}, ...
         'LineWidth', 1.5, 'DisplayName', sprintf('无人机%d', i));
end
xlabel('时间 (s)', 'FontSize', 12);
ylabel('推力 (N)', 'FontSize', 12);
title('控制输入 - 推力', 'FontSize', 12, 'FontWeight', 'bold');
legend('Location', 'northeast', 'FontSize', 9);
xlim([0, max(t_span)]);

fprintf('📊 分布式观测器可视化完成！\n');
fprintf('✅ 生成了4个主要图表\n');
fprintf('✅ 位置跟踪误差图 (包含放大图和稳态分析)\n');
fprintf('✅ 速度跟踪误差图 (包含放大图和稳态分析)\n');
fprintf('✅ 分布式观测器估计误差分析\n');
fprintf('✅ 3D飞行轨迹与编队形成过程\n');

end
