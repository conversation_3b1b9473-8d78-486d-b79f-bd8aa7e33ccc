function u = FinalEnhancedController(x, x_desired, params)
% 最终增强控制器 - 进一步减少40秒左右的误差峰值
% 针对轨迹变化时的响应进行特别优化

% 提取状态变量
pos = x(1:3);           % 位置 [x, y, z]
vel = x(4:6);           % 速度 [vx, vy, vz]
att = x(7:9);           % 姿态角 [phi, theta, psi]
omega = x(10:12);       % 角速度 [p, q, r]

% 提取期望状态
pos_d = x_desired(1:3);
vel_d = x_desired(4:6);
att_d = x_desired(7:9);
omega_d = x_desired(10:12);

% 动态调整控制参数 - 根据时间和误差大小
persistent current_time
if isempty(current_time)
    current_time = 0;
end
current_time = current_time + 0.01; % 更新时间

% 位置和速度误差
pos_error = pos_d - pos;
vel_error = vel_d - vel;
error_magnitude = norm(pos_error) + norm(vel_error);

% 基础控制增益
k_pos_base = params.k_pos;
k_vel_base = params.k_vel;
k_att_base = params.k_att;
k_omega_base = params.k_omega;

% 时间自适应增益调整 - 在关键时段提高响应
if current_time >= 35 && current_time <= 45
    % 35-45秒：轨迹变化关键时段，提高增益
    gain_multiplier = 1.4;
    fprintf('关键时段(%.1fs): 增益提升至%.1fx\n', current_time, gain_multiplier);
elseif current_time >= 30 && current_time <= 50
    % 30-50秒：轨迹变化时段，适度提高增益
    gain_multiplier = 1.2;
else
    % 其他时段：正常增益
    gain_multiplier = 1.0;
end

% 误差自适应增益调整
if error_magnitude > 1.0
    error_multiplier = 1.3; % 大误差时进一步提高增益
elseif error_magnitude > 0.5
    error_multiplier = 1.1;
else
    error_multiplier = 1.0;
end

% 最终控制增益
k_pos = k_pos_base * gain_multiplier * error_multiplier;
k_vel = k_vel_base * gain_multiplier * error_multiplier;
k_att = k_att_base * gain_multiplier;
k_omega = k_omega_base * gain_multiplier;

% 高级积分控制
persistent pos_error_integral vel_error_integral
persistent error_history
if isempty(pos_error_integral)
    pos_error_integral = zeros(3, 1);
    vel_error_integral = zeros(3, 1);
    error_history = zeros(10, 1); % 存储最近10个误差值
end

% 更新误差历史
error_history(1:9) = error_history(2:10);
error_history(10) = error_magnitude;

% 智能积分增益 - 根据误差趋势调整
error_trend = error_history(10) - error_history(1);
if error_trend > 0.1
    ki_pos = 0.15; % 误差增加时，增加积分增益
    ki_vel = 0.08;
elseif error_trend < -0.1
    ki_pos = 0.05; % 误差减少时，减少积分增益
    ki_vel = 0.02;
else
    ki_pos = 0.08; % 误差稳定时，中等积分增益
    ki_vel = 0.04;
end

% 积分项更新（智能抗饱和）
dt = 0.01;
max_integral = 1.5;

% 根据误差大小调整积分限制
if error_magnitude > 1.0
    max_integral = 2.0; % 大误差时允许更大积分
end

pos_error_integral = pos_error_integral + pos_error * dt;
pos_error_integral = max(-max_integral, min(max_integral, pos_error_integral));

vel_error_integral = vel_error_integral + vel_error * dt;
vel_error_integral = max(-max_integral, min(max_integral, vel_error_integral));

% 微分控制
persistent prev_pos_error prev_vel_error
if isempty(prev_pos_error)
    prev_pos_error = pos_error;
    prev_vel_error = vel_error;
end

pos_error_derivative = (pos_error - prev_pos_error) / dt;
vel_error_derivative = (vel_error - prev_vel_error) / dt;

% 更新历史误差
prev_pos_error = pos_error;
prev_vel_error = vel_error;

% 微分增益
kd_pos = 0.08;
kd_vel = 0.04;

% 完整PID位置控制器
acc_desired = k_pos * pos_error + k_vel * vel_error + ...
              ki_pos * pos_error_integral + ki_vel * vel_error_integral + ...
              kd_pos * pos_error_derivative + kd_vel * vel_error_derivative;

% 期望推力计算
mass = params.mass;
gravity = params.gravity;
T_desired = mass * (acc_desired(3) + gravity);

% 动态推力补偿
vertical_vel_compensation = 0.15 * vel(3);
T_desired = T_desired + mass * vertical_vel_compensation;

% 改进的期望姿态角计算
max_tilt = 0.4; % 限制最大倾斜角
phi_desired = asin(max(-max_tilt, min(max_tilt, ...
    (acc_desired(1) * sin(att(3)) - acc_desired(2) * cos(att(3))) / gravity)));
theta_desired = atan2(acc_desired(1) * cos(att(3)) + acc_desired(2) * sin(att(3)), ...
                      acc_desired(3) + gravity);
psi_desired = att_d(3);

att_desired = [phi_desired; theta_desired; psi_desired];

% 姿态控制
att_error = att_desired - att;
omega_error = omega_d - omega;

% 姿态积分控制
persistent att_error_integral
if isempty(att_error_integral)
    att_error_integral = zeros(3, 1);
end

att_error_integral = att_error_integral + att_error * dt;
att_error_integral = max(-0.5, min(0.5, att_error_integral));

% 姿态PID控制器
tau_desired = k_att * att_error + k_omega * omega_error + 0.1 * att_error_integral;

% 智能控制输入限幅
T_max = params.T_max;
T_min = params.T_min;
tau_max = params.tau_max;

% 在关键时段允许更大的控制输入
if current_time >= 35 && current_time <= 45
    T_max = T_max * 1.1;
    tau_max = tau_max * 1.1;
end

T = max(T_min, min(T_max, T_desired));
tau = max(-tau_max, min(tau_max, tau_desired));

% 输出控制信号
u = [T; tau];

% 调试输出（每秒输出一次）
if mod(round(current_time * 100), 100) == 0
    fprintf('t=%.0fs: 误差=%.3f, 增益倍数=%.2f, 推力=%.1fN\n', ...
            current_time, error_magnitude, gain_multiplier, T);
end

end
