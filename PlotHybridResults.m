function PlotHybridResults(t_span, x_history, x_hat_history, x_desired, x_virtual, ...
                         estimation_error, u_real_history, u_estimated_history, ...
                         pos_errors_real, vel_errors_real, pos_errors_estimated, vel_errors_estimated, ...
                         formation_offset, neighbors)
% 混合架构系统结果可视化函数
% 展示真实状态控制 vs 估计状态控制的性能对比
% 以及完整的分布式观测器功能展示

N_agents = size(x_history, 2);

% 设置中文字体
try
    set(0, 'DefaultAxesFontName', 'SimHei');
    set(0, 'DefaultTextFontName', 'SimHei');
catch
    warning('无法设置中文字体，使用默认字体');
end

% 高对比度颜色设置
colors = [
    0.8, 0.0, 0.8;  % 洋红 - 领导者
    1.0, 0.0, 0.0;  % 红色 - 跟随者1
    0.0, 0.8, 0.0;  % 绿色 - 跟随者2  
    0.0, 0.0, 1.0;  % 蓝色 - 跟随者3
    1.0, 0.5, 0.0   % 橙色 - 跟随者4
];

line_styles = {'-', '--', '-.', ':', '-'};
line_width = 2.5;

%% 图1: 性能对比 - 位置跟踪误差
figure('Name', '混合架构 - 位置跟踪误差性能对比', 'Position', [100, 100, 1400, 800]);

% 真实状态控制性能
subplot(2, 3, [1, 2]);
hold on; grid on;
for i = 1:N_agents
    if i == 1
        label_name = '领导者';
    else
        label_name = sprintf('跟随者%d', i-1);
    end
    plot(t_span, pos_errors_real(i, :), 'Color', colors(i, :), 'LineStyle', line_styles{i}, ...
         'LineWidth', line_width, 'DisplayName', label_name);
end
xlabel('时间 (s)', 'FontSize', 12);
ylabel('位置跟踪误差 (m)', 'FontSize', 12);
title('主控制系统 - 基于真实状态 (保证性能)', 'FontSize', 14, 'FontWeight', 'bold');
legend('Location', 'northeast', 'FontSize', 10);
xlim([0, max(t_span)]);
ylim([0, max(pos_errors_real(:)) * 1.1]);

% 添加性能指标
max_pos_real = max(pos_errors_real(:));
steady_start_idx = find(t_span >= 15, 1);
if ~isempty(steady_start_idx)
    steady_max_pos_real = max(pos_errors_real(:, steady_start_idx:end));
    text(0.02, 0.95, sprintf('最大误差: %.2f m\n15秒后最大: %.2f m', max_pos_real, steady_max_pos_real), ...
         'Units', 'normalized', 'FontSize', 10, 'BackgroundColor', 'white', ...
         'VerticalAlignment', 'top');
end

% 估计状态控制性能
subplot(2, 3, [4, 5]);
hold on; grid on;
for i = 1:N_agents
    if i == 1
        label_name = '领导者';
    else
        label_name = sprintf('跟随者%d', i-1);
    end
    plot(t_span, pos_errors_estimated(i, :), 'Color', colors(i, :), 'LineStyle', line_styles{i}, ...
         'LineWidth', line_width, 'DisplayName', label_name);
end
xlabel('时间 (s)', 'FontSize', 12);
ylabel('位置跟踪误差 (m)', 'FontSize', 12);
title('对比系统 - 基于估计状态 (展示观测器影响)', 'FontSize', 14, 'FontWeight', 'bold');
legend('Location', 'northeast', 'FontSize', 10);
xlim([0, max(t_span)]);
ylim([0, max(pos_errors_estimated(:)) * 1.1]);

% 添加性能指标
max_pos_est = max(pos_errors_estimated(:));
if ~isempty(steady_start_idx)
    steady_max_pos_est = max(pos_errors_estimated(:, steady_start_idx:end));
    text(0.02, 0.95, sprintf('最大误差: %.2f m\n15秒后最大: %.2f m', max_pos_est, steady_max_pos_est), ...
         'Units', 'normalized', 'FontSize', 10, 'BackgroundColor', 'white', ...
         'VerticalAlignment', 'top');
end

% 性能对比柱状图
subplot(2, 3, [3, 6]);
hold on; grid on;

% 计算关键性能指标
metrics_real = [max(pos_errors_real(:)), mean(pos_errors_real(:))];
metrics_est = [max(pos_errors_estimated(:)), mean(pos_errors_estimated(:))];

if ~isempty(steady_start_idx)
    steady_real = max(pos_errors_real(:, steady_start_idx:end));
    steady_est = max(pos_errors_estimated(:, steady_start_idx:end));
    metrics_real = [metrics_real, steady_real];
    metrics_est = [metrics_est, steady_est];
end

x_pos = 1:length(metrics_real);
width = 0.35;

bar(x_pos - width/2, metrics_real, width, 'FaceColor', [0.2, 0.6, 0.8], ...
    'EdgeColor', 'k', 'LineWidth', 1.5, 'DisplayName', '真实状态控制');
bar(x_pos + width/2, metrics_est, width, 'FaceColor', [0.8, 0.4, 0.2], ...
    'EdgeColor', 'k', 'LineWidth', 1.5, 'DisplayName', '估计状态控制');

set(gca, 'XTick', x_pos);
if length(metrics_real) == 3
    set(gca, 'XTickLabel', {'最大误差', '平均误差', '15s后最大'});
else
    set(gca, 'XTickLabel', {'最大误差', '平均误差'});
end
ylabel('位置误差 (m)', 'FontSize', 12);
title('位置跟踪性能对比', 'FontSize', 12, 'FontWeight', 'bold');
legend('Location', 'northeast', 'FontSize', 10);

% 添加数值标签
for i = 1:length(metrics_real)
    text(i - width/2, metrics_real(i) + max([metrics_real, metrics_est])*0.02, ...
         sprintf('%.2f', metrics_real(i)), 'HorizontalAlignment', 'center', 'FontSize', 9);
    text(i + width/2, metrics_est(i) + max([metrics_real, metrics_est])*0.02, ...
         sprintf('%.2f', metrics_est(i)), 'HorizontalAlignment', 'center', 'FontSize', 9);
end

%% 图2: 分布式观测器状态估计误差
figure('Name', '混合架构 - 分布式观测器状态估计分析', 'Position', [150, 150, 1200, 800]);

% 位置估计误差
subplot(2, 2, 1);
hold on; grid on;
for i = 1:N_agents
    pos_est_error = squeeze(sqrt(sum(estimation_error(1:3, i, :).^2, 1)));
    if i == 1
        label_name = '领导者 (精确状态)';
        line_style_obs = ':';
    else
        label_name = sprintf('跟随者%d', i-1);
        line_style_obs = line_styles{i};
    end
    plot(t_span, pos_est_error, 'Color', colors(i, :), 'LineStyle', line_style_obs, ...
         'LineWidth', line_width, 'DisplayName', label_name);
end
xlabel('时间 (s)', 'FontSize', 12);
ylabel('位置估计误差 (m)', 'FontSize', 12);
title('分布式观测器 - 位置估计误差', 'FontSize', 14, 'FontWeight', 'bold');
legend('Location', 'northeast', 'FontSize', 10);
xlim([0, max(t_span)]);

% 速度估计误差
subplot(2, 2, 2);
hold on; grid on;
for i = 1:N_agents
    vel_est_error = squeeze(sqrt(sum(estimation_error(4:6, i, :).^2, 1)));
    if i == 1
        label_name = '领导者 (精确状态)';
        line_style_obs = ':';
    else
        label_name = sprintf('跟随者%d', i-1);
        line_style_obs = line_styles{i};
    end
    plot(t_span, vel_est_error, 'Color', colors(i, :), 'LineStyle', line_style_obs, ...
         'LineWidth', line_width, 'DisplayName', label_name);
end
xlabel('时间 (s)', 'FontSize', 12);
ylabel('速度估计误差 (m/s)', 'FontSize', 12);
title('分布式观测器 - 速度估计误差', 'FontSize', 14, 'FontWeight', 'bold');
legend('Location', 'northeast', 'FontSize', 10);
xlim([0, max(t_span)]);

% 通信拓扑可视化
subplot(2, 2, 3);
hold on; grid on;

% 绘制通信拓扑图
node_positions = [
    0, 0;       % 领导者
    -1, -1;     % 跟随者1
    1, -1;      % 跟随者2
    -1, 1;      % 跟随者3
    1, 1        % 跟随者4
];

% 绘制通信连接
for i = 1:N_agents
    for j = 1:N_agents
        if neighbors(i, j) == 1 && i ~= j
            plot([node_positions(i, 1), node_positions(j, 1)], ...
                 [node_positions(i, 2), node_positions(j, 2)], ...
                 'k-', 'LineWidth', 1.5, 'Color', [0.5, 0.5, 0.5]);
        end
    end
end

% 绘制无人机节点
for i = 1:N_agents
    if i == 1
        marker_style = 'o';
        marker_size = 15;
        node_label = '领导者';
    else
        marker_style = 's';
        marker_size = 12;
        node_label = sprintf('跟随者%d', i-1);
    end
    plot(node_positions(i, 1), node_positions(i, 2), marker_style, ...
         'Color', colors(i, :), 'MarkerSize', marker_size, ...
         'MarkerFaceColor', colors(i, :), 'MarkerEdgeColor', 'k', 'LineWidth', 2);
    
    % 添加节点标签
    text(node_positions(i, 1), node_positions(i, 2) - 0.3, node_label, ...
         'HorizontalAlignment', 'center', 'FontSize', 9, 'FontWeight', 'bold');
end

xlabel('相对位置 X', 'FontSize', 12);
ylabel('相对位置 Y', 'FontSize', 12);
title('分布式通信拓扑', 'FontSize', 12, 'FontWeight', 'bold');
axis equal;
xlim([-2, 2]);
ylim([-2, 2]);

% 观测器性能统计
subplot(2, 2, 4);
hold on; grid on;

% 计算每个无人机的平均估计误差
avg_errors = zeros(N_agents, 1);
max_errors = zeros(N_agents, 1);
for i = 1:N_agents
    total_est_error = squeeze(sqrt(sum(estimation_error(:, i, :).^2, 1)));
    avg_errors(i) = mean(total_est_error);
    max_errors(i) = max(total_est_error);
end

x_agents_pos = 1:N_agents;
width = 0.35;

bar(x_agents_pos - width/2, avg_errors, width, 'FaceColor', [0.3, 0.7, 0.3], ...
    'EdgeColor', 'k', 'LineWidth', 1.5, 'DisplayName', '平均估计误差');
bar(x_agents_pos + width/2, max_errors, width, 'FaceColor', [0.7, 0.3, 0.3], ...
    'EdgeColor', 'k', 'LineWidth', 1.5, 'DisplayName', '最大估计误差');

agent_labels = cell(N_agents, 1);
for i = 1:N_agents
    if i == 1
        agent_labels{i} = '领导者';
    else
        agent_labels{i} = sprintf('跟随者%d', i-1);
    end
end

set(gca, 'XTick', x_agents_pos);
set(gca, 'XTickLabel', agent_labels);
ylabel('状态估计误差 (m)', 'FontSize', 12);
title('分布式观测器性能统计', 'FontSize', 12, 'FontWeight', 'bold');
legend('Location', 'northeast', 'FontSize', 10);

% 添加数值标签
for i = 1:N_agents
    text(i - width/2, avg_errors(i) + max(max_errors)*0.02, ...
         sprintf('%.2f', avg_errors(i)), 'HorizontalAlignment', 'center', 'FontSize', 9);
    text(i + width/2, max_errors(i) + max(max_errors)*0.02, ...
         sprintf('%.2f', max_errors(i)), 'HorizontalAlignment', 'center', 'FontSize', 9);
end

%% 图3: 3D轨迹对比
figure('Name', '混合架构 - 3D轨迹对比与编队展示', 'Position', [200, 200, 1400, 800]);

% 真实系统3D轨迹
subplot(2, 2, 1);
hold on; grid on;
for i = 1:N_agents
    if i == 1
        label_name = '领导者';
        line_width_3d = 3;
    else
        label_name = sprintf('跟随者%d', i-1);
        line_width_3d = 2;
    end
    plot3(squeeze(x_history(1, i, :)), squeeze(x_history(2, i, :)), squeeze(x_history(3, i, :)), ...
          'Color', colors(i, :), 'LineStyle', '-', 'LineWidth', line_width_3d, ...
          'DisplayName', label_name);
end
xlabel('X (m)', 'FontSize', 12);
ylabel('Y (m)', 'FontSize', 12);
zlabel('Z (m)', 'FontSize', 12);
title('真实系统 - 3D飞行轨迹', 'FontSize', 12, 'FontWeight', 'bold');
legend('Location', 'best', 'FontSize', 9);
view(45, 30);
axis equal;

% 虚拟系统3D轨迹 (基于估计状态控制)
subplot(2, 2, 2);
hold on; grid on;
for i = 1:N_agents
    if i == 1
        label_name = '领导者';
        line_width_3d = 3;
    else
        label_name = sprintf('跟随者%d', i-1);
        line_width_3d = 2;
    end
    plot3(squeeze(x_virtual(1, i, :)), squeeze(x_virtual(2, i, :)), squeeze(x_virtual(3, i, :)), ...
          'Color', colors(i, :), 'LineStyle', '--', 'LineWidth', line_width_3d, ...
          'DisplayName', label_name);
end
xlabel('X (m)', 'FontSize', 12);
ylabel('Y (m)', 'FontSize', 12);
zlabel('Z (m)', 'FontSize', 12);
title('虚拟系统 - 基于估计状态控制', 'FontSize', 12, 'FontWeight', 'bold');
legend('Location', 'best', 'FontSize', 9);
view(45, 30);
axis equal;

% 编队形成过程
subplot(2, 2, [3, 4]);
hold on; grid on;
time_snapshots = [0, 10, 20, 30, 40];
snapshot_colors = [0.8, 0.8, 0.8; 0.6, 0.6, 0.6; 0.4, 0.4, 0.4; 0.2, 0.2, 0.2; 0.0, 0.0, 0.0];

for t_idx = 1:length(time_snapshots)
    t_snap = time_snapshots(t_idx);
    k_snap = find(t_span >= t_snap, 1);
    if ~isempty(k_snap)
        % 绘制真实系统位置 (实线)
        for i = 1:N_agents
            if i == 1
                marker_style = 'o';
                marker_size = 12;
            else
                marker_style = 's';
                marker_size = 10;
            end
            plot(x_history(1, i, k_snap), x_history(2, i, k_snap), marker_style, ...
                 'Color', snapshot_colors(t_idx, :), 'MarkerSize', marker_size, ...
                 'MarkerFaceColor', snapshot_colors(t_idx, :), 'MarkerEdgeColor', 'k');
        end
        
        % 绘制虚拟系统位置 (虚线边框)
        for i = 1:N_agents
            if i == 1
                marker_style = 'o';
                marker_size = 12;
            else
                marker_style = 's';
                marker_size = 10;
            end
            plot(x_virtual(1, i, k_snap), x_virtual(2, i, k_snap), marker_style, ...
                 'Color', snapshot_colors(t_idx, :), 'MarkerSize', marker_size, ...
                 'MarkerFaceColor', 'none', 'MarkerEdgeColor', snapshot_colors(t_idx, :), ...
                 'LineWidth', 2, 'LineStyle', '--');
        end
        
        % 连接编队成员 (最终时刻)
        if t_idx == length(time_snapshots)
            % 真实系统连接
            leader_pos_real = [x_history(1, 1, k_snap), x_history(2, 1, k_snap)];
            for i = 2:N_agents
                follower_pos_real = [x_history(1, i, k_snap), x_history(2, i, k_snap)];
                plot([leader_pos_real(1), follower_pos_real(1)], [leader_pos_real(2), follower_pos_real(2)], ...
                     '-', 'Color', snapshot_colors(t_idx, :), 'LineWidth', 1.5);
            end
            
            % 虚拟系统连接
            leader_pos_virtual = [x_virtual(1, 1, k_snap), x_virtual(2, 1, k_snap)];
            for i = 2:N_agents
                follower_pos_virtual = [x_virtual(1, i, k_snap), x_virtual(2, i, k_snap)];
                plot([leader_pos_virtual(1), follower_pos_virtual(1)], [leader_pos_virtual(2), follower_pos_virtual(2)], ...
                     '--', 'Color', snapshot_colors(t_idx, :), 'LineWidth', 1.5);
            end
        end
    end
end

xlabel('X (m)', 'FontSize', 12);
ylabel('Y (m)', 'FontSize', 12);
title('编队形成对比 (实线=真实系统, 虚线=估计状态系统)', 'FontSize', 12, 'FontWeight', 'bold');
axis equal;

% 添加图例说明
text(0.02, 0.98, '● 实心标记: 真实状态控制\n○ 空心标记: 估计状态控制', ...
     'Units', 'normalized', 'FontSize', 10, 'BackgroundColor', 'white', ...
     'VerticalAlignment', 'top');

fprintf('📊 混合架构系统可视化完成！\n');
fprintf('✅ 生成了3个主要图表组\n');
fprintf('✅ 位置跟踪误差性能对比\n');
fprintf('✅ 分布式观测器状态估计分析\n');
fprintf('✅ 3D轨迹对比与编队展示\n');
fprintf('✅ 通信拓扑可视化\n');
fprintf('✅ 完整性能统计对比\n');

end
