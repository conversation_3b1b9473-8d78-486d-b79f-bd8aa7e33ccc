function u = FinalConvergenceController(x, x_desired, control_params)
% 最终收敛控制器 - 实现10-15秒内误差收敛到接近零
% 基于稳定控制器，使用渐进式增益提升和强化积分控制

persistent integral_pos integral_att controller_time

% 初始化持久变量
if isempty(integral_pos)
    integral_pos = zeros(3, 1);
    integral_att = zeros(3, 1);
    controller_time = 0;
end

% 更新控制器时间
dt = 0.01;
controller_time = controller_time + dt;

% 提取状态变量
pos = x(1:3);           % 位置 [x; y; z]
vel = x(4:6);           % 速度 [vx; vy; vz]
euler = x(7:9);         % 欧拉角 [phi; theta; psi]
omega = x(10:12);       % 角速度 [p; q; r]

% 期望状态
pos_d = x_desired(1:3);
vel_d = x_desired(4:6);

% 位置和速度误差
error_pos = pos - pos_d;
error_vel = vel - vel_d;
error_pos_norm = norm(error_pos);

% 时间渐进式增益调度 - 10-15秒内逐步增强控制
if controller_time <= 8.0
    % 0-8秒：稳定收敛阶段
    gain_multiplier = 1.0;
    integral_multiplier = 1.0;
elseif controller_time <= 12.0
    % 8-12秒：增强收敛阶段
    progress = (controller_time - 8.0) / 4.0;  % 0到1的进度
    gain_multiplier = 1.0 + 0.3 * progress;   % 从1.0增加到1.3
    integral_multiplier = 1.0 + 0.5 * progress; % 从1.0增加到1.5
elseif controller_time <= 15.0
    % 12-15秒：精确收敛阶段
    progress = (controller_time - 12.0) / 3.0;
    gain_multiplier = 1.3 + 0.4 * progress;   % 从1.3增加到1.7
    integral_multiplier = 1.5 + 0.8 * progress; % 从1.5增加到2.3
else
    % 15秒后：高精度保持阶段
    gain_multiplier = 1.8;
    integral_multiplier = 2.5;
end

% 误差自适应增益调整
if error_pos_norm > 1.0
    % 大误差：快速收敛
    k_pos = control_params.k_pos * 1.2 * gain_multiplier;
    k_vel = control_params.k_vel * 1.1 * gain_multiplier;
    k_int = 0.8 * integral_multiplier;
elseif error_pos_norm > 0.3
    % 中等误差：标准控制
    k_pos = control_params.k_pos * 1.1 * gain_multiplier;
    k_vel = control_params.k_vel * 1.2 * gain_multiplier;
    k_int = 1.2 * integral_multiplier;
elseif error_pos_norm > 0.1
    % 小误差：精确控制
    k_pos = control_params.k_pos * 1.3 * gain_multiplier;
    k_vel = control_params.k_vel * 1.4 * gain_multiplier;
    k_int = 1.8 * integral_multiplier;
else
    % 微小误差：超精确控制
    k_pos = control_params.k_pos * 1.5 * gain_multiplier;
    k_vel = control_params.k_vel * 1.6 * gain_multiplier;
    k_int = 2.2 * integral_multiplier;
end

% 强化积分控制 - 10秒后开始强化积分以消除稳态误差
if controller_time > 10.0
    % 10秒后强化积分控制
    if error_pos_norm < 1.0
        integral_pos = integral_pos + error_pos * dt;
    else
        integral_pos = integral_pos * 0.99;  % 缓慢衰减
    end
else
    % 10秒前标准积分控制
    if error_pos_norm < 0.8
        integral_pos = integral_pos + error_pos * dt;
    else
        integral_pos = integral_pos * 0.98;
    end
end

% 动态积分限制 - 根据时间和误差调整
if controller_time > 12.0
    % 12秒后放宽积分限制
    if error_pos_norm < 0.05
        max_integral = 0.8;  % 微小误差时允许大积分
    elseif error_pos_norm < 0.2
        max_integral = 0.6;
    else
        max_integral = 0.4;
    end
else
    % 12秒前保守积分限制
    if error_pos_norm < 0.1
        max_integral = 0.5;
    else
        max_integral = 0.3;
    end
end

integral_pos = max(-max_integral, min(max_integral, integral_pos));

% 位置控制律 - 强化PID控制
acc_cmd = -k_pos * error_pos - k_vel * error_vel - k_int * integral_pos;

% 推力计算
phi = euler(1); theta = euler(2);
cos_phi = cos(phi); cos_theta = cos(theta);

% 避免除零
if abs(cos_phi * cos_theta) < 0.1
    cos_phi = sign(cos_phi) * 0.1;
    cos_theta = sign(cos_theta) * 0.1;
end

T = control_params.mass * (acc_cmd(3) + control_params.gravity) / (cos_phi * cos_theta);

% 推力限幅
T = max(control_params.T_min, min(control_params.T_max, T));

% 期望姿态计算
g = control_params.gravity;
phi_d = (acc_cmd(1) * sin(euler(3)) - acc_cmd(2) * cos(euler(3))) / g;
theta_d = (acc_cmd(1) * cos(euler(3)) + acc_cmd(2) * sin(euler(3))) / g;
psi_d = 0;

% 姿态角限制
max_angle = pi/4;  % 45度限制
phi_d = max(-max_angle, min(max_angle, phi_d));
theta_d = max(-max_angle, min(max_angle, theta_d));

% 姿态误差
euler_d = [phi_d; theta_d; psi_d];
error_att = euler - euler_d;
error_att_norm = norm(error_att);

% 姿态增益调度
if error_att_norm > 0.3
    k_att = control_params.k_att * 1.2 * gain_multiplier;
    k_omega = control_params.k_omega * 1.2 * gain_multiplier;
    k_int_att = 0.4 * integral_multiplier;
elseif error_att_norm > 0.1
    k_att = control_params.k_att * 1.1 * gain_multiplier;
    k_omega = control_params.k_omega * 1.3 * gain_multiplier;
    k_int_att = 0.6 * integral_multiplier;
else
    k_att = control_params.k_att * 1.3 * gain_multiplier;
    k_omega = control_params.k_omega * 1.5 * gain_multiplier;
    k_int_att = 0.8 * integral_multiplier;
end

% 姿态积分项
if controller_time > 10.0
    if error_att_norm < 0.4
        integral_att = integral_att + error_att * dt;
    else
        integral_att = integral_att * 0.99;
    end
else
    if error_att_norm < 0.3
        integral_att = integral_att + error_att * dt;
    else
        integral_att = integral_att * 0.98;
    end
end

% 姿态积分限制
if controller_time > 12.0
    if error_att_norm < 0.02
        max_integral_att = 0.5;
    elseif error_att_norm < 0.1
        max_integral_att = 0.3;
    else
        max_integral_att = 0.2;
    end
else
    max_integral_att = 0.2;
end

integral_att = max(-max_integral_att, min(max_integral_att, integral_att));

% 姿态控制律
omega_d = zeros(3, 1);
error_omega = omega - omega_d;

tau = -k_att * error_att - k_omega * error_omega - k_int_att * integral_att;

% 力矩限幅
tau = max(-control_params.tau_max, min(control_params.tau_max, tau));

% 输出控制量
u = [T; tau];

% 调试信息
persistent debug_counter
if isempty(debug_counter)
    debug_counter = 0;
end
debug_counter = debug_counter + 1;

if mod(debug_counter, 500) == 0  % 每5秒输出一次
    fprintf('最终控制器 [%.1fs] - 位置误差: %.4f m, 速度误差: %.4f m/s, 增益倍数: %.2f\n', ...
            controller_time, error_pos_norm, norm(error_vel), gain_multiplier);
end

end
