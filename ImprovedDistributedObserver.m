function [x_hat_new, quality] = ImprovedDistributedObserver(agent_id, x_agents, x_hat_agents, neighbors, params, dt)
% 改进的分布式观测器
% 重点解决估计误差过大问题，提高观测器精度和稳定性
% 输入参数:
%   agent_id - 当前无人机ID
%   x_agents - 所有无人机真实状态矩阵 (12 x N)
%   x_hat_agents - 所有无人机状态估计矩阵 (12 x N)
%   neighbors - 邻接矩阵 (N x N)
%   params - 观测器参数结构体
%   dt - 时间步长
% 输出:
%   x_hat_new - 更新后的状态估计 (12x1)
%   quality - 观测器质量指标 (0-1)

% 获取当前状态估计
x_hat_i = x_hat_agents(:, agent_id);

% 生成高质量测量（降低噪声）
y_pos = x_agents(1:3, agent_id) + params.noise_pos * randn(3, 1);
y_att = x_agents(7:9, agent_id) + params.noise_att * randn(3, 1);

%% 高精度局部观测器更新
% 强化位置估计校正
pos_error = y_pos - x_hat_i(1:3);
x_hat_i(1:3) = x_hat_i(1:3) + params.alpha_pos * pos_error;

% 改进的速度估计
persistent prev_pos_improved prev_time_improved prev_vel_improved
if isempty(prev_pos_improved)
    prev_pos_improved = zeros(3, 10);
    prev_time_improved = zeros(1, 10);
    prev_vel_improved = zeros(3, 10);
end

if agent_id <= size(prev_pos_improved, 2)
    if prev_time_improved(agent_id) > 0
        % 多点差分速度估计（提高精度）
        vel_diff = (y_pos - prev_pos_improved(:, agent_id)) / dt;
        
        % 速度平滑滤波
        if prev_time_improved(agent_id) > dt
            vel_filtered = 0.7 * vel_diff + 0.3 * prev_vel_improved(:, agent_id);
        else
            vel_filtered = vel_diff;
        end
        
        % 强化速度估计校正
        vel_error = vel_filtered - x_hat_i(4:6);
        x_hat_i(4:6) = x_hat_i(4:6) + params.alpha_vel * vel_error;
        
        prev_vel_improved(:, agent_id) = vel_filtered;
    end
    prev_pos_improved(:, agent_id) = y_pos;
    prev_time_improved(agent_id) = prev_time_improved(agent_id) + dt;
end

% 强化姿态估计校正
att_error = y_att - x_hat_i(7:9);
x_hat_i(7:9) = x_hat_i(7:9) + params.alpha_att * att_error;

% 改进的角速度估计
persistent prev_att_improved prev_omega_improved
if isempty(prev_att_improved)
    prev_att_improved = zeros(3, 10);
    prev_omega_improved = zeros(3, 10);
end

if agent_id <= size(prev_att_improved, 2)
    if prev_time_improved(agent_id) > dt
        % 角速度差分估计
        omega_diff = (y_att - prev_att_improved(:, agent_id)) / dt;
        
        % 角速度平滑滤波
        if prev_time_improved(agent_id) > 2*dt
            omega_filtered = 0.6 * omega_diff + 0.4 * prev_omega_improved(:, agent_id);
        else
            omega_filtered = omega_diff;
        end
        
        % 强化角速度估计校正
        omega_error = omega_filtered - x_hat_i(10:12);
        x_hat_i(10:12) = x_hat_i(10:12) + params.alpha_omega * omega_error;
        
        prev_omega_improved(:, agent_id) = omega_filtered;
    end
    prev_att_improved(:, agent_id) = y_att;
end

%% 增强分布式一致性更新
N = size(x_hat_agents, 2);
consensus_term = zeros(12, 1);
neighbor_count = 0;
consensus_weights = zeros(12, 1);

% 计算加权一致性误差
for j = 1:N
    if neighbors(agent_id, j) == 1 && j ~= agent_id
        % 计算邻居的可信度（基于估计质量）
        neighbor_pos_error = norm(x_hat_agents(1:3, j) - x_agents(1:3, j));
        neighbor_reliability = exp(-neighbor_pos_error / 0.5);  % 可信度函数
        
        % 位置一致性（加权）
        pos_consensus = neighbor_reliability * (x_hat_agents(1:3, j) - x_hat_i(1:3));
        consensus_term(1:3) = consensus_term(1:3) + pos_consensus;
        consensus_weights(1:3) = consensus_weights(1:3) + neighbor_reliability;
        
        % 速度一致性（加权）
        vel_consensus = neighbor_reliability * (x_hat_agents(4:6, j) - x_hat_i(4:6));
        consensus_term(4:6) = consensus_term(4:6) + vel_consensus;
        consensus_weights(4:6) = consensus_weights(4:6) + neighbor_reliability;
        
        % 姿态一致性（较小权重，加权）
        att_consensus = 0.3 * neighbor_reliability * (x_hat_agents(7:9, j) - x_hat_i(7:9));
        consensus_term(7:9) = consensus_term(7:9) + att_consensus;
        consensus_weights(7:9) = consensus_weights(7:9) + 0.3 * neighbor_reliability;
        
        % 角速度一致性（较小权重，加权）
        omega_consensus = 0.3 * neighbor_reliability * (x_hat_agents(10:12, j) - x_hat_i(10:12));
        consensus_term(10:12) = consensus_term(10:12) + omega_consensus;
        consensus_weights(10:12) = consensus_weights(10:12) + 0.3 * neighbor_reliability;
        
        neighbor_count = neighbor_count + 1;
    end
end

% 应用加权分布式一致性校正
if neighbor_count > 0
    % 归一化一致性项
    for state_idx = 1:12
        if consensus_weights(state_idx) > 0
            consensus_term(state_idx) = consensus_term(state_idx) / consensus_weights(state_idx);
        end
    end
    
    % 自适应一致性增益
    adaptive_beta = params.beta_consensus * min(2.0, 1.0 + 0.2 * neighbor_count);
    
    % 应用一致性更新
    x_hat_i = x_hat_i + adaptive_beta * consensus_term;
end

%% 高级观测器功能

% 1. 动态模型预测校正
if prev_time_improved(agent_id) > dt
    % 基于动态模型的状态预测
    predicted_pos = x_hat_i(1:3) + dt * x_hat_i(4:6);
    predicted_vel = x_hat_i(4:6);  % 简化：假设速度变化较慢
    
    % 预测误差评估
    pos_prediction_error = norm(predicted_pos - y_pos);
    
    % 如果预测精度高，增强对模型的信任
    if pos_prediction_error < 0.2
        model_trust = 0.15;
        x_hat_i(1:3) = (1 - model_trust) * x_hat_i(1:3) + model_trust * predicted_pos;
    end
end

% 2. 自适应噪声估计
persistent noise_history_improved
if isempty(noise_history_improved)
    noise_history_improved = zeros(10, 5);  % 10个无人机，5个历史点
end

if agent_id <= size(noise_history_improved, 1)
    % 更新噪声历史
    current_noise = norm(pos_error);
    noise_history_improved(agent_id, :) = [noise_history_improved(agent_id, 2:end), current_noise];
    
    % 自适应调整观测器增益
    avg_noise = mean(noise_history_improved(agent_id, :));
    if avg_noise > 0.1
        % 噪声较大时降低增益
        adaptive_factor = 0.8;
    else
        % 噪声较小时提高增益
        adaptive_factor = 1.2;
    end
    
    % 应用自适应调整（但保持稳定）
    adaptive_factor = max(0.5, min(1.5, adaptive_factor));
    % 注意：这里不直接修改params，而是在下次更新时应用
end

%% 严格的数值稳定性保护
% 物理约束（更严格）
max_pos = 20;
x_hat_i(1:3) = max(-max_pos, min(max_pos, x_hat_i(1:3)));

max_vel = 8;
x_hat_i(4:6) = max(-max_vel, min(max_vel, x_hat_i(4:6)));

max_att = pi/6;  % 30度限制（更严格）
x_hat_i(7:9) = max(-max_att, min(max_att, x_hat_i(7:9)));

max_omega = 2.0;
x_hat_i(10:12) = max(-max_omega, min(max_omega, x_hat_i(10:12)));

% 估计质量监控和自适应重置
estimation_error_norm = norm(x_hat_i - x_agents(:, agent_id));
if estimation_error_norm > params.max_estimation_error
    % 渐进重置机制（更温和）
    reset_factor = 0.02;
    x_hat_i(1:3) = (1 - reset_factor) * x_hat_i(1:3) + reset_factor * y_pos;
    x_hat_i(7:9) = (1 - reset_factor) * x_hat_i(7:9) + reset_factor * y_att;
    
    % 速度和角速度软重置
    x_hat_i(4:6) = 0.95 * x_hat_i(4:6);
    x_hat_i(10:12) = 0.95 * x_hat_i(10:12);
end

%% 观测器质量评估
% 计算多维度质量指标
pos_quality = exp(-norm(x_hat_i(1:3) - x_agents(1:3, agent_id)) / 0.3);
vel_quality = exp(-norm(x_hat_i(4:6) - x_agents(4:6, agent_id)) / 0.5);
att_quality = exp(-norm(x_hat_i(7:9) - x_agents(7:9, agent_id)) / 0.2);

% 综合质量指标
quality = 0.5 * pos_quality + 0.3 * vel_quality + 0.2 * att_quality;
quality = max(0, min(1, quality));  % 限制在[0,1]范围

% 如果质量过低，应用额外的稳定化措施
if quality < params.quality_threshold
    stabilization_factor = 0.05;
    x_hat_i(1:3) = (1 - stabilization_factor) * x_hat_i(1:3) + stabilization_factor * y_pos;
    x_hat_i(4:6) = 0.9 * x_hat_i(4:6);  % 减速稳定
end

%% 输出更新后的状态估计
x_hat_new = x_hat_i;

%% 改进的调试信息
persistent debug_counter_improved
if isempty(debug_counter_improved)
    debug_counter_improved = zeros(1, 10);
end

if agent_id <= length(debug_counter_improved)
    debug_counter_improved(agent_id) = debug_counter_improved(agent_id) + 1;
    
    % 每5秒输出一次详细调试信息
    if mod(debug_counter_improved(agent_id), 500) == 0
        pos_est_error = norm(x_hat_i(1:3) - x_agents(1:3, agent_id));
        vel_est_error = norm(x_hat_i(4:6) - x_agents(4:6, agent_id));
        
        uav_type = sprintf('跟随者%d', agent_id-1);
        
        fprintf('改进观测器[%s] [%.1fs] - 位置估计误差: %.4f m, 速度估计误差: %.4f m/s, 邻居数: %d, 质量: %.3f\n', ...
                uav_type, debug_counter_improved(agent_id)*0.01, pos_est_error, vel_est_error, neighbor_count, quality);
    end
end

end
