function u = OptimizedFastController(x, x_desired, control_params)
% 优化快速控制器 - 基于稳定控制器改进，实现5-15秒快速收敛
% 使用适度增益和智能积分控制

persistent integral_pos integral_att

% 初始化持久变量
if isempty(integral_pos)
    integral_pos = zeros(3, 1);
    integral_att = zeros(3, 1);
end

% 提取状态变量
pos = x(1:3);           % 位置 [x; y; z]
vel = x(4:6);           % 速度 [vx; vy; vz]
euler = x(7:9);         % 欧拉角 [phi; theta; psi]
omega = x(10:12);       % 角速度 [p; q; r]

% 期望状态
pos_d = x_desired(1:3);
vel_d = x_desired(4:6);
acc_d = x_desired(7:9);

% 位置和速度误差
error_pos = pos - pos_d;
error_vel = vel - vel_d;

% 优化的快速收敛增益 - 基于稳定系统的增益进行适度提升
k_pos = 3.5;        % 位置增益 (从2.5提升到3.5)
k_vel = 2.8;        % 速度增益 (从1.8提升到2.8)
k_int_pos = 0.8;    % 积分增益 (适度积分)

% 积分项计算 (智能积分控制)
dt = 0.01;
error_pos_norm = norm(error_pos);

% 只在误差较小时使用积分控制，避免积分饱和
if error_pos_norm < 1.0  % 误差小于1m时才积分
    integral_pos = integral_pos + error_pos * dt;
else
    integral_pos = integral_pos * 0.9; % 大误差时衰减积分项
end

% 积分抗饱和
max_integral = 0.5;
integral_pos = max(-max_integral, min(max_integral, integral_pos));

% 位置控制律 - 改进的PD+I控制
acc_cmd = -k_pos * error_pos - k_vel * error_vel - k_int_pos * integral_pos + acc_d;

% 推力计算
phi = euler(1); theta = euler(2);
T = control_params.mass * (acc_cmd(3) + control_params.gravity) / (cos(phi) * cos(theta));

% 推力限幅
T = max(control_params.T_min, min(control_params.T_max, T));

% 期望姿态计算
phi_d = (acc_cmd(1) * sin(euler(3)) - acc_cmd(2) * cos(euler(3))) / control_params.gravity;
theta_d = (acc_cmd(1) * cos(euler(3)) + acc_cmd(2) * sin(euler(3))) / control_params.gravity;
psi_d = 0; % 偏航角保持为0

% 姿态误差
euler_d = [phi_d; theta_d; psi_d];
error_att = euler - euler_d;

% 姿态控制增益 - 适度提升
k_att = 4.5;        % 姿态增益 (从3.0提升到4.5)
k_omega = 2.5;      % 角速度增益 (从1.5提升到2.5)
k_int_att = 0.3;    % 姿态积分增益

% 姿态积分项 (智能积分)
error_att_norm = norm(error_att);
if error_att_norm < 0.2  % 姿态误差小于0.2rad时才积分
    integral_att = integral_att + error_att * dt;
else
    integral_att = integral_att * 0.9; % 大误差时衰减积分项
end

% 积分抗饱和
max_integral_att = 0.2;
integral_att = max(-max_integral_att, min(max_integral_att, integral_att));

% 姿态控制律
omega_d = zeros(3, 1); % 期望角速度为0
error_omega = omega - omega_d;

tau = -k_att * error_att - k_omega * error_omega - k_int_att * integral_att;

% 力矩限幅
tau = max(-control_params.tau_max, min(control_params.tau_max, tau));

% 输出控制量
u = [T; tau];

end
