function [x_hat_dot, x_hat] = DistributedObserver(agent_id, x_agents, y_agents, x_hat_agents, neighbors, params)
% 分布式观测器 - 基于一致性的状态估计
% 输入参数:
%   agent_id - 当前无人机ID
%   x_agents - 所有无人机真实状态矩阵 (12 x N) [仅用于仿真，实际中不可获得]
%   y_agents - 所有无人机测量输出矩阵 (6 x N) [位置和姿态测量]
%   x_hat_agents - 所有无人机状态估计矩阵 (12 x N)
%   neighbors - 邻接矩阵，表示通信拓扑 (N x N)
%   params - 观测器参数结构体
% 输出:
%   x_hat_dot - 状态估计导数 (12x1)
%   x_hat - 当前状态估计 (12x1)

% 获取当前无人机的状态估计和测量
x_hat_i = x_hat_agents(:, agent_id);
y_i = y_agents(:, agent_id);

% 观测器参数
L = params.observer_gain;          % 观测器增益矩阵 (12x6)
k_consensus_obs = params.k_consensus_obs;  % 观测器一致性增益
A = params.A;                      % 系统矩阵 (线性化后)
B = params.B;                      % 输入矩阵
C = params.C;                      % 输出矩阵 (6x12)

% 获取当前控制输入 (这里简化处理，实际中需要从控制器获取)
u_i = zeros(4, 1);  % 控制输入，实际应用中需要传入

% 预测的输出
y_hat_i = C * x_hat_i;

% 输出误差
e_y_i = y_i - y_hat_i;

% 一致性项计算
consensus_term = zeros(12, 1);
N = size(x_hat_agents, 2);  % 无人机数量

% 计算与邻居的状态估计一致性误差
for j = 1:N
    if neighbors(agent_id, j) == 1 && j ~= agent_id
        % 状态估计一致性
        consensus_term = consensus_term + k_consensus_obs * (x_hat_agents(:, j) - x_hat_i);
    end
end

% 分布式观测器动态方程
% x_hat_dot = A * x_hat + B * u + L * (y - C * x_hat) + consensus_term
x_hat_dot = A * x_hat_i + B * u_i + L * e_y_i + consensus_term;

% 检查并处理NaN值
if any(isnan(x_hat_dot))
    x_hat_dot = zeros(12, 1);
end

% 输出当前状态估计
x_hat = x_hat_i;

end
