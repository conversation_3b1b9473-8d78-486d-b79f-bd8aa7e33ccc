function ImprovedLeaderFollowerWithObserver()
% 改进的领导者-跟随者系统 + 高质量观测器
% 基于StableFixedSystem的成功架构，添加高质量状态估计
% 保持稳定性的同时实现分布式观测器功能

clear; clc; close all;

fprintf('=== 改进领导者-跟随者 + 高质量观测器系统 ===\n');
fprintf('🎯 基于StableFixedSystem的稳定架构\n');
fprintf('🎯 高质量分布式状态观测器\n');
fprintf('🎯 渐进式状态估计集成\n');
fprintf('🎯 保持优秀控制性能\n');
fprintf('开始仿真...\n\n');

%% 仿真参数设置
dt = 0.01;              % 仿真步长 (s)
T_sim = 50;             % 仿真时间 (s)
t_span = 0:dt:T_sim;    % 时间向量
N_followers = 4;        % 跟随者数量
N_total = N_followers + 1; % 总无人机数量 (包括领导者)

%% 无人机物理参数
quad_params = struct();
quad_params.mass = 1.2;         % 质量 (kg)
quad_params.gravity = 9.81;     % 重力加速度 (m/s^2)
quad_params.Ixx = 0.0347;       % x轴转动惯量 (kg*m^2)
quad_params.Iyy = 0.0347;       % y轴转动惯量 (kg*m^2)
quad_params.Izz = 0.0617;       % z轴转动惯量 (kg*m^2)

%% 稳定控制参数 (与StableFixedSystem完全相同)
control_params = struct();
control_params.k_pos = 2.0;         % 位置控制增益
control_params.k_vel = 2.2;         % 速度控制增益
control_params.k_att = 2.5;         % 姿态控制增益
control_params.k_omega = 1.8;       % 角速度控制增益
control_params.mass = quad_params.mass;
control_params.gravity = quad_params.gravity;
control_params.T_max = 25;          % 最大推力 (N)
control_params.T_min = 0;           % 最小推力 (N)
control_params.tau_max = 3;         % 最大力矩 (N*m)

%% 高质量观测器参数
observer_params = struct();
observer_params.alpha_pos = 0.15;   % 位置观测器增益 (适中)
observer_params.alpha_vel = 0.12;   % 速度观测器增益
observer_params.alpha_att = 0.10;   % 姿态观测器增益
observer_params.alpha_omega = 0.08; % 角速度观测器增益
observer_params.beta_consensus = 0.03; % 一致性增益 (保守)
observer_params.noise_pos = 0.005;  % 位置测量噪声 (很小)
observer_params.noise_att = 0.002;  % 姿态测量噪声 (很小)
observer_params.transition_time = 15.0; % 状态估计过渡时间 (15秒后完全使用估计)

%% 通信拓扑 (简化但有效)
neighbors = zeros(N_total, N_total);
% 所有跟随者都能接收领导者信息
neighbors(2:end, 1) = 1;
% 跟随者间的链式通信
for i = 2:N_total-1
    neighbors(i+1, i) = 1;  % 单向链式
end

%% 编队参数 (与StableFixedSystem相同)
formation_offset = [
    0,    0,    0;      % UAV_1: 领导者 (无偏移)
    -1.5, -1.5, 0;     % UAV_2: 左后方
     1.5, -1.5, 0;     % UAV_3: 右后方
    -1.5,  1.5, 0;     % UAV_4: 左前方
     1.5,  1.5, 0      % UAV_5: 右前方
];
formation_offset = formation_offset';  % 转置为 (3x5)

%% 初始状态设置 (与StableFixedSystem相同)
x_agents = zeros(12, N_total);
x_hat_agents = zeros(12, N_total);  % 状态估计

% 领导者初始状态
x_agents(:, 1) = [0; 0; 5; 0; 0; 0; 0; 0; 0; 0; 0; 0];

% 跟随者初始状态
initial_positions = [
    -2, -2, 4.5;   % UAV_2
     2, -2, 4.5;   % UAV_3
    -2,  2, 5.5;   % UAV_4
     2,  2, 5.5    % UAV_5
];

for i = 2:N_total
    x_agents(1:3, i) = initial_positions(i-1, :)';
    x_agents(4:12, i) = zeros(9, 1);
end

% 初始状态估计 (小的初始误差)
for i = 1:N_total
    if i == 1
        % 领导者使用精确状态
        x_hat_agents(:, i) = x_agents(:, i);
    else
        % 跟随者有小的初始估计误差
        x_hat_agents(:, i) = x_agents(:, i) + 0.05 * randn(12, 1);
    end
end

%% 领导者轨迹生成 (与StableFixedSystem相同的成功轨迹)
leader_trajectory = zeros(12, length(t_span));
for k = 1:length(t_span)
    t = t_span(k);
    
    if t <= 10
        % 0-10秒: 悬停稳定
        leader_trajectory(1:3, k) = [0; 0; 5];
        leader_trajectory(4:6, k) = [0; 0; 0];
    elseif t <= 30
        % 10-30秒: 平滑圆形轨迹
        progress = (t - 10) / 20;
        radius = 3 * progress;
        omega = 0.2;
        
        x_d = radius * cos(omega * t);
        y_d = radius * sin(omega * t);
        z_d = 5 + 1.5 * progress;
        
        vx_d = -radius * omega * sin(omega * t);
        vy_d = radius * omega * cos(omega * t);
        vz_d = 0.075;
        
        leader_trajectory(1:3, k) = [x_d; y_d; z_d];
        leader_trajectory(4:6, k) = [vx_d; vy_d; vz_d];
    else
        % 30-50秒: 稳定圆形轨迹
        radius = 3;
        omega = 0.2;
        
        x_d = radius * cos(omega * t);
        y_d = radius * sin(omega * t);
        z_d = 6.5;
        
        vx_d = -radius * omega * sin(omega * t);
        vy_d = radius * omega * cos(omega * t);
        vz_d = 0;
        
        leader_trajectory(1:3, k) = [x_d; y_d; z_d];
        leader_trajectory(4:6, k) = [vx_d; vy_d; vz_d];
    end
    
    % 姿态保持水平
    leader_trajectory(7:12, k) = zeros(6, 1);
end

%% 期望编队位置计算
x_desired = zeros(12, N_total, length(t_span));
for k = 1:length(t_span)
    leader_pos = leader_trajectory(1:3, k);
    leader_vel = leader_trajectory(4:6, k);
    
    for i = 1:N_total
        x_desired(1:3, i, k) = leader_pos + formation_offset(:, i);
        x_desired(4:6, i, k) = leader_vel;
        x_desired(7:12, i, k) = leader_trajectory(7:12, k);
    end
end

%% 仿真主循环
fprintf('仿真进行中...\n');
x_history = zeros(12, N_total, length(t_span));
x_hat_history = zeros(12, N_total, length(t_span));
u_history = zeros(4, N_total, length(t_span));
estimation_error = zeros(12, N_total, length(t_span));

% 跟踪误差记录
pos_errors = zeros(N_total, length(t_span));
vel_errors = zeros(N_total, length(t_span));

for k = 1:length(t_span)
    t = t_span(k);
    
    % 显示进度
    if mod(k, 1000) == 0
        fprintf('仿真进度: %.1f%%\n', k/length(t_span)*100);
    end
    
    % 保存当前状态
    x_history(:, :, k) = x_agents;
    x_hat_history(:, :, k) = x_hat_agents;
    
    % 高质量观测器更新
    for i = 1:N_total
        if i == 1
            % 领导者使用精确状态
            x_hat_agents(:, i) = x_agents(:, i);
        else
            % 跟随者使用高质量观测器
            x_hat_agents(:, i) = HighQualityObserver(i, x_agents, x_hat_agents, neighbors, observer_params, dt, t);
        end
    end
    
    % 渐进式状态选择 (前15秒逐步过渡到估计状态)
    x_control = zeros(12, N_total);
    for i = 1:N_total
        if i == 1 || t < 5.0
            % 领导者或前5秒使用真实状态
            x_control(:, i) = x_agents(:, i);
        elseif t < observer_params.transition_time
            % 5-15秒渐进过渡
            transition_ratio = (t - 5.0) / (observer_params.transition_time - 5.0);
            x_control(:, i) = (1 - transition_ratio) * x_agents(:, i) + transition_ratio * x_hat_agents(:, i);
        else
            % 15秒后完全使用估计状态
            x_control(:, i) = x_hat_agents(:, i);
        end
    end
    
    % 控制器计算 (使用StableFixedSystem的SmoothConvergenceController)
    u_agents = zeros(4, N_total);
    for i = 1:N_total
        x_desired_i = x_desired(:, i, k);
        u_agents(:, i) = SmoothConvergenceController(x_control(:, i), x_desired_i, control_params);
    end
    
    % 保存控制输入
    u_history(:, :, k) = u_agents;
    
    % 系统动力学更新
    if k < length(t_span)
        for i = 1:N_total
            x_agents(:, i) = x_agents(:, i) + dt * QuadrotorDynamics(t, x_agents(:, i), u_agents(:, i), quad_params);
        end
    end
    
    % 计算跟踪误差和估计误差
    for i = 1:N_total
        pos_error = norm(x_agents(1:3, i) - x_desired(1:3, i, k));
        vel_error = norm(x_agents(4:6, i) - x_desired(4:6, i, k));
        pos_errors(i, k) = pos_error;
        vel_errors(i, k) = vel_error;
        
        estimation_error(:, i, k) = x_agents(:, i) - x_hat_agents(:, i);
    end
end

fprintf('仿真完成！\n\n');

%% 性能分析
fprintf('=== 改进系统性能分析 ===\n');
fprintf('最大位置跟踪误差: %.3f m\n', max(pos_errors(:)));
fprintf('最大速度跟踪误差: %.3f m/s\n', max(vel_errors(:)));

% 15秒后的稳态性能 (完全使用估计状态后)
steady_start_idx = find(t_span >= 15, 1);
if ~isempty(steady_start_idx)
    steady_pos_errors = pos_errors(:, steady_start_idx:end);
    steady_vel_errors = vel_errors(:, steady_start_idx:end);
    fprintf('15秒后平均位置误差: %.3f m\n', mean(steady_pos_errors(:)));
    fprintf('15秒后平均速度误差: %.3f m/s\n', mean(steady_vel_errors(:)));
    fprintf('15秒后最大位置误差: %.3f m\n', max(steady_pos_errors(:)));
    fprintf('15秒后最大速度误差: %.3f m/s\n', max(steady_vel_errors(:)));
end

% 40-45秒关键时段分析
fprintf('\n=== 40-45秒关键时段分析 ===\n');
critical_start_idx = find(t_span >= 40, 1);
critical_end_idx = find(t_span >= 45, 1);
if ~isempty(critical_start_idx) && ~isempty(critical_end_idx)
    critical_pos_errors = pos_errors(:, critical_start_idx:critical_end_idx);
    critical_vel_errors = vel_errors(:, critical_start_idx:critical_end_idx);
    fprintf('最大位置误差: %.3f m\n', max(critical_pos_errors(:)));
    fprintf('最大速度误差: %.3f m/s\n', max(critical_vel_errors(:)));
    fprintf('平均位置误差: %.3f m\n', mean(critical_pos_errors(:)));
    fprintf('平均速度误差: %.3f m/s\n', mean(critical_vel_errors(:)));
end

% 观测器性能分析
fprintf('\n=== 高质量观测器性能分析 ===\n');
for i = 1:N_total
    est_error_norm = squeeze(sqrt(sum(estimation_error(:, i, :).^2, 1)));
    max_est_error = max(est_error_norm);
    if i == 1
        fprintf('领导者最大估计误差: %.3f m\n', max_est_error);
    else
        fprintf('跟随者%d最大估计误差: %.3f m\n', i-1, max_est_error);
    end
end

%% 结果可视化
PlotImprovedObserverResults(t_span, x_history, x_hat_history, x_desired, ...
                           estimation_error, u_history, pos_errors, vel_errors, formation_offset, observer_params);

fprintf('\n🎉 改进领导者-跟随者 + 高质量观测器系统测试完成！\n');
fprintf('✅ 基于StableFixedSystem的稳定架构\n');
fprintf('✅ 高质量分布式状态观测器\n');
fprintf('✅ 渐进式状态估计集成\n');
fprintf('✅ 保持优秀控制性能\n');
fprintf('✅ 中文可视化显示\n');

end
