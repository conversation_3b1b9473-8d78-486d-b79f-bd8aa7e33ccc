function [u] = LeaderFollowerController(agent_id, x_hat_agents, x_desired, params, t)
% 领导者-跟随者控制器 - 使用状态估计
% 专门设计用于解决振荡问题的平滑收敛控制器
% 输入参数:
%   agent_id - 当前无人机ID
%   x_hat_agents - 所有无人机状态估计矩阵 (12 x N)
%   x_desired - 期望状态 (12x1)
%   params - 控制参数结构体
%   t - 当前时间
% 输出:
%   u - 控制输入 [推力; 力矩x; 力矩y; 力矩z] (4x1)

persistent integral_pos integral_att controller_time error_history

% 初始化持久变量
if isempty(integral_pos)
    integral_pos = zeros(3, 10);  % 支持最多10个无人机
    integral_att = zeros(3, 10);
    controller_time = zeros(1, 10);
    error_history = zeros(3, 10, 10);  % 每个无人机保存10个历史误差
end

% 更新控制器时间
dt = 0.01;
if agent_id <= length(controller_time)
    controller_time(agent_id) = controller_time(agent_id) + dt;
end

% 获取当前无人机状态估计
x_hat_i = x_hat_agents(:, agent_id);

% 提取状态变量
pos_i = x_hat_i(1:3);          % 当前位置估计
vel_i = x_hat_i(4:6);          % 当前速度估计
euler_i = x_hat_i(7:9);        % 当前欧拉角估计
omega_i = x_hat_i(10:12);      % 当前角速度估计

% 期望状态
pos_d_i = x_desired(1:3);      % 期望位置
vel_d_i = x_desired(4:6);      % 期望速度
euler_d_i = x_desired(7:9);    % 期望欧拉角
omega_d_i = x_desired(10:12);  % 期望角速度

% 基本跟踪误差
error_pos = pos_i - pos_d_i;
error_vel = vel_i - vel_d_i;
error_pos_norm = norm(error_pos);

% 更新误差历史 (用于振荡检测)
if agent_id <= size(error_history, 3)
    error_history(:, :, agent_id) = [error_history(:, 2:end, agent_id), error_pos];
end

%% 振荡检测算法
oscillation_detected = false;
if agent_id <= length(controller_time) && controller_time(agent_id) > 5.0
    sign_changes = 0;
    for dim = 1:3  % 检查x,y,z三个方向
        for j = 2:10
            if agent_id <= size(error_history, 3)
                if sign(error_history(dim, j, agent_id)) ~= sign(error_history(dim, j-1, agent_id))
                    sign_changes = sign_changes + 1;
                end
            end
        end
    end
    if sign_changes > 12  % 如果符号变化太频繁，认为存在振荡
        oscillation_detected = true;
    end
end

%% 自适应增益调度
% 基础增益
k_pos_base = params.k_pos;
k_vel_base = params.k_vel;

% 时间分段增益调度
if agent_id <= length(controller_time)
    current_time = controller_time(agent_id);
else
    current_time = t;
end

if current_time <= 8.0
    % 0-8秒：稳定收敛阶段
    gain_mult = 1.0;
elseif current_time <= 15.0
    % 8-15秒：逐步增强阶段
    progress = (current_time - 8.0) / 7.0;
    gain_mult = 1.0 + 0.1 * progress;  % 从1.0增加到1.1
else
    % 15秒后：精确控制阶段
    gain_mult = 1.15;
end

% 振荡抑制策略
if oscillation_detected
    % 检测到振荡时，降低比例增益，增加阻尼
    k_pos = k_pos_base * 0.75 * gain_mult;
    k_vel = k_vel_base * 1.8 * gain_mult;   % 增加阻尼
    k_int = 0.2;  % 减少积分作用
else
    % 正常情况下的增益调整
    if error_pos_norm > 1.0
        % 大误差时：稳定收敛
        k_pos = k_pos_base * 1.0 * gain_mult;
        k_vel = k_vel_base * 1.2 * gain_mult;
        k_int = 0.4;
    elseif error_pos_norm > 0.3
        % 中等误差时：平衡性能
        k_pos = k_pos_base * 1.1 * gain_mult;
        k_vel = k_vel_base * 1.4 * gain_mult;
        k_int = 0.6;
    else
        % 小误差时：精确控制，重点增加阻尼
        k_pos = k_pos_base * 1.0 * gain_mult;
        k_vel = k_vel_base * 1.6 * gain_mult;  % 强阻尼
        k_int = 0.8;
    end
end

%% 积分控制 (抗积分饱和)
if agent_id <= size(integral_pos, 2)
    if current_time > 8.0 && error_pos_norm < 1.5 && ~oscillation_detected
        integral_pos(:, agent_id) = integral_pos(:, agent_id) + error_pos * dt;
    else
        integral_pos(:, agent_id) = integral_pos(:, agent_id) * 0.95;  % 缓慢衰减
    end

    % 积分限制
    if oscillation_detected
        max_integral = 0.1;   % 振荡时严格限制
    elseif current_time > 12.0
        max_integral = 0.3;   % 12秒后适度放宽
    else
        max_integral = 0.2;
    end

    integral_pos(:, agent_id) = max(-max_integral, min(max_integral, integral_pos(:, agent_id)));
end

%% 位置控制律 (PID控制)
if agent_id <= size(integral_pos, 2)
    integral_term = integral_pos(:, agent_id);
else
    integral_term = zeros(3, 1);
end
acc_cmd = -k_pos * error_pos - k_vel * error_vel - k_int * integral_term;

%% 推力计算
phi = euler_i(1); theta = euler_i(2);
cos_phi = cos(phi); cos_theta = cos(theta);

% 避免除零
if abs(cos_phi * cos_theta) < 0.1
    cos_phi = sign(cos_phi) * 0.1;
    cos_theta = sign(cos_theta) * 0.1;
end

T = params.mass * (acc_cmd(3) + params.gravity) / (cos_phi * cos_theta);

% 推力限幅
T = max(params.T_min, min(params.T_max, T));

%% 期望姿态计算
g = params.gravity;
psi = euler_i(3);  % 保持当前偏航角

phi_d = (acc_cmd(1) * sin(psi) - acc_cmd(2) * cos(psi)) / g;
theta_d = (acc_cmd(1) * cos(psi) + acc_cmd(2) * sin(psi)) / g;
psi_d = euler_d_i(3);  % 期望偏航角

% 姿态角限制
max_angle = pi/6;  % 30度限制
phi_d = max(-max_angle, min(max_angle, phi_d));
theta_d = max(-max_angle, min(max_angle, theta_d));

%% 姿态控制
euler_d = [phi_d; theta_d; psi_d];
error_att = euler_i - euler_d;
error_omega = omega_i - omega_d_i;

% 姿态控制增益
if oscillation_detected
    k_att = params.k_att * 0.85;
    k_omega = params.k_omega * 1.3;
    k_int_att = 0.15;
else
    k_att = params.k_att * 1.0;
    k_omega = params.k_omega * 1.1;
    k_int_att = 0.3;
end

% 姿态积分项
error_att_norm = norm(error_att);
if agent_id <= size(integral_att, 2)
    if current_time > 10.0 && error_att_norm < 0.15 && ~oscillation_detected
        integral_att(:, agent_id) = integral_att(:, agent_id) + error_att * dt;
    else
        integral_att(:, agent_id) = integral_att(:, agent_id) * 0.98;
    end
    
    % 姿态积分限制
    if oscillation_detected
        max_integral_att = 0.05;
    else
        max_integral_att = 0.1;
    end
    integral_att(:, agent_id) = max(-max_integral_att, min(max_integral_att, integral_att(:, agent_id)));
end

% 姿态控制律
if agent_id <= size(integral_att, 2)
    integral_att_term = integral_att(:, agent_id);
else
    integral_att_term = zeros(3, 1);
end
tau = -k_att * error_att - k_omega * error_omega - k_int_att * integral_att_term;

% 力矩限幅
tau = max(-params.tau_max, min(params.tau_max, tau));

%% 输出控制量
u = [T; tau];

%% 调试信息
persistent debug_counter
if isempty(debug_counter)
    debug_counter = zeros(1, 10);
end

if agent_id <= length(debug_counter)
    debug_counter(agent_id) = debug_counter(agent_id) + 1;
    
    if mod(debug_counter(agent_id), 500) == 0  % 每5秒输出一次
        if oscillation_detected
            osc_status = '振荡检测';
        else
            osc_status = '正常';
        end
        
        if agent_id == 1
            uav_type = '领导者';
        else
            uav_type = sprintf('跟随者%d', agent_id-1);
        end
        
        fprintf('控制器[%s] [%.1fs] - 位置误差: %.4f m, 速度误差: %.4f m/s, 状态: %s\n', ...
                uav_type, current_time, error_pos_norm, norm(error_vel), osc_status);
    end
end

end
