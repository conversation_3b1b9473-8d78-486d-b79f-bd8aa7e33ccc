function [x_hat_updated, quality] = StableDistributedObserver(agent_id, x_agents, x_hat_agents, neighbors, params, dt)
% 稳定分布式观测器 - 专为完美混合架构设计
% 目标：稳定的状态估计，不影响主系统性能

persistent observer_states

% 初始化持久变量
if isempty(observer_states)
    N_total = size(x_agents, 2);
    observer_states = struct();
    for i = 1:N_total
        observer_states(i).integral_error = zeros(12, 1);
        observer_states(i).prev_consensus = zeros(12, 1);
        observer_states(i).reset_count = 0;
        observer_states(i).quality_history = ones(1, 10) * 0.8;
    end
end

% 获取当前智能体状态
x_hat_i = x_hat_agents(:, agent_id);
x_i = x_agents(:, agent_id);  % 真实状态（用于计算质量指标）

% 如果是领导者，直接返回真实状态
if agent_id == 1
    x_hat_updated = x_i;
    quality = 1.0;
    return;
end

%% 本地观测更新
% 位置和姿态可以直接观测（GPS + IMU）
y_pos = x_i(1:3) + params.noise_pos * randn(3, 1);
y_att = x_i(7:9) + params.noise_att * randn(3, 1);

% 本地观测器增益
L_pos = params.alpha_pos * eye(3);
L_vel = params.alpha_vel * eye(3);
L_att = params.alpha_att * eye(3);
L_omega = params.alpha_omega * eye(3);

%% 分布式一致性项
consensus_term = zeros(12, 1);
neighbor_count = 0;

for j = 1:size(x_hat_agents, 2)
    if neighbors(agent_id, j) == 1
        neighbor_count = neighbor_count + 1;
        state_diff = x_hat_agents(:, j) - x_hat_i;
        
        % 限制一致性项的幅度以保持稳定性
        for k = 1:12
            if abs(state_diff(k)) > 2.0  % 位置/速度限制
                state_diff(k) = sign(state_diff(k)) * 2.0;
            end
        end
        
        consensus_term = consensus_term + params.beta_consensus * state_diff;
    end
end

%% 状态估计更新
% 位置估计更新
pos_innovation = L_pos * (y_pos - x_hat_i(1:3));
pos_consensus = consensus_term(1:3);
x_hat_i(1:3) = x_hat_i(1:3) + dt * (x_hat_i(4:6) + pos_innovation + pos_consensus);

% 速度估计更新
vel_innovation = L_vel * (y_pos - x_hat_i(1:3));  % 基于位置误差的速度修正
vel_consensus = consensus_term(4:6);
x_hat_i(4:6) = x_hat_i(4:6) + dt * (vel_innovation + vel_consensus);

% 姿态估计更新
att_innovation = L_att * (y_att - x_hat_i(7:9));
att_consensus = consensus_term(7:9);
x_hat_i(7:9) = x_hat_i(7:9) + dt * (x_hat_i(10:12) + att_innovation + att_consensus);

% 角速度估计更新
omega_innovation = L_omega * (y_att - x_hat_i(7:9));  % 基于姿态误差的角速度修正
omega_consensus = consensus_term(10:12);
x_hat_i(10:12) = x_hat_i(10:12) + dt * (omega_innovation + omega_consensus);

%% 稳定性保护机制
% 1. 物理约束
max_pos = 15;  % 位置限制
x_hat_i(1:3) = max(-max_pos, min(max_pos, x_hat_i(1:3)));

max_vel = 8;   % 速度限制
x_hat_i(4:6) = max(-max_vel, min(max_vel, x_hat_i(4:6)));

max_att = pi/6;  % 姿态限制（30度）
x_hat_i(7:9) = max(-max_att, min(max_att, x_hat_i(7:9)));

max_omega = pi;  % 角速度限制
x_hat_i(10:12) = max(-max_omega, min(max_omega, x_hat_i(10:12)));

% 2. 估计误差检查和软重置
estimation_error = norm(x_hat_i - x_i);
if estimation_error > params.max_estimation_error
    % 软重置：逐渐向观测值靠近
    reset_factor = 0.05;  % 5%的重置率
    x_hat_i(1:3) = (1 - reset_factor) * x_hat_i(1:3) + reset_factor * y_pos;
    x_hat_i(7:9) = (1 - reset_factor) * x_hat_i(7:9) + reset_factor * y_att;
    
    % 减速处理
    x_hat_i(4:6) = 0.9 * x_hat_i(4:6);
    x_hat_i(10:12) = 0.9 * x_hat_i(10:12);
    
    observer_states(agent_id).reset_count = observer_states(agent_id).reset_count + 1;
end

%% 观测器质量评估
% 基于估计误差和邻居数量计算质量指标
pos_error = norm(x_hat_i(1:3) - x_i(1:3));
vel_error = norm(x_hat_i(4:6) - x_i(4:6));
att_error = norm(x_hat_i(7:9) - x_i(7:9));

% 归一化误差
normalized_pos_error = min(1, pos_error / 1.0);
normalized_vel_error = min(1, vel_error / 2.0);
normalized_att_error = min(1, att_error / (pi/6));

% 综合质量指标
base_quality = 1 - 0.4 * normalized_pos_error - 0.3 * normalized_vel_error - 0.3 * normalized_att_error;

% 邻居连接质量
neighbor_quality = min(1, neighbor_count / 3);  % 理想邻居数为3

% 一致性质量
consensus_magnitude = norm(consensus_term);
consensus_quality = max(0, 1 - consensus_magnitude / 0.5);

% 最终质量指标
quality = 0.6 * base_quality + 0.2 * neighbor_quality + 0.2 * consensus_quality;
quality = max(0, min(1, quality));

% 更新质量历史
observer_states(agent_id).quality_history = [observer_states(agent_id).quality_history(2:end), quality];

% 平滑质量指标
quality = mean(observer_states(agent_id).quality_history);

%% 输出更新后的状态估计
x_hat_updated = x_hat_i;

% 更新持久状态
observer_states(agent_id).prev_consensus = consensus_term;

end
