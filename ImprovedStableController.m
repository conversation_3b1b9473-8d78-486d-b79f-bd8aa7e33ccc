function u = ImprovedStableController(x, x_desired, control_params)
% 改进稳定控制器 - 基于稳定控制器，适度改进收敛速度，消除振荡
% 保持系统稳定性的前提下实现更好的收敛性能

persistent integral_pos integral_att

% 初始化持久变量
if isempty(integral_pos)
    integral_pos = zeros(3, 1);
    integral_att = zeros(3, 1);
end

% 提取状态变量
pos = x(1:3);           % 位置 [x; y; z]
vel = x(4:6);           % 速度 [vx; vy; vz]
euler = x(7:9);         % 欧拉角 [phi; theta; psi]
omega = x(10:12);       % 角速度 [p; q; r]

% 期望状态
pos_d = x_desired(1:3);
vel_d = x_desired(4:6);

% 位置和速度误差
error_pos = pos - pos_d;
error_vel = vel - vel_d;
error_pos_norm = norm(error_pos);

% 自适应增益 - 根据误差大小调整增益
if error_pos_norm > 1.0
    % 大误差时使用较高增益快速收敛
    k_pos = control_params.k_pos * 1.2;
    k_vel = control_params.k_vel * 1.2;
    k_int = 0.8;
elseif error_pos_norm > 0.3
    % 中等误差时使用标准增益
    k_pos = control_params.k_pos;
    k_vel = control_params.k_vel;
    k_int = 1.0;
else
    % 小误差时降低增益减少振荡
    k_pos = control_params.k_pos * 0.8;
    k_vel = control_params.k_vel * 1.1;  % 增加阻尼
    k_int = 0.6;
end

% 智能积分控制 - 只在小误差时使用积分
dt = 0.01;
if error_pos_norm < 0.5
    integral_pos = integral_pos + error_pos * dt;
else
    integral_pos = integral_pos * 0.95;  % 大误差时衰减积分项
end

% 积分抗饱和
max_integral = 0.2;
integral_pos = max(-max_integral, min(max_integral, integral_pos));

% 位置控制律 - 改进的PD+I控制
acc_cmd = -k_pos * error_pos - k_vel * error_vel - k_int * integral_pos;

% 推力计算
phi = euler(1); theta = euler(2);
T = control_params.mass * (acc_cmd(3) + control_params.gravity) / (cos(phi) * cos(theta));

% 推力限幅
T = max(control_params.T_min, min(control_params.T_max, T));

% 期望姿态计算
phi_d = (acc_cmd(1) * sin(euler(3)) - acc_cmd(2) * cos(euler(3))) / control_params.gravity;
theta_d = (acc_cmd(1) * cos(euler(3)) + acc_cmd(2) * sin(euler(3))) / control_params.gravity;
psi_d = 0; % 偏航角保持为0

% 姿态角限制
max_angle = pi/6;  % 30度限制
phi_d = max(-max_angle, min(max_angle, phi_d));
theta_d = max(-max_angle, min(max_angle, theta_d));

% 姿态误差
euler_d = [phi_d; theta_d; psi_d];
error_att = euler - euler_d;
error_att_norm = norm(error_att);

% 自适应姿态增益
if error_att_norm > 0.3
    k_att = control_params.k_att * 1.1;
    k_omega = control_params.k_omega * 1.1;
    k_int_att = 0.3;
else
    k_att = control_params.k_att;
    k_omega = control_params.k_omega * 1.2;  % 增加阻尼
    k_int_att = 0.2;
end

% 姿态积分项
if error_att_norm < 0.2
    integral_att = integral_att + error_att * dt;
else
    integral_att = integral_att * 0.95;
end

% 积分抗饱和
max_integral_att = 0.1;
integral_att = max(-max_integral_att, min(max_integral_att, integral_att));

% 姿态控制律
omega_d = zeros(3, 1); % 期望角速度为0
error_omega = omega - omega_d;

tau = -k_att * error_att - k_omega * error_omega - k_int_att * integral_att;

% 力矩限幅
tau = max(-control_params.tau_max, min(control_params.tau_max, tau));

% 输出控制量
u = [T; tau];

end
