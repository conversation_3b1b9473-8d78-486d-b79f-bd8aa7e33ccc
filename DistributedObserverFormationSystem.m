function DistributedObserverFormationSystem()
% 分布式观测器编队控制系统 - 解决振荡问题的改进版本
% 结合分布式观测器和平滑收敛控制技术

clear; clc; close all;

fprintf('=== 分布式观测器编队控制系统 ===\n');
fprintf('🎯 分布式状态观测器\n');
fprintf('🎯 一致性编队控制\n');
fprintf('🎯 解决振荡问题\n');
fprintf('🎯 中文可视化\n');
fprintf('开始仿真...\n\n');

%% 仿真参数设置
dt = 0.01;              % 仿真步长 (s)
T_sim = 50;             % 仿真时间 (s)
t_span = 0:dt:T_sim;    % 时间向量
N_agents = 4;           % 无人机数量

%% 无人机物理参数
quad_params = struct();
quad_params.mass = 1.2;         % 质量 (kg)
quad_params.gravity = 9.81;     % 重力加速度 (m/s^2)
quad_params.Ixx = 0.0347;       % x轴转动惯量 (kg*m^2)
quad_params.Iyy = 0.0347;       % y轴转动惯量 (kg*m^2)
quad_params.Izz = 0.0617;       % z轴转动惯量 (kg*m^2)

%% 改进的控制参数 - 针对振荡问题优化
control_params = struct();
control_params.k_pos = 2.0;         % 位置控制增益 (保守设置)
control_params.k_vel = 2.5;         % 速度控制增益 (增加阻尼)
control_params.k_att = 2.5;         % 姿态控制增益
control_params.k_omega = 1.8;       % 角速度控制增益
control_params.k_consensus = 0.3;   % 一致性增益 (降低避免振荡)
control_params.mass = quad_params.mass;
control_params.gravity = quad_params.gravity;
control_params.T_max = 20;          % 最大推力 (N)
control_params.T_min = 0;           % 最小推力 (N)
control_params.tau_max = 2.5;       % 最大力矩 (N*m)

%% 分布式观测器参数
observer_params = struct();
% 线性化系统矩阵 (悬停点附近)
observer_params.A = zeros(12, 12);
observer_params.A(1:3, 4:6) = eye(3);      % 位置-速度关系
observer_params.A(7:9, 10:12) = eye(3);    % 姿态-角速度关系

observer_params.B = zeros(12, 4);           % 输入矩阵 (简化)
observer_params.C = [eye(3), zeros(3, 9);  % 位置测量
                     zeros(3, 6), eye(3), zeros(3, 3)]; % 姿态测量

% 观测器增益矩阵 - 保守设计避免振荡
observer_params.observer_gain = 1.5 * eye(12, 6);  % 降低增益
observer_params.k_consensus_obs = 0.2;              % 降低一致性增益

%% 通信拓扑 (邻接矩阵)
neighbors = [0, 1, 1, 0;    % 无人机1与2,3连接
             1, 0, 1, 1;    % 无人机2与1,3,4连接
             1, 1, 0, 1;    % 无人机3与1,2,4连接
             0, 1, 1, 0];   % 无人机4与2,3连接

%% 初始状态设置
% 真实状态初始化
x_agents = zeros(12, N_agents);
% 位置初始化 (四个角落)
x_agents(1:3, 1) = [0; 0; 0];      % 无人机1
x_agents(1:3, 2) = [2; 0; 0];      % 无人机2
x_agents(1:3, 3) = [2; 2; 0];      % 无人机3
x_agents(1:3, 4) = [0; 2; 0];      % 无人机4

% 状态估计初始化 (添加小的初始估计误差)
x_hat_agents = x_agents + 0.05 * randn(12, N_agents);

%% 期望编队形状 (正方形编队，边长2m，高度5m)
formation_center = [5; 5; 5];  % 编队中心
formation_size = 2;            % 编队尺寸

x_desired = zeros(12, N_agents);
% 期望位置 (正方形编队)
x_desired(1:3, 1) = formation_center + [-formation_size/2; -formation_size/2; 0];
x_desired(1:3, 2) = formation_center + [formation_size/2; -formation_size/2; 0];
x_desired(1:3, 3) = formation_center + [formation_size/2; formation_size/2; 0];
x_desired(1:3, 4) = formation_center + [-formation_size/2; formation_size/2; 0];

%% 数据存储
x_history = zeros(12, N_agents, length(t_span));
x_hat_history = zeros(12, N_agents, length(t_span));
u_history = zeros(4, N_agents, length(t_span));
estimation_error = zeros(12, N_agents, length(t_span));
tracking_error_pos = zeros(3, N_agents, length(t_span));
tracking_error_vel = zeros(3, N_agents, length(t_span));

%% 主仿真循环
fprintf('仿真进行中...\n');
for k = 1:length(t_span)
    t = t_span(k);
    
    % 生成测量数据 (位置和姿态，添加测量噪声)
    y_agents = zeros(6, N_agents);
    for i = 1:N_agents
        % 位置测量 (GPS等)
        y_agents(1:3, i) = x_agents(1:3, i) + 0.005 * randn(3, 1);  % 减少噪声
        % 姿态测量 (IMU等)
        y_agents(4:6, i) = x_agents(7:9, i) + 0.002 * randn(3, 1);  % 减少噪声
    end
    
    % 分布式观测器更新
    x_hat_dot_agents = zeros(12, N_agents);
    for i = 1:N_agents
        [x_hat_dot_agents(:, i), ~] = DistributedObserver(i, x_agents, y_agents, ...
                                                          x_hat_agents, neighbors, observer_params);
    end
    
    % 更新状态估计 (欧拉积分)
    x_hat_agents = x_hat_agents + dt * x_hat_dot_agents;
    
    % 改进的分布式控制器 - 使用平滑收敛技术
    u_agents = zeros(4, N_agents);
    for i = 1:N_agents
        % 使用状态估计进行控制
        u_agents(:, i) = ImprovedDistributedController(i, x_hat_agents, x_desired, neighbors, control_params, t);
    end
    
    % 无人机动力学更新
    x_dot_agents = zeros(12, N_agents);
    for i = 1:N_agents
        x_dot_agents(:, i) = QuadrotorDynamics(t, x_agents(:, i), u_agents(:, i), quad_params);
    end
    
    % 更新真实状态 (欧拉积分)
    x_agents = x_agents + dt * x_dot_agents;
    
    % 计算跟踪误差
    for i = 1:N_agents
        tracking_error_pos(:, i, k) = x_agents(1:3, i) - x_desired(1:3, i);
        tracking_error_vel(:, i, k) = x_agents(4:6, i) - x_desired(4:6, i);
    end
    
    % 存储数据
    x_history(:, :, k) = x_agents;
    x_hat_history(:, :, k) = x_hat_agents;
    u_history(:, :, k) = u_agents;
    estimation_error(:, :, k) = x_agents - x_hat_agents;
    
    % 显示进度
    if mod(k, 1000) == 0
        fprintf('仿真进度: %.1f%%\n', k/length(t_span)*100);
    end
end

fprintf('仿真完成！\n\n');

%% 性能分析
fprintf('=== 分布式观测器系统性能分析 ===\n');

% 计算跟踪误差统计
pos_errors = zeros(N_agents, length(t_span));
vel_errors = zeros(N_agents, length(t_span));

for i = 1:N_agents
    for k = 1:length(t_span)
        pos_errors(i, k) = norm(tracking_error_pos(:, i, k));
        vel_errors(i, k) = norm(tracking_error_vel(:, i, k));
    end
end

max_pos_error = max(pos_errors(:));
max_vel_error = max(vel_errors(:));

fprintf('最大位置跟踪误差: %.3f m\n', max_pos_error);
fprintf('最大速度跟踪误差: %.3f m/s\n', max_vel_error);

% 15秒后的稳态性能
steady_start = find(t_span >= 15, 1);
if ~isempty(steady_start)
    steady_pos_error = mean(pos_errors(:, steady_start:end), 'all');
    steady_vel_error = mean(vel_errors(:, steady_start:end), 'all');
    fprintf('15秒后平均位置误差: %.3f m\n', steady_pos_error);
    fprintf('15秒后平均速度误差: %.3f m/s\n', steady_vel_error);
end

% 40-45秒关键时段分析
t40_idx = find(t_span >= 40, 1);
t45_idx = find(t_span >= 45, 1);
if ~isempty(t40_idx) && ~isempty(t45_idx)
    critical_pos_error = max(pos_errors(:, t40_idx:t45_idx), [], 'all');
    critical_vel_error = max(vel_errors(:, t40_idx:t45_idx), [], 'all');
    fprintf('\n=== 40-45秒关键时段分析 ===\n');
    fprintf('最大位置误差: %.3f m\n', critical_pos_error);
    fprintf('最大速度误差: %.3f m/s\n', critical_vel_error);
end

%% 结果可视化
PlotDistributedObserverResults(t_span, x_history, x_hat_history, x_desired, ...
                               estimation_error, u_history, tracking_error_pos, tracking_error_vel, pos_errors, vel_errors);

fprintf('\n🎉 分布式观测器编队控制系统测试完成！\n');
fprintf('✅ 分布式状态观测器\n');
fprintf('✅ 一致性编队控制\n');
fprintf('✅ 振荡问题优化\n');
fprintf('✅ 中文可视化显示\n');

end
