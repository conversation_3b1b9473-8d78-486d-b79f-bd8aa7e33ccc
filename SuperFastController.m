function u = SuperFastController(x, x_desired, control_params)
% 超快速收敛控制器 - 解决振荡问题，实现5-15秒内真正收敛到零
% 使用自适应增益和振荡抑制技术

persistent integral_pos integral_att prev_error_pos prev_error_att
persistent oscillation_counter damping_active

% 初始化持久变量
if isempty(integral_pos)
    integral_pos = zeros(3, 1);
    integral_att = zeros(3, 1);
    prev_error_pos = zeros(3, 1);
    prev_error_att = zeros(3, 1);
    oscillation_counter = 0;
    damping_active = false;
end

% 提取状态变量
pos = x(1:3);           % 位置 [x; y; z]
vel = x(4:6);           % 速度 [vx; vy; vz]
euler = x(7:9);         % 欧拉角 [phi; theta; psi]
omega = x(10:12);       % 角速度 [p; q; r]

% 期望状态
pos_d = x_desired(1:3);
vel_d = x_desired(4:6);
acc_d = x_desired(7:9);

% 位置和速度误差
error_pos = pos - pos_d;
error_vel = vel - vel_d;
error_pos_norm = norm(error_pos);
error_vel_norm = norm(error_vel);

% 振荡检测
error_change = error_pos - prev_error_pos;
if norm(error_change) > 0.01 && error_pos_norm < 1.0
    oscillation_counter = oscillation_counter + 1;
else
    oscillation_counter = max(0, oscillation_counter - 1);
end

% 激活阻尼模式
if oscillation_counter > 50  % 检测到持续振荡
    damping_active = true;
elseif error_pos_norm < 0.05 && error_vel_norm < 0.05
    damping_active = false;
    oscillation_counter = 0;
end

% 自适应增益设计
if error_pos_norm > 2.0
    % 大误差阶段 - 快速响应
    k_pos = 5.0;
    k_vel = 4.0;
    k_int_pos = 0.5;
elseif error_pos_norm > 0.5
    % 中等误差阶段 - 平衡快速性和稳定性
    k_pos = 4.0;
    k_vel = 3.2;
    k_int_pos = 1.2;
elseif error_pos_norm > 0.1
    % 小误差阶段 - 精确控制
    k_pos = 3.0;
    k_vel = 2.5;
    k_int_pos = 1.5;
else
    % 微小误差阶段 - 高精度保持
    k_pos = 2.0;
    k_vel = 2.0;
    k_int_pos = 0.8;
end

% 振荡抑制增益调整
if damping_active
    k_pos = k_pos * 0.7;  % 降低比例增益
    k_vel = k_vel * 1.5;  % 增加阻尼
    k_int_pos = k_int_pos * 0.5;  % 减少积分增益
end

% 智能积分控制
dt = 0.01;
if error_pos_norm < 0.8 && ~damping_active
    integral_pos = integral_pos + error_pos * dt;
else
    integral_pos = integral_pos * 0.95;  % 衰减积分项
end

% 积分抗饱和
max_integral = 0.3;
integral_pos = max(-max_integral, min(max_integral, integral_pos));

% 微分项计算 (用于阻尼)
derivative_pos = (error_pos - prev_error_pos) / dt;
if damping_active
    k_der_pos = 0.8;
else
    k_der_pos = 0.3;
end

% 位置控制律 - 自适应PID控制
acc_cmd = -k_pos * error_pos - k_vel * error_vel - k_int_pos * integral_pos - k_der_pos * derivative_pos + acc_d;

% 推力计算
phi = euler(1); theta = euler(2);
cos_phi = cos(phi); cos_theta = cos(theta);

% 避免除零
if abs(cos_phi * cos_theta) < 0.1
    cos_phi = sign(cos_phi) * 0.1;
    cos_theta = sign(cos_theta) * 0.1;
end

T = control_params.mass * (acc_cmd(3) + control_params.gravity) / (cos_phi * cos_theta);

% 推力限幅
T = max(control_params.T_min, min(control_params.T_max, T));

% 期望姿态计算
g = control_params.gravity;
phi_d = (acc_cmd(1) * sin(euler(3)) - acc_cmd(2) * cos(euler(3))) / g;
theta_d = (acc_cmd(1) * cos(euler(3)) + acc_cmd(2) * sin(euler(3))) / g;
psi_d = 0;

% 姿态角限制
max_angle = pi/6;  % 30度限制
phi_d = max(-max_angle, min(max_angle, phi_d));
theta_d = max(-max_angle, min(max_angle, theta_d));

% 姿态误差
euler_d = [phi_d; theta_d; psi_d];
error_att = euler - euler_d;
error_att_norm = norm(error_att);

% 自适应姿态增益
if error_att_norm > 0.5
    k_att = 6.0;
    k_omega = 3.5;
    k_int_att = 0.3;
elseif error_att_norm > 0.1
    k_att = 5.0;
    k_omega = 3.0;
    k_int_att = 0.5;
else
    k_att = 4.0;
    k_omega = 2.5;
    k_int_att = 0.3;
end

% 姿态振荡抑制
if damping_active
    k_att = k_att * 0.8;
    k_omega = k_omega * 1.3;
end

% 姿态积分项
if error_att_norm < 0.3 && ~damping_active
    integral_att = integral_att + error_att * dt;
else
    integral_att = integral_att * 0.95;
end

% 积分抗饱和
max_integral_att = 0.15;
integral_att = max(-max_integral_att, min(max_integral_att, integral_att));

% 姿态微分项
derivative_att = (error_att - prev_error_att) / dt;
if damping_active
    k_der_att = 0.5;
else
    k_der_att = 0.2;
end

% 姿态控制律
omega_d = zeros(3, 1);
error_omega = omega - omega_d;

tau = -k_att * error_att - k_omega * error_omega - k_int_att * integral_att - k_der_att * derivative_att;

% 力矩限幅
tau = max(-control_params.tau_max, min(control_params.tau_max, tau));

% 更新历史误差
prev_error_pos = error_pos;
prev_error_att = error_att;

% 输出控制量
u = [T; tau];

% 调试信息
persistent debug_counter
if isempty(debug_counter)
    debug_counter = 0;
end
debug_counter = debug_counter + 1;

if mod(debug_counter, 500) == 0  % 每5秒输出一次
    if damping_active
        damping_status = '激活';
    else
        damping_status = '关闭';
    end
    fprintf('超快控制器 - 位置误差: %.4f m, 速度误差: %.4f m/s, 振荡抑制: %s\n', ...
            error_pos_norm, error_vel_norm, damping_status);
end

end
