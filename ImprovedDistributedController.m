function [u] = ImprovedDistributedController(agent_id, x_agents, x_desired, neighbors, params, t)
% 改进的分布式控制器 - 解决振荡问题
% 结合分布式一致性算法和平滑收敛技术

persistent integral_pos integral_att controller_time error_history

% 初始化持久变量
if isempty(integral_pos)
    integral_pos = zeros(3, 4);  % 为4个无人机分别存储积分项
    integral_att = zeros(3, 4);
    controller_time = zeros(1, 4);
    error_history = zeros(3, 10, 4);  % 每个无人机保存10个历史误差
end

% 更新控制器时间
dt = 0.01;
controller_time(agent_id) = controller_time(agent_id) + dt;

% 获取当前无人机状态
x_i = x_agents(:, agent_id);
x_d_i = x_desired(:, agent_id);

% 提取状态变量
pos_i = x_i(1:3);          % 当前位置
vel_i = x_i(4:6);          % 当前速度
euler_i = x_i(7:9);        % 当前欧拉角
omega_i = x_i(10:12);      % 当前角速度

% 期望状态
pos_d_i = x_d_i(1:3);      % 期望位置
vel_d_i = x_d_i(4:6);      % 期望速度 (通常为0)
euler_d_i = x_d_i(7:9);    % 期望欧拉角 (通常为0)
omega_d_i = x_d_i(10:12);  % 期望角速度 (通常为0)

% 基本跟踪误差
error_pos = pos_i - pos_d_i;
error_vel = vel_i - vel_d_i;
error_pos_norm = norm(error_pos);

% 更新误差历史
error_history(:, :, agent_id) = [error_history(:, 2:end, agent_id), error_pos];

% 振荡检测
oscillation_detected = false;
if controller_time(agent_id) > 5.0
    sign_changes = 0;
    for dim = 1:3  % 检查x,y,z三个方向
        for j = 2:10
            if sign(error_history(dim, j, agent_id)) ~= sign(error_history(dim, j-1, agent_id))
                sign_changes = sign_changes + 1;
            end
        end
    end
    if sign_changes > 12  % 如果符号变化太频繁
        oscillation_detected = true;
    end
end

%% 分布式一致性项计算
N = size(x_agents, 2);
consensus_pos = zeros(3, 1);
consensus_vel = zeros(3, 1);
consensus_att = zeros(3, 1);

% 计算与邻居的一致性误差
for j = 1:N
    if neighbors(agent_id, j) == 1 && j ~= agent_id
        % 位置一致性 - 相对编队误差
        relative_pos = (pos_i - pos_d_i) - (x_agents(1:3, j) - x_desired(1:3, j));
        consensus_pos = consensus_pos + relative_pos;
        
        % 速度一致性
        relative_vel = (vel_i - vel_d_i) - (x_agents(4:6, j) - x_desired(4:6, j));
        consensus_vel = consensus_vel + relative_vel;
        
        % 姿态一致性
        relative_att = (euler_i - euler_d_i) - (x_agents(7:9, j) - x_desired(7:9, j));
        consensus_att = consensus_att + relative_att;
    end
end

%% 自适应增益调度
% 基础增益
k_pos_base = params.k_pos;
k_vel_base = params.k_vel;
k_consensus_base = params.k_consensus;

% 时间分段增益调度
if controller_time(agent_id) <= 8.0
    % 0-8秒：稳定收敛
    gain_mult = 1.0;
elseif controller_time(agent_id) <= 15.0
    % 8-15秒：逐步增强
    progress = (controller_time(agent_id) - 8.0) / 7.0;
    gain_mult = 1.0 + 0.15 * progress;  % 从1.0增加到1.15
else
    % 15秒后：精确控制
    gain_mult = 1.2;
end

% 振荡抑制策略
if oscillation_detected
    % 检测到振荡时，降低比例增益，增加阻尼
    k_pos = k_pos_base * 0.7 * gain_mult;
    k_vel = k_vel_base * 1.6 * gain_mult;  % 增加阻尼
    k_consensus = k_consensus_base * 0.4;   % 降低一致性增益
    k_int = 0.3;  % 减少积分
else
    % 正常情况下的增益调整
    if error_pos_norm > 0.8
        k_pos = k_pos_base * 1.0 * gain_mult;
        k_vel = k_vel_base * 1.0 * gain_mult;
        k_consensus = k_consensus_base * 0.8;
        k_int = 0.5;
    elseif error_pos_norm > 0.3
        k_pos = k_pos_base * 1.1 * gain_mult;
        k_vel = k_vel_base * 1.3 * gain_mult;
        k_consensus = k_consensus_base * 1.0;
        k_int = 0.8;
    else
        % 小误差时重点增加阻尼
        k_pos = k_pos_base * 1.0 * gain_mult;
        k_vel = k_vel_base * 1.5 * gain_mult;  % 强阻尼
        k_consensus = k_consensus_base * 1.2;
        k_int = 1.0;
    end
end

%% 积分控制
if controller_time(agent_id) > 8.0 && error_pos_norm < 1.0 && ~oscillation_detected
    integral_pos(:, agent_id) = integral_pos(:, agent_id) + error_pos * dt;
else
    integral_pos(:, agent_id) = integral_pos(:, agent_id) * 0.95;  % 缓慢衰减
end

% 积分限制
if oscillation_detected
    max_integral = 0.15;  % 振荡时严格限制
elseif controller_time(agent_id) > 12.0
    max_integral = 0.4;   % 12秒后适度放宽
else
    max_integral = 0.25;
end

integral_pos(:, agent_id) = max(-max_integral, min(max_integral, integral_pos(:, agent_id)));

%% 位置控制律 (分布式PID + 一致性)
acc_cmd = -k_pos * error_pos - k_vel * error_vel - k_int * integral_pos(:, agent_id) - k_consensus * consensus_pos;

%% 推力计算
phi = euler_i(1); theta = euler_i(2);
cos_phi = cos(phi); cos_theta = cos(theta);

% 避免除零
if abs(cos_phi * cos_theta) < 0.1
    cos_phi = sign(cos_phi) * 0.1;
    cos_theta = sign(cos_theta) * 0.1;
end

T = params.mass * (acc_cmd(3) + params.gravity) / (cos_phi * cos_theta);

% 推力限幅
T = max(params.T_min, min(params.T_max, T));

%% 期望姿态计算
g = params.gravity;
phi_d = (acc_cmd(1) * sin(euler_i(3)) - acc_cmd(2) * cos(euler_i(3))) / g;
theta_d = (acc_cmd(1) * cos(euler_i(3)) + acc_cmd(2) * sin(euler_i(3))) / g;
psi_d = 0;

% 姿态角限制
max_angle = pi/6;  % 30度限制
phi_d = max(-max_angle, min(max_angle, phi_d));
theta_d = max(-max_angle, min(max_angle, theta_d));

%% 姿态控制
euler_d = [phi_d; theta_d; psi_d];
error_att = euler_i - euler_d;
error_omega = omega_i - omega_d_i;

% 姿态控制增益
if oscillation_detected
    k_att = params.k_att * 0.8;
    k_omega = params.k_omega * 1.4;
    k_int_att = 0.2;
else
    k_att = params.k_att * 1.0;
    k_omega = params.k_omega * 1.2;
    k_int_att = 0.4;
end

% 姿态积分项
error_att_norm = norm(error_att);
if controller_time(agent_id) > 10.0 && error_att_norm < 0.2 && ~oscillation_detected
    integral_att(:, agent_id) = integral_att(:, agent_id) + error_att * dt;
else
    integral_att(:, agent_id) = integral_att(:, agent_id) * 0.98;
end

% 姿态积分限制
if oscillation_detected
    max_integral_att = 0.08;
else
    max_integral_att = 0.15;
end
integral_att(:, agent_id) = max(-max_integral_att, min(max_integral_att, integral_att(:, agent_id)));

% 姿态控制律 (分布式PID + 一致性)
tau = -k_att * error_att - k_omega * error_omega - k_int_att * integral_att(:, agent_id) - k_consensus * 0.5 * consensus_att;

% 力矩限幅
tau = max(-params.tau_max, min(params.tau_max, tau));

%% 输出控制量
u = [T; tau];

%% 调试信息
persistent debug_counter
if isempty(debug_counter)
    debug_counter = zeros(1, 4);
end
debug_counter(agent_id) = debug_counter(agent_id) + 1;

if mod(debug_counter(agent_id), 500) == 0  % 每5秒输出一次
    if oscillation_detected
        osc_status = '振荡检测';
    else
        osc_status = '正常';
    end
    fprintf('分布式控制器[UAV%d] [%.1fs] - 位置误差: %.4f m, 速度误差: %.4f m/s, 状态: %s\n', ...
            agent_id, controller_time(agent_id), error_pos_norm, norm(error_vel), osc_status);
end

end
