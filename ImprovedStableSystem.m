function ImprovedStableSystem()
% 改进稳定系统 - 基于稳定系统，适度改进收敛速度，消除振荡
% 保持系统稳定性的前提下实现更好的收敛性能

clear; clc; close all;

fprintf('=== 抗振荡编队控制系统 ===\n');
fprintf('🎯 消除振荡，实现平滑收敛\n');
fprintf('🎯 低通滤波 + 自适应阻尼\n');
fprintf('🎯 保守PID控制\n');
fprintf('🎯 增强编队队形显示\n');
fprintf('开始仿真...\n\n');

%% 仿真参数设置
dt = 0.01;              % 仿真步长 (s)
T_sim = 50;             % 仿真时间 (s)
t_span = 0:dt:T_sim;    % 时间向量
N_followers = 4;        % 跟随者数量
N_total = N_followers + 1; % 总无人机数量

%% 无人机物理参数
quad_params = struct();
quad_params.mass = 1.2;         % 质量 (kg)
quad_params.gravity = 9.81;     % 重力加速度 (m/s^2)
quad_params.Ixx = 0.0347;       % x轴转动惯量 (kg*m^2)
quad_params.Iyy = 0.0347;       % y轴转动惯量 (kg*m^2)
quad_params.Izz = 0.0617;       % z轴转动惯量 (kg*m^2)

%% 抗振荡控制参数 - 保守设置消除振荡
control_params = struct();
control_params.k_pos = 2.5;         % 位置控制增益 (保守设置)
control_params.k_vel = 2.0;         % 速度控制增益 (保守设置)
control_params.k_att = 3.0;         % 姿态控制增益 (保守设置)
control_params.k_omega = 1.8;       % 角速度控制增益 (保守设置)
control_params.mass = quad_params.mass;
control_params.gravity = quad_params.gravity;
control_params.T_max = 22;          % 保守的最大推力
control_params.T_min = 0;           % 最小推力 (N)
control_params.tau_max = 2.5;       % 保守的最大力矩

%% 编队参数
formation_offset = [
    -2.0, -2.0, 0;    % UAV_1: 左后方
     2.0, -2.0, 0;    % UAV_2: 右后方
    -2.0,  2.0, 0;    % UAV_3: 左前方
     2.0,  2.0, 0     % UAV_4: 右前方
];
formation_offset = formation_offset';  % 转置为 (3x4)

%% 初始状态设置
x_agents = zeros(12, N_total);

% 领导者初始位置（轨迹中心）
x_agents(1:3, 1) = [8; 8; 12];

% 跟随者初始位置
for i = 1:N_followers
    x_agents(1:3, i+1) = x_agents(1:3, 1) + formation_offset(:, i);
end

%% 数据存储
x_history = zeros(12, N_total, length(t_span));
x_desired_history = zeros(12, N_total, length(t_span));
u_history = zeros(4, N_total, length(t_span));
tracking_error_pos = zeros(3, N_followers, length(t_span));
tracking_error_vel = zeros(3, N_followers, length(t_span));

%% 主仿真循环
fprintf('仿真进行中...\n');
for k = 1:length(t_span)
    t = t_span(k);
    
    %% 领导者轨迹生成
    [leader_pos, leader_vel, leader_acc] = GenerateLeaderTrajectory(t);
    
    % 领导者期望状态
    x_desired_leader = zeros(12, 1);
    x_desired_leader(1:3) = leader_pos;
    x_desired_leader(4:6) = leader_vel;
    
    %% 跟随者期望轨迹计算
    x_desired_followers = zeros(12, N_followers);
    for i = 1:N_followers
        x_desired_followers(1:3, i) = leader_pos + formation_offset(:, i);
        x_desired_followers(4:6, i) = leader_vel;
    end
    
    %% 控制器 - 使用抗振荡控制器
    % 领导者控制
    u_leader = AntiOscillationController(x_agents(:, 1), x_desired_leader, control_params);

    % 跟随者控制
    u_followers = zeros(4, N_followers);
    for i = 1:N_followers
        u_followers(:, i) = AntiOscillationController(x_agents(:, i+1), ...
                                                      x_desired_followers(:, i), ...
                                                      control_params);
    end
    
    %% 动力学更新
    % 领导者
    x_dot_leader = QuadrotorDynamics(t, x_agents(:, 1), u_leader, quad_params);
    x_agents(:, 1) = x_agents(:, 1) + dt * x_dot_leader;
    
    % 跟随者
    for i = 1:N_followers
        x_dot_follower = QuadrotorDynamics(t, x_agents(:, i+1), u_followers(:, i), quad_params);
        x_agents(:, i+1) = x_agents(:, i+1) + dt * x_dot_follower;
    end
    
    %% 跟踪误差计算
    for i = 1:N_followers
        tracking_error_pos(:, i, k) = x_agents(1:3, i+1) - x_desired_followers(1:3, i);
        tracking_error_vel(:, i, k) = x_agents(4:6, i+1) - x_desired_followers(4:6, i);
    end
    
    %% 数据存储
    x_history(:, :, k) = x_agents;
    x_desired_history(:, 1, k) = x_desired_leader;
    x_desired_history(:, 2:end, k) = x_desired_followers;
    u_history(:, 1, k) = u_leader;
    u_history(:, 2:end, k) = u_followers;
    
    % 显示进度
    if mod(k, 1000) == 0
        fprintf('仿真进度: %.1f%%\n', k/length(t_span)*100);
    end
end

fprintf('仿真完成！\n');

%% 性能分析
pos_error_norms = zeros(N_followers, length(t_span));
vel_error_norms = zeros(N_followers, length(t_span));

for i = 1:N_followers
    for k = 1:length(t_span)
        pos_error_norms(i, k) = norm(tracking_error_pos(:, i, k));
        vel_error_norms(i, k) = norm(tracking_error_vel(:, i, k));
    end
end

% 关键时间点分析
idx_5 = find(t_span >= 5, 1);
idx_10 = find(t_span >= 10, 1);
idx_15 = find(t_span >= 15, 1);
idx_15_end = find(t_span >= 15);
idx_40_45 = find(t_span >= 40 & t_span <= 45);

% 性能指标计算
max_pos_error = max(pos_error_norms(:));
max_vel_error = max(vel_error_norms(:));

pos_error_at_5s = max(pos_error_norms(:, idx_5));
vel_error_at_5s = max(vel_error_norms(:, idx_5));
pos_error_at_10s = max(pos_error_norms(:, idx_10));
vel_error_at_10s = max(vel_error_norms(:, idx_10));
pos_error_at_15s = max(pos_error_norms(:, idx_15));
vel_error_at_15s = max(vel_error_norms(:, idx_15));

steady_pos_error = mean(pos_error_norms(:, idx_15_end), 'all');
steady_vel_error = mean(vel_error_norms(:, idx_15_end), 'all');

max_pos_40_45 = max(pos_error_norms(:, idx_40_45), [], 'all');
max_vel_40_45 = max(vel_error_norms(:, idx_40_45), [], 'all');
avg_pos_40_45 = mean(pos_error_norms(:, idx_40_45), 'all');
avg_vel_40_45 = mean(vel_error_norms(:, idx_40_45), 'all');

fprintf('\n=== 改进稳定系统性能分析 ===\n');
fprintf('最大位置跟踪误差: %.3f m\n', max_pos_error);
fprintf('最大速度跟踪误差: %.3f m/s\n', max_vel_error);

fprintf('\n=== 关键时间点误差 ===\n');
fprintf('5秒时误差:  位置=%.3f m, 速度=%.3f m/s\n', pos_error_at_5s, vel_error_at_5s);
fprintf('10秒时误差: 位置=%.3f m, 速度=%.3f m/s\n', pos_error_at_10s, vel_error_at_10s);
fprintf('15秒时误差: 位置=%.3f m, 速度=%.3f m/s\n', pos_error_at_15s, vel_error_at_15s);

fprintf('\n=== 15秒后稳态性能 ===\n');
fprintf('平均位置误差: %.3f m\n', steady_pos_error);
fprintf('平均速度误差: %.3f m/s\n', steady_vel_error);

fprintf('\n=== 40-45秒关键时段分析 ===\n');
fprintf('最大位置误差: %.3f m\n', max_pos_40_45);
fprintf('最大速度误差: %.3f m/s\n', max_vel_40_45);
fprintf('平均位置误差: %.3f m\n', avg_pos_40_45);
fprintf('平均速度误差: %.3f m/s\n', avg_vel_40_45);

%% 使用增强版可视化
fprintf('\n开始生成改进稳定系统图表...\n');
FixedVisualizationOnly(t_span, tracking_error_pos, tracking_error_vel, x_history, x_desired_history);

fprintf('\n🎉 抗振荡编队控制系统测试完成！\n');
fprintf('✅ 振荡消除技术\n');
fprintf('✅ 低通滤波平滑\n');
fprintf('✅ 保守PID控制\n');
fprintf('✅ 增强编队队形显示\n');

end
