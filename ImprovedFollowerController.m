function u = ImprovedFollowerController(x, x_desired, params)
% 改进的跟随者控制器 - 平衡性能和稳定性
% 输入:
%   x - 当前状态 [12x1]: [位置; 速度; 姿态角; 角速度]
%   x_desired - 期望状态 [12x1]
%   params - 控制参数结构体
% 输出:
%   u - 控制输入 [4x1]: [推力; 滚转力矩; 俯仰力矩; 偏航力矩]

% 提取状态变量
pos = x(1:3);           % 位置 [x, y, z]
vel = x(4:6);           % 速度 [vx, vy, vz]
att = x(7:9);           % 姿态角 [phi, theta, psi]
omega = x(10:12);       % 角速度 [p, q, r]

% 提取期望状态
pos_d = x_desired(1:3);
vel_d = x_desired(4:6);
att_d = x_desired(7:9);
omega_d = x_desired(10:12);

% 改进的控制参数 - 适度提高增益
k_pos = params.k_pos * 1.2;     % 位置增益适度提高
k_vel = params.k_vel * 1.1;     % 速度增益适度提高
k_att = params.k_att * 1.1;     % 姿态增益适度提高
k_omega = params.k_omega * 1.05; % 角速度增益轻微提高

% 简单积分控制以消除稳态误差
persistent pos_error_integral
if isempty(pos_error_integral)
    pos_error_integral = zeros(3, 1);
end

% 位置和速度误差
pos_error = pos_d - pos;
vel_error = vel_d - vel;

% 积分项更新（带抗饱和）
dt = 0.01; % 仿真步长
ki_pos = 0.05; % 保守的积分增益

% 积分抗饱和
max_integral = 1.0;
pos_error_integral = pos_error_integral + pos_error * dt;
pos_error_integral = max(-max_integral, min(max_integral, pos_error_integral));

% 位置控制器 (PI控制)
acc_desired = k_pos * pos_error + k_vel * vel_error + ki_pos * pos_error_integral;

% 期望推力 (考虑重力补偿)
mass = params.mass;
gravity = params.gravity;
T_desired = mass * (acc_desired(3) + gravity);

% 期望姿态角计算 (从期望加速度推导)
phi_desired = asin(max(-0.3, min(0.3, (acc_desired(1) * sin(att(3)) - acc_desired(2) * cos(att(3))) / gravity)));
theta_desired = atan2(acc_desired(1) * cos(att(3)) + acc_desired(2) * sin(att(3)), ...
                      acc_desired(3) + gravity);
psi_desired = att_d(3); % 期望偏航角

att_desired = [phi_desired; theta_desired; psi_desired];

% 姿态误差
att_error = att_desired - att;
omega_error = omega_d - omega;

% 姿态控制器 (PD控制)
tau_desired = k_att * att_error + k_omega * omega_error;

% 控制输入限幅
T_max = params.T_max;
T_min = params.T_min;
tau_max = params.tau_max;

T = max(T_min, min(T_max, T_desired));
tau = max(-tau_max, min(tau_max, tau_desired));

% 输出控制信号
u = [T; tau];

end
