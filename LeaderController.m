function u = LeaderController(x_current, x_desired, params)
% 领导者控制器 - 轨迹跟踪控制
% 输入参数:
%   x_current - 当前状态 [位置(3), 速度(3), 欧拉角(3), 角速度(3)]' (12x1)
%   x_desired - 期望状态 [位置(3), 速度(3), 欧拉角(3), 角速度(3)]' (12x1)
%   params - 控制参数结构体
% 输出:
%   u - 控制输入 [总推力, 滚转力矩, 俯仰力矩, 偏航力矩]' (4x1)

% 提取当前状态
pos = x_current(1:3);          % 当前位置
vel = x_current(4:6);          % 当前速度
euler = x_current(7:9);        % 当前欧拉角
omega = x_current(10:12);      % 当前角速度

% 提取期望状态
pos_d = x_desired(1:3);        % 期望位置
vel_d = x_desired(4:6);        % 期望速度
euler_d = x_desired(7:9);      % 期望欧拉角 (通常为0)
omega_d = x_desired(10:12);    % 期望角速度 (通常为0)

% 控制参数
k_pos = params.k_pos;          % 位置控制增益
k_vel = params.k_vel;          % 速度控制增益
k_att = params.k_att;          % 姿态控制增益
k_omega = params.k_omega;      % 角速度控制增益
m = params.mass;               % 质量
g = params.gravity;            % 重力加速度

%% 位置控制器
% 位置误差和速度误差
pos_error = pos - pos_d;
vel_error = vel - vel_d;

% 期望加速度 (PD控制)
acc_desired = -k_pos * pos_error - k_vel * vel_error;

%% 姿态解算
% 当前姿态角
phi = euler(1);    % 滚转角
theta = euler(2);  % 俯仰角
psi = euler(3);    % 偏航角

% 期望推力计算
% 考虑重力补偿和期望加速度
F_desired = m * (acc_desired + [0; 0; g]);

% 总推力 (机体z轴方向)
cos_phi_theta = cos(phi) * cos(theta);
if abs(cos_phi_theta) < 0.1
    cos_phi_theta = sign(cos_phi_theta) * 0.1;  % 避免除零
end
T_desired = norm(F_desired) / cos_phi_theta;

% 期望姿态角计算
% 限制水平加速度以避免过大倾斜
acc_horizontal = sqrt(acc_desired(1)^2 + acc_desired(2)^2);
max_tilt_acc = 0.7 * g;  % 最大倾斜对应的加速度
if acc_horizontal > max_tilt_acc
    acc_desired(1:2) = acc_desired(1:2) * max_tilt_acc / acc_horizontal;
end

% 期望滚转角和俯仰角
phi_desired = asin(max(-0.7, min(0.7, (acc_desired(1) * sin(psi) - acc_desired(2) * cos(psi)) / g)));
theta_desired = atan2(acc_desired(1) * cos(psi) + acc_desired(2) * sin(psi), acc_desired(3) + g);

%% 姿态控制器
% 姿态误差
att_error = euler - [phi_desired; theta_desired; euler_d(3)];
omega_error = omega - omega_d;

% 期望力矩 (PD控制)
tau_desired = -k_att * att_error - k_omega * omega_error;

%% 控制输入限制
T_max = params.T_max;
T_min = params.T_min;
tau_max = params.tau_max;

% 推力限制
T = max(T_min, min(T_max, T_desired));

% 力矩限制
tau_phi = max(-tau_max, min(tau_max, tau_desired(1)));
tau_theta = max(-tau_max, min(tau_max, tau_desired(2)));
tau_psi = max(-tau_max, min(tau_max, tau_desired(3)));

% 检查NaN值
if isnan(T)
    T = m * g;  % 悬停推力
end
if any(isnan([tau_phi, tau_theta, tau_psi]))
    tau_phi = 0; tau_theta = 0; tau_psi = 0;
end

%% 输出控制信号
u = [T; tau_phi; tau_theta; tau_psi];

end
