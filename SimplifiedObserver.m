function x_hat_new = SimplifiedObserver(agent_id, x_agents, x_hat_agents, neighbors, params, dt)
% 简化分布式观测器
% 专门为稳定性设计的保守观测器
% 输入参数:
%   agent_id - 当前无人机ID
%   x_agents - 所有无人机真实状态矩阵 (12 x N) [仅用于生成测量]
%   x_hat_agents - 所有无人机状态估计矩阵 (12 x N)
%   neighbors - 邻接矩阵，表示通信拓扑 (N x N)
%   params - 观测器参数结构体
%   dt - 时间步长
% 输出:
%   x_hat_new - 更新后的状态估计 (12x1)

% 获取当前状态估计
x_hat_i = x_hat_agents(:, agent_id);

% 生成带噪声的测量 (模拟传感器)
% 假设可以测量位置和姿态
y_pos = x_agents(1:3, agent_id) + params.noise_std * randn(3, 1);  % 位置测量
y_att = x_agents(7:9, agent_id) + params.noise_std * 0.5 * randn(3, 1);  % 姿态测量

%% 局部观测器更新 (基于测量的状态校正)
% 位置估计校正
pos_error = y_pos - x_hat_i(1:3);
x_hat_i(1:3) = x_hat_i(1:3) + params.alpha * pos_error;

% 速度估计 (基于位置变化)
persistent prev_pos prev_time
if isempty(prev_pos)
    prev_pos = zeros(3, 10);  % 支持最多10个无人机
    prev_time = zeros(1, 10);
end

if agent_id <= size(prev_pos, 2)
    if prev_time(agent_id) > 0
        vel_estimate = (y_pos - prev_pos(:, agent_id)) / dt;
        % 平滑速度估计
        x_hat_i(4:6) = 0.8 * x_hat_i(4:6) + 0.2 * vel_estimate;
    end
    prev_pos(:, agent_id) = y_pos;
    prev_time(agent_id) = prev_time(agent_id) + dt;
end

% 姿态估计校正
att_error = y_att - x_hat_i(7:9);
x_hat_i(7:9) = x_hat_i(7:9) + params.alpha * att_error;

% 角速度估计 (基于姿态变化)
persistent prev_att
if isempty(prev_att)
    prev_att = zeros(3, 10);
end

if agent_id <= size(prev_att, 2)
    if prev_time(agent_id) > dt
        omega_estimate = (y_att - prev_att(:, agent_id)) / dt;
        % 平滑角速度估计
        x_hat_i(10:12) = 0.8 * x_hat_i(10:12) + 0.2 * omega_estimate;
    end
    prev_att(:, agent_id) = y_att;
end

%% 简化一致性更新 (仅位置和速度)
N = size(x_hat_agents, 2);
consensus_term = zeros(12, 1);
neighbor_count = 0;

% 计算与邻居的一致性误差 (仅针对位置和速度)
for j = 1:N
    if neighbors(agent_id, j) == 1 && j ~= agent_id
        % 位置一致性
        pos_consensus = x_hat_agents(1:3, j) - x_hat_i(1:3);
        consensus_term(1:3) = consensus_term(1:3) + pos_consensus;
        
        % 速度一致性
        vel_consensus = x_hat_agents(4:6, j) - x_hat_i(4:6);
        consensus_term(4:6) = consensus_term(4:6) + vel_consensus;
        
        neighbor_count = neighbor_count + 1;
    end
end

% 应用一致性校正 (非常保守)
if neighbor_count > 0
    consensus_term = consensus_term / neighbor_count;
    % 只对位置和速度应用一致性，姿态保持局部估计
    x_hat_i(1:6) = x_hat_i(1:6) + params.beta * consensus_term(1:6);
end

%% 数值稳定性保护
% 限制估计误差的增长
estimation_error_norm = norm(x_hat_i - x_agents(:, agent_id));
if estimation_error_norm > params.max_estimation_error
    % 如果估计误差过大，重置为测量值附近
    x_hat_i(1:3) = y_pos + 0.1 * randn(3, 1);
    x_hat_i(4:6) = 0.9 * x_hat_i(4:6);  % 保守的速度估计
    x_hat_i(7:9) = y_att + 0.05 * randn(3, 1);
    x_hat_i(10:12) = 0.9 * x_hat_i(10:12);  % 保守的角速度估计
end

% 物理约束
% 位置限制
max_pos = 50;
x_hat_i(1:3) = max(-max_pos, min(max_pos, x_hat_i(1:3)));

% 速度限制
max_vel = 15;
x_hat_i(4:6) = max(-max_vel, min(max_vel, x_hat_i(4:6)));

% 姿态限制
max_att = pi/4;  % 45度限制
x_hat_i(7:9) = max(-max_att, min(max_att, x_hat_i(7:9)));

% 角速度限制
max_omega = 3;
x_hat_i(10:12) = max(-max_omega, min(max_omega, x_hat_i(10:12)));

%% 输出更新后的状态估计
x_hat_new = x_hat_i;

%% 调试信息 (可选)
persistent debug_counter
if isempty(debug_counter)
    debug_counter = zeros(1, 10);
end

if agent_id <= length(debug_counter)
    debug_counter(agent_id) = debug_counter(agent_id) + 1;
    
    % 每5秒输出一次调试信息
    if mod(debug_counter(agent_id), 500) == 0
        pos_est_error = norm(x_hat_i(1:3) - x_agents(1:3, agent_id));
        vel_est_error = norm(x_hat_i(4:6) - x_agents(4:6, agent_id));
        
        if agent_id == 1
            uav_type = '领导者';
        else
            uav_type = sprintf('跟随者%d', agent_id-1);
        end
        
        fprintf('简化观测器[%s] [%.1fs] - 位置估计误差: %.4f m, 速度估计误差: %.4f m/s, 邻居数: %d\n', ...
                uav_type, debug_counter(agent_id)*0.01, pos_est_error, vel_est_error, neighbor_count);
    end
end

end
