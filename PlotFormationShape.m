function PlotFormationShape(t_span, x_history, x_desired_history)
% 专门用于显示编队形状的可视化函数
% 输入参数:
%   t_span - 时间向量
%   x_history - 实际轨迹历史 [12 x N_total x length(t_span)]
%   x_desired_history - 期望轨迹历史 [12 x N_total x length(t_span)]

%% 创建编队形状可视化图
figure('Name', '编队形状分析', 'Position', [500, 100, 1200, 800]);

% 颜色设置
colors = [0, 0, 0;          % 领导者：黑色
          1, 0, 0;          % 跟随者1：红色
          0, 0.8, 0;        % 跟随者2：绿色
          0, 0.8, 0.8;      % 跟随者3：青色
          1, 0, 1];         % 跟随者4：品红色

%% 子图1: 3D轨迹 + 编队形状
subplot(2, 2, [1, 2]);

% 绘制轨迹
N_total = size(x_history, 2);
for i = 1:N_total
    x_traj = squeeze(x_history(1, i, :));
    y_traj = squeeze(x_history(2, i, :));
    z_traj = squeeze(x_history(3, i, :));
    
    if i == 1
        plot3(x_traj, y_traj, z_traj, 'Color', colors(i, :), ...
              'LineWidth', 2.5, 'DisplayName', '领导者轨迹');
    else
        plot3(x_traj, y_traj, z_traj, 'Color', colors(i, :), ...
              'LineWidth', 1.5, 'DisplayName', sprintf('跟随者%d轨迹', i-1));
    end
    hold on;
end

% 显示多个时刻的编队形状
time_snapshots = [5, 15, 25, 35, 45]; % 选择的时间快照
formation_alpha = 0.6; % 编队连线透明度

for snap_idx = 1:length(time_snapshots)
    t_idx = find(abs(t_span - time_snapshots(snap_idx)) < 0.1, 1);
    if ~isempty(t_idx)
        % 获取该时刻的位置
        positions = squeeze(x_history(1:3, :, t_idx));
        leader_pos = positions(:, 1);
        follower_pos = positions(:, 2:end);
        
        % 绘制编队连线
        line_color = [0.5, 0.5, 0.5, formation_alpha];
        
        % 领导者到跟随者的连线
        for i = 1:size(follower_pos, 2)
            plot3([leader_pos(1), follower_pos(1, i)], ...
                  [leader_pos(2), follower_pos(2, i)], ...
                  [leader_pos(3), follower_pos(3, i)], ...
                  '--', 'Color', line_color(1:3), 'LineWidth', 1, ...
                  'HandleVisibility', 'off');
        end
        
        % 跟随者之间的矩形连线
        if size(follower_pos, 2) == 4
            rect_order = [1, 2, 4, 3, 1]; % 矩形连接顺序
            for i = 1:length(rect_order)-1
                idx1 = rect_order(i);
                idx2 = rect_order(i+1);
                plot3([follower_pos(1, idx1), follower_pos(1, idx2)], ...
                      [follower_pos(2, idx1), follower_pos(2, idx2)], ...
                      [follower_pos(3, idx1), follower_pos(3, idx2)], ...
                      '-', 'Color', line_color(1:3), 'LineWidth', 1.2, ...
                      'HandleVisibility', 'off');
            end
        end
        
        % 标记该时刻的位置
        scatter3(leader_pos(1), leader_pos(2), leader_pos(3), ...
                80, colors(1, :), 'filled', 'MarkerEdgeColor', 'w', ...
                'LineWidth', 1.5, 'HandleVisibility', 'off');
        
        for i = 1:size(follower_pos, 2)
            scatter3(follower_pos(1, i), follower_pos(2, i), follower_pos(3, i), ...
                    60, colors(i+1, :), 'filled', 'MarkerEdgeColor', 'w', ...
                    'LineWidth', 1, 'HandleVisibility', 'off');
        end
        
        % 添加时间标签
        text(leader_pos(1), leader_pos(2), leader_pos(3) + 0.5, ...
             sprintf('t=%.0fs', time_snapshots(snap_idx)), ...
             'FontSize', 8, 'HorizontalAlignment', 'center', ...
             'BackgroundColor', 'white', 'EdgeColor', 'black');
    end
end

% 添加编队连线到图例
plot3(NaN, NaN, NaN, '-', 'Color', [0.5, 0.5, 0.5], 'LineWidth', 1.2, ...
      'DisplayName', '编队形状');

xlabel('x/m'); ylabel('y/m'); zlabel('z/m');
title('三维编队飞行轨迹及形状保持');
legend('Location', 'best', 'FontSize', 9);
view(45, 30);
axis equal;
grid on;
xlim([2, 14]); ylim([2, 14]); zlim([8, 20]);

%% 子图2: 编队形状误差分析
subplot(2, 2, 3);

% 计算编队形状误差
formation_errors = zeros(1, length(t_span));
desired_formation_size = zeros(1, length(t_span));

for k = 1:length(t_span)
    % 实际编队形状
    actual_pos = squeeze(x_history(1:3, 2:end, k)); % 只考虑跟随者
    % 期望编队形状
    desired_pos = squeeze(x_desired_history(1:3, 2:end, k));
    
    % 计算形状误差（Frobenius范数）
    formation_errors(k) = norm(actual_pos - desired_pos, 'fro');
    
    % 计算期望编队尺寸（用于归一化）
    center_desired = mean(desired_pos, 2);
    desired_formation_size(k) = mean(vecnorm(desired_pos - center_desired, 2, 1));
end

% 归一化编队误差
normalized_errors = formation_errors ./ max(desired_formation_size);

plot(t_span, formation_errors, 'b-', 'LineWidth', 2, 'DisplayName', '绝对误差');
hold on;
plot(t_span, normalized_errors * max(formation_errors), 'r--', 'LineWidth', 1.5, ...
     'DisplayName', '归一化误差');

xlabel('时间 (s)'); ylabel('编队形状误差 (m)');
title('编队形状保持误差');
legend('Location', 'best');
grid on;

%% 子图3: 编队几何参数分析
subplot(2, 2, 4);

% 计算编队的几何参数
formation_area = zeros(1, length(t_span));
formation_perimeter = zeros(1, length(t_span));

for k = 1:length(t_span)
    follower_pos = squeeze(x_history(1:3, 2:end, k));
    
    if size(follower_pos, 2) == 4
        % 计算矩形编队的面积和周长
        % 假设跟随者按矩形排列
        p1 = follower_pos(:, 1); p2 = follower_pos(:, 2);
        p3 = follower_pos(:, 3); p4 = follower_pos(:, 4);
        
        % 计算边长
        side1 = norm(p2 - p1);
        side2 = norm(p4 - p2);
        side3 = norm(p3 - p4);
        side4 = norm(p1 - p3);
        
        % 近似矩形的面积和周长
        length_avg = (side1 + side3) / 2;
        width_avg = (side2 + side4) / 2;
        
        formation_area(k) = length_avg * width_avg;
        formation_perimeter(k) = 2 * (length_avg + width_avg);
    end
end

% 理论值
theoretical_area = 3 * 3; % 1.5m x 1.5m 的矩形 x 4
theoretical_perimeter = 4 * 3; % 矩形周长

yyaxis left;
plot(t_span, formation_area, 'g-', 'LineWidth', 2);
yline(theoretical_area, 'g--', 'LineWidth', 1, 'Alpha', 0.7);
ylabel('编队面积 (m²)', 'Color', 'g');

yyaxis right;
plot(t_span, formation_perimeter, 'm-', 'LineWidth', 2);
yline(theoretical_perimeter, 'm--', 'LineWidth', 1, 'Alpha', 0.7);
ylabel('编队周长 (m)', 'Color', 'm');

xlabel('时间 (s)');
title('编队几何参数变化');
grid on;

%% 整体图设置
sgtitle('无人机编队形状分析', 'FontSize', 14, 'FontWeight', 'bold');

fprintf('编队形状可视化完成！\n');
fprintf('- 显示了多个时刻的编队快照\n');
fprintf('- 分析了编队形状保持误差\n');
fprintf('- 监控了编队几何参数变化\n');

end
