function PlotUltraSimpleResults(t_span, x_history, x_desired, u_history, pos_errors, vel_errors, formation_offset)
% 极简稳定系统结果可视化
% 专注于展示是否达成严格的性能要求

%% 设置中文字体
try
    set(0, 'DefaultAxesFontName', 'Microsoft YaHei');
    set(0, 'DefaultTextFontName', 'Microsoft YaHei');
catch
    % 如果设置失败，使用默认字体
end

N_total = size(x_history, 2);
colors = {'m', 'r', 'g', 'b', 'c'};
line_styles = {'-', '--', '-.', ':', '-'};

%% 图1: 性能达成分析
figure('Name', '极简稳定系统 - 性能达成分析', 'Position', [100, 100, 1400, 800]);

% 子图1: 位置跟踪误差
subplot(2, 3, 1);
hold on; grid on;
for i = 1:N_total
    if i == 1
        plot(t_span, pos_errors(i, :), 'Color', colors{i}, 'LineWidth', 2.5, 'LineStyle', line_styles{i});
    else
        plot(t_span, pos_errors(i, :), 'Color', colors{i}, 'LineWidth', 1.8, 'LineStyle', line_styles{i});
    end
end
xlabel('时间 (s)', 'FontSize', 12);
ylabel('位置跟踪误差 (m)', 'FontSize', 12);
title('位置跟踪性能', 'FontSize', 14, 'FontWeight', 'bold');

% 添加性能要求线
yline(0.5, 'r--', '目标: 0.5m', 'LineWidth', 2, 'FontSize', 10);
yline(0.1, 'g--', '优秀: 0.1m', 'LineWidth', 1.5, 'FontSize', 10);

legend_labels = cell(N_total, 1);
for i = 1:N_total
    if i == 1
        legend_labels{i} = '领导者';
    else
        legend_labels{i} = sprintf('跟随者%d', i-1);
    end
end
legend(legend_labels, 'Location', 'best', 'FontSize', 10);
ylim([0, max(1.0, max(pos_errors(:))*1.1)]);

% 子图2: 速度跟踪误差
subplot(2, 3, 2);
hold on; grid on;
for i = 1:N_total
    if i == 1
        plot(t_span, vel_errors(i, :), 'Color', colors{i}, 'LineWidth', 2.5, 'LineStyle', line_styles{i});
    else
        plot(t_span, vel_errors(i, :), 'Color', colors{i}, 'LineWidth', 1.8, 'LineStyle', line_styles{i});
    end
end
xlabel('时间 (s)', 'FontSize', 12);
ylabel('速度跟踪误差 (m/s)', 'FontSize', 12);
title('速度跟踪性能', 'FontSize', 14, 'FontWeight', 'bold');

% 添加性能要求线
yline(1.0, 'r--', '目标: 1.0m/s', 'LineWidth', 2, 'FontSize', 10);
yline(0.2, 'g--', '优秀: 0.2m/s', 'LineWidth', 1.5, 'FontSize', 10);

legend(legend_labels, 'Location', 'best', 'FontSize', 10);
ylim([0, max(2.0, max(vel_errors(:))*1.1)]);

% 子图3: 15秒后性能统计
subplot(2, 3, 3);
steady_start_idx = find(t_span >= 15, 1);
if ~isempty(steady_start_idx)
    steady_pos = pos_errors(:, steady_start_idx:end);
    steady_vel = vel_errors(:, steady_start_idx:end);
    
    performance_stats = [
        mean(steady_pos(:));
        max(steady_pos(:));
        mean(steady_vel(:));
        max(steady_vel(:))
    ];
    
    % 根据是否达成目标设置颜色
    bar_colors = zeros(4, 3);
    bar_colors(1, :) = [0.2, 0.8, 0.2];  % 位置平均 - 绿色
    bar_colors(2, :) = [0.8, 0.6, 0.2];  % 位置最大 - 橙色
    bar_colors(3, :) = [0.2, 0.6, 0.8];  % 速度平均 - 蓝色
    bar_colors(4, :) = [0.8, 0.2, 0.2];  % 速度最大 - 红色
    
    % 如果未达成目标，改为红色
    if performance_stats(1) >= 0.5
        bar_colors(1, :) = [0.8, 0.2, 0.2];
    end
    if performance_stats(3) >= 1.0
        bar_colors(3, :) = [0.8, 0.2, 0.2];
    end
    
    bar_handle = bar(performance_stats);
    bar_handle.FaceColor = 'flat';
    bar_handle.CData = bar_colors;
    
    set(gca, 'XTickLabel', {'位置-平均', '位置-最大', '速度-平均', '速度-最大'});
    ylabel('跟踪误差', 'FontSize', 12);
    title('15秒后稳态性能统计', 'FontSize', 14, 'FontWeight', 'bold');
    grid on;
    
    % 添加数值标签
    for i = 1:length(performance_stats)
        text(i, performance_stats(i) + max(performance_stats)*0.02, ...
             sprintf('%.3f', performance_stats(i)), 'HorizontalAlignment', 'center', 'FontSize', 10);
    end
    
    % 添加目标线
    yline(0.5, 'r--', 'LineWidth', 1.5);
    yline(1.0, 'b--', 'LineWidth', 1.5);
end

% 子图4: 目标达成情况
subplot(2, 3, 4);
if ~isempty(steady_start_idx)
    % 计算目标达成百分比
    pos_achievement = min(100, (0.5 / mean(steady_pos(:))) * 100);
    vel_achievement = min(100, (1.0 / mean(steady_vel(:))) * 100);
    
    achievement_data = [pos_achievement; vel_achievement];
    
    bar_handle2 = bar(achievement_data);
    
    % 根据达成情况设置颜色
    if pos_achievement >= 100 && vel_achievement >= 100
        bar_handle2.FaceColor = [0.2, 0.8, 0.2];  % 绿色 - 全部达成
    elseif pos_achievement >= 100 || vel_achievement >= 100
        bar_handle2.FaceColor = [0.8, 0.8, 0.2];  % 黄色 - 部分达成
    else
        bar_handle2.FaceColor = [0.8, 0.2, 0.2];  % 红色 - 未达成
    end
    
    set(gca, 'XTickLabel', {'位置目标', '速度目标'});
    ylabel('目标达成率 (%)', 'FontSize', 12);
    title('性能目标达成情况', 'FontSize', 14, 'FontWeight', 'bold');
    grid on;
    
    % 添加100%目标线
    yline(100, 'r--', '100%目标', 'LineWidth', 2, 'FontSize', 10);
    
    % 添加数值标签
    for i = 1:length(achievement_data)
        text(i, achievement_data(i) + max(achievement_data)*0.02, ...
             sprintf('%.1f%%', achievement_data(i)), 'HorizontalAlignment', 'center', 'FontSize', 10);
    end
    
    % 添加达成状态文本
    pos_met = mean(steady_pos(:)) < 0.5;
    vel_met = mean(steady_vel(:)) < 1.0;
    
    if pos_met && vel_met
        text(0.5, 0.9, '🎉 全部目标达成！', 'Units', 'normalized', 'FontSize', 12, ...
             'FontWeight', 'bold', 'Color', 'green', 'HorizontalAlignment', 'center');
    elseif pos_met
        text(0.5, 0.9, '⚠️ 仅位置目标达成', 'Units', 'normalized', 'FontSize', 12, ...
             'FontWeight', 'bold', 'Color', 'orange', 'HorizontalAlignment', 'center');
    elseif vel_met
        text(0.5, 0.9, '⚠️ 仅速度目标达成', 'Units', 'normalized', 'FontSize', 12, ...
             'FontWeight', 'bold', 'Color', 'orange', 'HorizontalAlignment', 'center');
    else
        text(0.5, 0.9, '❌ 目标未达成', 'Units', 'normalized', 'FontSize', 12, ...
             'FontWeight', 'bold', 'Color', 'red', 'HorizontalAlignment', 'center');
    end
end

% 子图5: 控制输入分析
subplot(2, 3, 5);
hold on; grid on;
for i = 1:N_total
    thrust_history = squeeze(u_history(1, i, :));
    plot(t_span, thrust_history, 'Color', colors{i}, 'LineWidth', 1.5, 'LineStyle', line_styles{i});
end
xlabel('时间 (s)', 'FontSize', 12);
ylabel('推力 (N)', 'FontSize', 12);
title('控制输入 - 推力', 'FontSize', 14, 'FontWeight', 'bold');
legend(legend_labels, 'Location', 'best', 'FontSize', 10);

% 子图6: 15-25秒窗口放大分析
subplot(2, 3, 6);
zoom_start = find(t_span >= 15, 1);
zoom_end = find(t_span <= 25, 1, 'last');
if ~isempty(zoom_start) && ~isempty(zoom_end)
    t_zoom = t_span(zoom_start:zoom_end);
    hold on; grid on;
    for i = 1:N_total
        plot(t_zoom, pos_errors(i, zoom_start:zoom_end), 'Color', colors{i}, 'LineWidth', 2, 'LineStyle', '-');
        plot(t_zoom, vel_errors(i, zoom_start:zoom_end), 'Color', colors{i}, 'LineWidth', 2, 'LineStyle', '--');
    end
    xlabel('时间 (s)', 'FontSize', 12);
    ylabel('跟踪误差', 'FontSize', 12);
    title('15-25秒稳态窗口分析', 'FontSize', 14, 'FontWeight', 'bold');
    
    % 添加目标线
    yline(0.5, 'r--', '位置目标', 'LineWidth', 1.5, 'FontSize', 10);
    yline(1.0, 'b--', '速度目标', 'LineWidth', 1.5, 'FontSize', 10);
    
    legend_zoom = cell(N_total*2, 1);
    for i = 1:N_total
        if i == 1
            legend_zoom{2*i-1} = '领导者-位置';
            legend_zoom{2*i} = '领导者-速度';
        else
            legend_zoom{2*i-1} = sprintf('跟随者%d-位置', i-1);
            legend_zoom{2*i} = sprintf('跟随者%d-速度', i-1);
        end
    end
    legend(legend_zoom, 'Location', 'best', 'FontSize', 8);
end

sgtitle('极简稳定系统 - 性能达成分析', 'FontSize', 16, 'FontWeight', 'bold');

%% 图2: 3D轨迹展示
figure('Name', '极简稳定系统 - 3D轨迹展示', 'Position', [200, 200, 1200, 600]);

% 子图1: 3D飞行轨迹
subplot(1, 2, 1);
hold on; grid on;
for i = 1:N_total
    x_traj = squeeze(x_history(1, i, :));
    y_traj = squeeze(x_history(2, i, :));
    z_traj = squeeze(x_history(3, i, :));
    plot3(x_traj, y_traj, z_traj, 'Color', colors{i}, 'LineWidth', 2, 'LineStyle', line_styles{i});
    
    % 标记起始和结束位置
    scatter3(x_traj(1), y_traj(1), z_traj(1), 100, colors{i}, 'o', 'filled', 'MarkerEdgeColor', 'k');
    scatter3(x_traj(end), y_traj(end), z_traj(end), 100, colors{i}, 's', 'filled', 'MarkerEdgeColor', 'k');
end
xlabel('X (m)', 'FontSize', 12); 
ylabel('Y (m)', 'FontSize', 12); 
zlabel('Z (m)', 'FontSize', 12);
title('3D飞行轨迹', 'FontSize', 14, 'FontWeight', 'bold');
legend(legend_labels, 'Location', 'best', 'FontSize', 10);
view(45, 30);

% 子图2: 编队形成展示
subplot(1, 2, 2);
time_snapshots = [5, 10, 15, 20, 25, 30];
snapshot_indices = arrayfun(@(t) find(t_span >= t, 1), time_snapshots);

hold on; grid on;
for snap_idx = 1:length(time_snapshots)
    k_snap = snapshot_indices(snap_idx);
    if k_snap <= size(x_history, 3)
        % 绘制编队
        for i = 1:N_total
            x_pos = x_history(1, i, k_snap);
            y_pos = x_history(2, i, k_snap);
            if i == 1
                scatter(x_pos, y_pos, 120, colors{i}, 'filled', 'o', 'MarkerEdgeColor', 'k', 'LineWidth', 1.5);
            else
                scatter(x_pos, y_pos, 80, colors{i}, 'filled', 's', 'MarkerEdgeColor', 'k', 'LineWidth', 1);
            end
        end
        
        % 连接编队（仅在最后时刻）
        if snap_idx == length(time_snapshots)
            leader_pos = [x_history(1, 1, k_snap), x_history(2, 1, k_snap)];
            for i = 2:N_total
                follower_pos = [x_history(1, i, k_snap), x_history(2, i, k_snap)];
                plot([leader_pos(1), follower_pos(1)], [leader_pos(2), follower_pos(2)], 'k--', 'LineWidth', 1);
            end
        end
    end
end

xlabel('X (m)', 'FontSize', 12);
ylabel('Y (m)', 'FontSize', 12);
title('编队形成过程', 'FontSize', 14, 'FontWeight', 'bold');
axis equal; grid on;

% 添加时间标签
for snap_idx = 1:length(time_snapshots)
    k_snap = snapshot_indices(snap_idx);
    if k_snap <= size(x_history, 3)
        x_center = mean(x_history(1, :, k_snap));
        y_center = mean(x_history(2, :, k_snap));
        text(x_center, y_center + 0.2, sprintf('%ds', time_snapshots(snap_idx)), ...
             'HorizontalAlignment', 'center', 'FontSize', 9, 'FontWeight', 'bold');
    end
end

sgtitle('极简稳定系统 - 3D轨迹与编队展示', 'FontSize', 16, 'FontWeight', 'bold');

%% 图3: 详细误差分析
figure('Name', '极简稳定系统 - 详细误差分析', 'Position', [250, 250, 1200, 600]);

% 子图1: 各UAV位置误差对比
subplot(2, 2, 1);
hold on; grid on;
for i = 1:N_total
    plot(t_span, pos_errors(i, :), 'Color', colors{i}, 'LineWidth', 2, 'LineStyle', line_styles{i});
end
xlabel('时间 (s)', 'FontSize', 12);
ylabel('位置跟踪误差 (m)', 'FontSize', 12);
title('各UAV位置误差对比', 'FontSize', 14, 'FontWeight', 'bold');
legend(legend_labels, 'Location', 'best', 'FontSize', 10);
yline(0.5, 'r--', '目标线', 'LineWidth', 2);

% 子图2: 各UAV速度误差对比
subplot(2, 2, 2);
hold on; grid on;
for i = 1:N_total
    plot(t_span, vel_errors(i, :), 'Color', colors{i}, 'LineWidth', 2, 'LineStyle', line_styles{i});
end
xlabel('时间 (s)', 'FontSize', 12);
ylabel('速度跟踪误差 (m/s)', 'FontSize', 12);
title('各UAV速度误差对比', 'FontSize', 14, 'FontWeight', 'bold');
legend(legend_labels, 'Location', 'best', 'FontSize', 10);
yline(1.0, 'r--', '目标线', 'LineWidth', 2);

% 子图3: 系统整体误差
subplot(2, 2, 3);
overall_pos_error = max(pos_errors, [], 1);
overall_vel_error = max(vel_errors, [], 1);
hold on; grid on;
plot(t_span, overall_pos_error, 'r-', 'LineWidth', 2.5, 'DisplayName', '系统最大位置误差');
plot(t_span, overall_vel_error, 'b-', 'LineWidth', 2.5, 'DisplayName', '系统最大速度误差');
xlabel('时间 (s)', 'FontSize', 12);
ylabel('系统整体误差', 'FontSize', 12);
title('系统整体误差趋势', 'FontSize', 14, 'FontWeight', 'bold');
legend('Location', 'best', 'FontSize', 10);
yline(0.5, 'r--', '位置目标', 'LineWidth', 1.5);
yline(1.0, 'b--', '速度目标', 'LineWidth', 1.5);

% 子图4: 误差收敛分析
subplot(2, 2, 4);
if ~isempty(steady_start_idx)
    % 计算收敛时间
    convergence_threshold_pos = 0.5;
    convergence_threshold_vel = 1.0;
    
    pos_converged = overall_pos_error < convergence_threshold_pos;
    vel_converged = overall_vel_error < convergence_threshold_vel;
    
    pos_convergence_time = NaN;
    vel_convergence_time = NaN;
    
    % 找到首次收敛时间
    pos_conv_idx = find(pos_converged, 1);
    if ~isempty(pos_conv_idx)
        pos_convergence_time = t_span(pos_conv_idx);
    end
    
    vel_conv_idx = find(vel_converged, 1);
    if ~isempty(vel_conv_idx)
        vel_convergence_time = t_span(vel_conv_idx);
    end
    
    % 绘制收敛分析
    convergence_data = [pos_convergence_time; vel_convergence_time];
    bar_handle3 = bar(convergence_data);
    bar_handle3.FaceColor = [0.2, 0.6, 0.8];
    
    set(gca, 'XTickLabel', {'位置收敛时间', '速度收敛时间'});
    ylabel('收敛时间 (s)', 'FontSize', 12);
    title('误差收敛分析', 'FontSize', 14, 'FontWeight', 'bold');
    grid on;
    
    % 添加数值标签
    for i = 1:length(convergence_data)
        if ~isnan(convergence_data(i))
            text(i, convergence_data(i) + max(convergence_data, [], 'omitnan')*0.02, ...
                 sprintf('%.1fs', convergence_data(i)), 'HorizontalAlignment', 'center', 'FontSize', 10);
        else
            text(i, 5, '未收敛', 'HorizontalAlignment', 'center', 'FontSize', 10, 'Color', 'red');
        end
    end
end

sgtitle('极简稳定系统 - 详细误差分析', 'FontSize', 16, 'FontWeight', 'bold');

fprintf('🎯 极简稳定系统可视化完成！\n');
fprintf('✅ 生成了3个主要图表组\n');
fprintf('✅ 性能达成分析与目标评估\n');
fprintf('✅ 3D轨迹与编队形成展示\n');
fprintf('✅ 详细误差分析与收敛评估\n');
fprintf('✅ 完整中文可视化显示\n');

end
