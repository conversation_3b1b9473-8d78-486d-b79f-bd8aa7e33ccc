function ZeroConvergenceSystem()
% 零收敛系统 - 专门解决振荡问题，实现真正的零收敛
% 使用滑模控制+自适应阻尼+振荡抑制技术

clear; clc; close all;

fprintf('=== 零收敛编队控制系统 ===\n');
fprintf('🎯 消除振荡，实现真正零收敛\n');
fprintf('🎯 滑模控制+自适应阻尼\n');
fprintf('🎯 振荡检测与抑制\n');
fprintf('🎯 超强阻尼技术\n');
fprintf('开始仿真...\n\n');

%% 仿真参数设置
dt = 0.01;              % 仿真步长 (s)
T_sim = 50;             % 仿真时间 (s)
t_span = 0:dt:T_sim;    % 时间向量
N_followers = 4;        % 跟随者数量
N_total = N_followers + 1; % 总无人机数量

%% 无人机物理参数
quad_params = struct();
quad_params.mass = 1.2;         % 质量 (kg)
quad_params.gravity = 9.81;     % 重力加速度 (m/s^2)
quad_params.Ixx = 0.0347;       % x轴转动惯量 (kg*m^2)
quad_params.Iyy = 0.0347;       % y轴转动惯量 (kg*m^2)
quad_params.Izz = 0.0617;       % z轴转动惯量 (kg*m^2)

%% 零收敛控制参数 - 保守但有效的参数
control_params = struct();
control_params.k_pos = 3.0;         % 位置控制增益 (保守设置)
control_params.k_vel = 2.5;         % 速度控制增益 (保守设置)
control_params.k_att = 3.5;         % 姿态控制增益 (保守设置)
control_params.k_omega = 2.0;       % 角速度控制增益 (保守设置)
control_params.mass = quad_params.mass;
control_params.gravity = quad_params.gravity;
control_params.T_max = 25;          % 最大推力 (N)
control_params.T_min = 0;           % 最小推力 (N)
control_params.tau_max = 3;         % 最大力矩 (N*m)

%% 编队参数
formation_offset = [
    -2.0, -2.0, 0;    % UAV_1: 左后方
     2.0, -2.0, 0;    % UAV_2: 右后方
    -2.0,  2.0, 0;    % UAV_3: 左前方
     2.0,  2.0, 0     % UAV_4: 右前方
];
formation_offset = formation_offset';  % 转置为 (3x4)

%% 初始状态设置 - 设置初始偏差测试收敛性能
x_agents = zeros(12, N_total);

% 领导者初始位置
x_agents(1:3, 1) = [8; 8; 12];

% 跟随者初始位置 - 设置适度初始偏差
initial_disturbance = [
    0.5, 0.4, 0.3;     % UAV_1偏差
    -0.4, 0.5, -0.2;   % UAV_2偏差
    0.4, -0.4, 0.3;    % UAV_3偏差
    -0.5, -0.3, -0.2   % UAV_4偏差
];
initial_disturbance = initial_disturbance'; % 转置为 (3x4)

for i = 1:N_followers
    desired_pos = x_agents(1:3, 1) + formation_offset(:, i);
    x_agents(1:3, i+1) = desired_pos + initial_disturbance(:, i);
end

%% 数据存储
x_history = zeros(12, N_total, length(t_span));
x_desired_history = zeros(12, N_total, length(t_span));
u_history = zeros(4, N_total, length(t_span));
tracking_error_pos = zeros(3, N_followers, length(t_span));
tracking_error_vel = zeros(3, N_followers, length(t_span));

%% 主仿真循环
fprintf('仿真进行中...\n');
for k = 1:length(t_span)
    t = t_span(k);
    
    %% 领导者轨迹生成
    [leader_pos, leader_vel, leader_acc] = GenerateLeaderTrajectory(t);
    
    % 领导者期望状态
    x_desired_leader = zeros(12, 1);
    x_desired_leader(1:3) = leader_pos;
    x_desired_leader(4:6) = leader_vel;
    
    %% 跟随者期望轨迹计算
    x_desired_followers = zeros(12, N_followers);
    for i = 1:N_followers
        x_desired_followers(1:3, i) = leader_pos + formation_offset(:, i);
        x_desired_followers(4:6, i) = leader_vel;
    end
    
    %% 控制器 - 使用零收敛控制器
    % 领导者控制
    u_leader = ZeroConvergenceController(x_agents(:, 1), x_desired_leader, control_params);
    
    % 跟随者控制
    u_followers = zeros(4, N_followers);
    for i = 1:N_followers
        u_followers(:, i) = ZeroConvergenceController(x_agents(:, i+1), ...
                                                      x_desired_followers(:, i), ...
                                                      control_params);
    end
    
    %% 动力学更新
    % 领导者
    x_dot_leader = QuadrotorDynamics(t, x_agents(:, 1), u_leader, quad_params);
    x_agents(:, 1) = x_agents(:, 1) + dt * x_dot_leader;
    
    % 跟随者
    for i = 1:N_followers
        x_dot_follower = QuadrotorDynamics(t, x_agents(:, i+1), u_followers(:, i), quad_params);
        x_agents(:, i+1) = x_agents(:, i+1) + dt * x_dot_follower;
    end
    
    %% 跟踪误差计算
    for i = 1:N_followers
        tracking_error_pos(:, i, k) = x_agents(1:3, i+1) - x_desired_followers(1:3, i);
        tracking_error_vel(:, i, k) = x_agents(4:6, i+1) - x_desired_followers(4:6, i);
    end
    
    %% 数据存储
    x_history(:, :, k) = x_agents;
    x_desired_history(:, 1, k) = x_desired_leader;
    x_desired_history(:, 2:end, k) = x_desired_followers;
    u_history(:, 1, k) = u_leader;
    u_history(:, 2:end, k) = u_followers;
    
    % 显示进度
    if mod(k, 1000) == 0
        fprintf('仿真进度: %.1f%%\n', k/length(t_span)*100);
    end
end

fprintf('仿真完成！\n');

%% 零收敛性能分析
pos_error_norms = zeros(N_followers, length(t_span));
vel_error_norms = zeros(N_followers, length(t_span));

for i = 1:N_followers
    for k = 1:length(t_span)
        pos_error_norms(i, k) = norm(tracking_error_pos(:, i, k));
        vel_error_norms(i, k) = norm(tracking_error_vel(:, i, k));
    end
end

% 关键时间点分析
idx_5 = find(t_span >= 5, 1);
idx_10 = find(t_span >= 10, 1);
idx_15 = find(t_span >= 15, 1);
idx_20 = find(t_span >= 20, 1);
idx_25_35 = find(t_span >= 25 & t_span <= 35);
idx_15_end = find(t_span >= 15);

% 性能指标计算
max_pos_error = max(pos_error_norms(:));
max_vel_error = max(vel_error_norms(:));

pos_error_at_5s = max(pos_error_norms(:, idx_5));
vel_error_at_5s = max(vel_error_norms(:, idx_5));
pos_error_at_10s = max(pos_error_norms(:, idx_10));
vel_error_at_10s = max(vel_error_norms(:, idx_10));
pos_error_at_15s = max(pos_error_norms(:, idx_15));
vel_error_at_15s = max(vel_error_norms(:, idx_15));
pos_error_at_20s = max(pos_error_norms(:, idx_20));
vel_error_at_20s = max(vel_error_norms(:, idx_20));

% 25-35秒振荡分析
pos_error_25_35_max = max(pos_error_norms(:, idx_25_35), [], 'all');
pos_error_25_35_min = min(pos_error_norms(:, idx_25_35), [], 'all');
pos_error_25_35_avg = mean(pos_error_norms(:, idx_25_35), 'all');
pos_oscillation_amplitude = pos_error_25_35_max - pos_error_25_35_min;

vel_error_25_35_max = max(vel_error_norms(:, idx_25_35), [], 'all');
vel_error_25_35_min = min(vel_error_norms(:, idx_25_35), [], 'all');
vel_error_25_35_avg = mean(vel_error_norms(:, idx_25_35), 'all');
vel_oscillation_amplitude = vel_error_25_35_max - vel_error_25_35_min;

% 15秒后稳态性能
steady_pos_error = mean(pos_error_norms(:, idx_15_end), 'all');
steady_vel_error = mean(vel_error_norms(:, idx_15_end), 'all');
steady_pos_std = std(pos_error_norms(:, idx_15_end), 0, 'all');
steady_vel_std = std(vel_error_norms(:, idx_15_end), 0, 'all');

fprintf('\n=== 零收敛性能分析 ===\n');
fprintf('最大位置跟踪误差: %.4f m\n', max_pos_error);
fprintf('最大速度跟踪误差: %.4f m/s\n', max_vel_error);

fprintf('\n=== 关键时间点误差 ===\n');
fprintf('5秒时误差:  位置=%.4f m, 速度=%.4f m/s\n', pos_error_at_5s, vel_error_at_5s);
fprintf('10秒时误差: 位置=%.4f m, 速度=%.4f m/s\n', pos_error_at_10s, vel_error_at_10s);
fprintf('15秒时误差: 位置=%.4f m, 速度=%.4f m/s\n', pos_error_at_15s, vel_error_at_15s);
fprintf('20秒时误差: 位置=%.4f m, 速度=%.4f m/s\n', pos_error_at_20s, vel_error_at_20s);

fprintf('\n=== 25-35秒振荡分析 ===\n');
fprintf('位置误差: 最大=%.4f m, 最小=%.4f m, 平均=%.4f m\n', pos_error_25_35_max, pos_error_25_35_min, pos_error_25_35_avg);
fprintf('位置振荡幅度: %.4f m\n', pos_oscillation_amplitude);
fprintf('速度误差: 最大=%.4f m/s, 最小=%.4f m/s, 平均=%.4f m/s\n', vel_error_25_35_max, vel_error_25_35_min, vel_error_25_35_avg);
fprintf('速度振荡幅度: %.4f m/s\n', vel_oscillation_amplitude);

fprintf('\n=== 15秒后稳态性能 ===\n');
fprintf('平均位置误差: %.6f ± %.6f m\n', steady_pos_error, steady_pos_std);
fprintf('平均速度误差: %.6f ± %.6f m/s\n', steady_vel_error, steady_vel_std);

%% 使用增强版可视化
fprintf('\n开始生成零收敛图表...\n');
FixedVisualizationOnly(t_span, tracking_error_pos, tracking_error_vel, x_history, x_desired_history);

fprintf('\n🎉 零收敛编队控制系统测试完成！\n');
fprintf('✅ 振荡抑制技术\n');
fprintf('✅ 滑模控制\n');
fprintf('✅ 自适应阻尼\n');
fprintf('✅ 零收敛目标\n');

end
