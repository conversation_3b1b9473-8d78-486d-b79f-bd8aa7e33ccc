function PlotLeaderFollowerResultsFixed(t_span, x_history, x_desired_history, ...
                                        tracking_error_pos, tracking_error_vel, u_history)
% 修复版本的可视化函数 - 确保图例和曲线正确显示
% 输入参数:
%   t_span - 时间向量
%   x_history - 实际轨迹历史 [12 x N_total x length(t_span)]
%   x_desired_history - 期望轨迹历史 [12 x N_total x length(t_span)]
%   tracking_error_pos - 位置跟踪误差 [3 x N_followers x length(t_span)]
%   tracking_error_vel - 速度跟踪误差 [3 x N_followers x length(t_span)]
%   u_history - 控制输入历史 [4 x N_total x length(t_span)]

fprintf('开始生成修复版可视化图表...\n');

%% 颜色和线型设置
colors = [0, 0, 0;          % 领导者：黑色
          1, 0, 0;          % 跟随者1：红色
          0, 0.8, 0;        % 跟随者2：绿色
          0, 0.8, 0.8;      % 跟随者3：青色
          1, 0, 1];         % 跟随者4：品红色

line_styles = {'-', '-', '-', '-', '-'};
line_widths = [2.5, 2, 2, 2, 2];

%% 图1: 位置跟踪误差
fprintf('生成位置跟踪误差图...\n');
figure('Name', '位置跟踪误差', 'Position', [100, 100, 800, 500]);

% 计算位置跟踪误差的模长
pos_error_norm = zeros(4, length(t_span));
for i = 1:4
    for k = 1:length(t_span)
        pos_error_norm(i, k) = norm(tracking_error_pos(:, i, k));
    end
end

% 绘制主图
hold on; grid on;
h_pos = []; % 存储图形句柄
for i = 1:4
    h = plot(t_span, pos_error_norm(i, :), 'Color', colors(i+1, :), ...
             'LineStyle', line_styles{i+1}, 'LineWidth', line_widths(i+1), ...
             'DisplayName', sprintf('无人机_%d的位置跟踪误差', i));
    h_pos = [h_pos, h];
end

% 设置主图属性
xlabel('时间(s)', 'FontSize', 12); 
ylabel('位置跟踪误差响应 m_{pi}(t)', 'FontSize', 12);
title('位置跟踪误差 m_{pi}(t)', 'FontSize', 14, 'FontWeight', 'bold');
legend(h_pos, 'Location', 'northeast', 'FontSize', 10);
xlim([0, 50]); ylim([-0.5, 5]);
set(gca, 'FontSize', 11);

% 添加放大图 (内嵌子图)
axes('Position', [0.15, 0.6, 0.25, 0.25]);
hold on; grid on;
t_zoom = t_span(t_span >= 5 & t_span <= 15);
idx_zoom = find(t_span >= 5 & t_span <= 15);
for i = 1:4
    plot(t_zoom, pos_error_norm(i, idx_zoom), 'Color', colors(i+1, :), ...
         'LineStyle', line_styles{i+1}, 'LineWidth', line_widths(i+1));
end
xlim([5, 15]); ylim([0, 1.5]);
xlabel('时间(s)', 'FontSize', 8); 
ylabel('位置跟踪误差响应 m_{pi}(t)', 'FontSize', 8);
set(gca, 'FontSize', 8);

%% 图2: 速度跟踪误差
fprintf('生成速度跟踪误差图...\n');
figure('Name', '速度跟踪误差', 'Position', [200, 200, 800, 500]);

% 计算速度跟踪误差的模长
vel_error_norm = zeros(4, length(t_span));
for i = 1:4
    for k = 1:length(t_span)
        vel_error_norm(i, k) = norm(tracking_error_vel(:, i, k));
    end
end

% 绘制主图
hold on; grid on;
h_vel = []; % 存储图形句柄
for i = 1:4
    h = plot(t_span, vel_error_norm(i, :), 'Color', colors(i+1, :), ...
             'LineStyle', line_styles{i+1}, 'LineWidth', line_widths(i+1), ...
             'DisplayName', sprintf('无人机_%d的速度跟踪误差', i));
    h_vel = [h_vel, h];
end

% 设置主图属性
xlabel('时间(s)', 'FontSize', 12); 
ylabel('速度跟踪误差响应 m_{vi}(t)', 'FontSize', 12);
title('速度跟踪误差 m_{vi}(t)', 'FontSize', 14, 'FontWeight', 'bold');
legend(h_vel, 'Location', 'northeast', 'FontSize', 10);
xlim([0, 50]); ylim([-0.5, 4]);
set(gca, 'FontSize', 11);

% 添加放大图 (内嵌子图)
axes('Position', [0.15, 0.6, 0.25, 0.25]);
hold on; grid on;
idx_zoom2 = find(t_span >= 2 & t_span <= 12);
for i = 1:4
    plot(t_span(idx_zoom2), vel_error_norm(i, idx_zoom2), 'Color', colors(i+1, :), ...
         'LineStyle', line_styles{i+1}, 'LineWidth', line_widths(i+1));
end
xlim([2, 12]); ylim([0, 1]);
xlabel('时间(s)', 'FontSize', 8); 
ylabel('速度跟踪误差响应 m_{vi}(t)', 'FontSize', 8);
set(gca, 'FontSize', 8);

%% 图3: 3D飞行轨迹
fprintf('生成3D飞行轨迹图...\n');
figure('Name', '3D飞行轨迹', 'Position', [300, 300, 800, 600]);
hold on; grid on;

% 绘制所有无人机的轨迹
N_total = size(x_history, 2);
h_3d = []; % 存储3D图形句柄

for i = 1:N_total
    x_traj = squeeze(x_history(1, i, :));
    y_traj = squeeze(x_history(2, i, :));
    z_traj = squeeze(x_history(3, i, :));
    
    if i == 1
        h = plot3(x_traj, y_traj, z_traj, 'Color', colors(i, :), ...
                  'LineWidth', 2.5, 'DisplayName', '领导者无人机');
    else
        h = plot3(x_traj, y_traj, z_traj, 'Color', colors(i, :), ...
                  'LineWidth', 1.5, 'DisplayName', sprintf('跟随者无人机_%d', i-1));
    end
    h_3d = [h_3d, h];
end

% 添加编队形状可视化
time_points = [10, 20, 30, 40, 50]; % 选择的时间点
formation_colors = [0.7, 0.7, 0.7]; % 编队连线颜色（灰色）

for tp_idx = 1:length(time_points)
    t_idx = find(abs(t_span - time_points(tp_idx)) < 0.1, 1);
    if ~isempty(t_idx)
        % 获取该时刻所有无人机的位置
        leader_pos = x_history(1:3, 1, t_idx);
        follower_positions = x_history(1:3, 2:end, t_idx);
        
        % 绘制编队连线（从领导者到每个跟随者）
        for i = 1:size(follower_positions, 2)
            plot3([leader_pos(1), follower_positions(1, i)], ...
                  [leader_pos(2), follower_positions(2, i)], ...
                  [leader_pos(3), follower_positions(3, i)], ...
                  '--', 'Color', formation_colors, 'LineWidth', 0.8, ...
                  'HandleVisibility', 'off');
        end
        
        % 绘制跟随者之间的连线（形成矩形）
        if size(follower_positions, 2) == 4
            % 连接相邻的跟随者形成矩形
            connections = [1,2; 2,4; 4,3; 3,1]; % 矩形连接顺序
            for conn_idx = 1:size(connections, 1)
                i1 = connections(conn_idx, 1);
                i2 = connections(conn_idx, 2);
                plot3([follower_positions(1, i1), follower_positions(1, i2)], ...
                      [follower_positions(2, i1), follower_positions(2, i2)], ...
                      [follower_positions(3, i1), follower_positions(3, i2)], ...
                      '--', 'Color', formation_colors, 'LineWidth', 0.8, ...
                      'HandleVisibility', 'off');
            end
        end
        
        % 在该时刻标记无人机位置
        scatter3(leader_pos(1), leader_pos(2), leader_pos(3), ...
                60, 'k', 'filled', 'MarkerEdgeColor', 'w', ...
                'HandleVisibility', 'off');
        for i = 1:size(follower_positions, 2)
            scatter3(follower_positions(1, i), follower_positions(2, i), follower_positions(3, i), ...
                    40, colors(i+1, :), 'filled', 'MarkerEdgeColor', 'w', ...
                    'HandleVisibility', 'off');
        end
    end
end

% 添加编队形状说明到图例
h_formation = plot3(NaN, NaN, NaN, '--', 'Color', formation_colors, 'LineWidth', 0.8, ...
                    'DisplayName', '编队连线');
h_3d = [h_3d, h_formation];

xlabel('x/m', 'FontSize', 12); 
ylabel('y/m', 'FontSize', 12); 
zlabel('z/m', 'FontSize', 12);
title('图6. 无人机编队的三维飞行轨迹及编队形状', 'FontSize', 14, 'FontWeight', 'bold');
legend(h_3d, 'Location', 'best', 'FontSize', 10);
view(3);
axis equal;
xlim([2, 14]); ylim([2, 14]); zlim([8, 20]);
set(gca, 'FontSize', 11);

fprintf('修复版可视化完成！\n');
fprintf('生成了3个主要图表:\n');
fprintf('1. 位置跟踪误差图\n');
fprintf('2. 速度跟踪误差图\n');
fprintf('3. 3D飞行轨迹图\n');

end
